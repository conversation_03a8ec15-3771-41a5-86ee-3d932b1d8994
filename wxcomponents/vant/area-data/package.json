{"name": "@vant/area-data", "version": "1.3.2", "description": "Vant 省市区数据", "main": "dist/index.cjs.js", "module": "dist/index.esm.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.esm.mjs", "require": "./dist/index.cjs.js"}}, "files": ["dist"], "scripts": {"clean": "rimraf ./dist", "dev": "node ./build.js -w", "build:types": "tsc -p ./tsconfig.json --emitDeclarationOnly", "build:bundle": "node ./build.js", "build": "pnpm clean && pnpm build:bundle && pnpm build:types", "release": "pnpm build && release-it", "prepare": "pnpm build"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "repository": {"type": "git", "url": "https://github.com/vant-ui/vant.git", "directory": "packages/vant-area-data"}, "bugs": "https://github.com/vant-ui/vant/issues", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"esbuild": "^0.14.29", "release-it": "^15.1.1", "typescript": "^4.7.4"}, "release-it": {"git": {"tag": false, "commitMessage": "release: @vant/area-data ${version}"}}}