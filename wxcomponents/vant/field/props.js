"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.commonProps = {
    value: {
        type: String,
        observer: function (value) {
            if (value !== this.value) {
                this.setData({ innerValue: value });
                this.value = value;
            }
        }
    },
    placeholder: String,
    placeholderStyle: String,
    placeholderClass: String,
    disabled: Boolean,
    maxlength: {
        type: Number,
        value: -1
    },
    cursorSpacing: {
        type: Number,
        value: 50
    },
    autoFocus: <PERSON>olean,
    focus: <PERSON>olean,
    cursor: {
        type: Number,
        value: -1
    },
    selectionStart: {
        type: Number,
        value: -1
    },
    selectionEnd: {
        type: Number,
        value: -1
    },
    adjustPosition: {
        type: Boolean,
        value: true
    },
    holdKeyboard: Boolean
};
exports.inputProps = {
    type: {
        type: String,
        value: 'text'
    },
    password: <PERSON><PERSON><PERSON>,
    confirmType: String,
    confirmHold: Boolean
};
exports.textareaProps = {
    autoHeight: <PERSON>olean,
    fixed: <PERSON><PERSON>an,
    showConfirmBar: {
        type: Boolean,
        value: true
    },
    disableDefaultPadding: {
        type: Boolean,
        value: true
    },
};
