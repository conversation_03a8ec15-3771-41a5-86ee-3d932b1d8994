/* eslint-disable */
var utils = require('../wxs/utils.wxs');

function inputStyle(autosize) {
  if (autosize && autosize.constructor === 'Object') {
    var style = '';
    if (autosize.minHeight) {
      style += 'min-height:' + utils.addUnit(autosize.minHeight) + ';';
    }
    if (autosize.maxHeight) {
      style += 'max-height:' + utils.addUnit(autosize.maxHeight) + ';';
    }
    return style;
  }

  return '';
}

module.exports = {
  inputStyle: inputStyle
};
