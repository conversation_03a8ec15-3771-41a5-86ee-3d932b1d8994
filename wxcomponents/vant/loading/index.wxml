<wxs src="../wxs/utils.wxs" module="utils" />

<view class="custom-class van-loading {{ vertical ? 'van-loading--vertical' : '' }}">
  <view
    class="van-loading__spinner van-loading__spinner--{{ type }}"
    style="color: {{ color }}; width: {{ utils.addUnit(size) }}; height: {{ utils.addUnit(size) }}"
  >
    <view
      wx:if="{{ type === 'spinner' }}"
      wx:for="{{ array12 }}"
      wx:key="index"
      class="van-loading__dot"
    />
  </view>
  <view class="van-loading__text" style="font-size: {{ utils.addUnit(textSize) }};">
    <slot />
  </view>
</view>
