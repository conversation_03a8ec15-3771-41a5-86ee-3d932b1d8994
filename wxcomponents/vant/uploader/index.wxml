<wxs src="../wxs/utils.wxs" module="utils" />

<view class="van-uploader">
  <view class="van-uploader__wrapper">
    <!-- 预览样式 -->
    <view
      wx:if="{{ previewImage }}"
      wx:for="{{ lists  }}"
      wx:key="index"
      class="van-uploader__preview"
    >
      <image
        wx:if="{{ item.isImage }}"
        mode="{{ imageFit }}"
        src="{{ item.url || item.path }}"
        alt="{{ item.name || ('图片' + index) }}"
        class="van-uploader__preview-image"
        style="width: {{ utils.addUnit(previewSize) }}; height: {{ utils.addUnit(previewSize) }};"
        data-index="{{ index }}"
        bind:tap="onPreviewImage"
      />
      <view
        wx:else
        class="van-uploader__file"
        style="width: {{ utils.addUnit(previewSize) }}; height: {{ utils.addUnit(previewSize) }};"
      >
        <van-icon name="description" class="van-uploader__file-icon" />
        <view class="van-uploader__file-name van-ellipsis">{{ item.name || item.url || item.path }}</view>
      </view>
      <view
        wx:if="{{ item.status === 'uploading' || item.status === 'failed' }}"
        class="van-uploader__mask"
      >
       <van-icon wx:if="{{ item.status === 'failed' }}" name="warning-o" class="van-uploader__mask-icon" />
       <van-loading wx:else class="van-uploader__loading"  />
       <text wx:if="{{ item.message }}" class="van-uploader__upload-text">{{ item.message }}</text>
      </view>
      <van-icon
        wx:if="{{ deletable }}"
        name="clear"
        class="van-uploader__preview-delete"
        data-index="{{ index }}"
        bind:tap="deleteItem"
      />
    </view>

    <!-- 上传样式 -->
    <block wx:if="{{ isInCount }}">
      <view class="van-uploader__slot" bind:tap="startUpload">
        <slot />
      </view>

      <!-- 默认上传样式 -->
      <view
        wx:if="{{ showUpload }}"
        class="van-uploader__upload {{ disabled ? 'van-uploader__upload--disabled': ''}}"
        style="width: {{ utils.addUnit(previewSize) }}; height: {{ utils.addUnit(previewSize) }};"
        bind:tap="startUpload"
      >
        <van-icon name="{{ uploadIcon }}" class="van-uploader__upload-icon" />
        <text wx:if="{{ uploadText }}" class="van-uploader__upload-text">{{ uploadText }}</text>
      </view>
    </block>
  </view>
</view>
