<template name="toolbar">
  <view
    wx:if="{{ showToolbar }}"
    class="van-picker__toolbar van-hairline--top-bottom toolbar-class"
  >
    <view
      class="van-picker__cancel"
      hover-class="van-picker__cancel--hover"
      hover-stay-time="70"
      data-type="cancel"
      bindtap="emit"
    >
      {{ cancelButtonText }}
    </view>
    <view wx:if="{{ title }}" class="van-picker__title van-ellipsis">{{
      title
    }}</view>
    <view
      class="van-picker__confirm"
      hover-class="van-picker__confirm--hover"
      hover-stay-time="70"
      data-type="confirm"
      bindtap="emit"
    >
      {{ confirmButtonText }}
    </view>
  </view>
</template>
