// 字体颜色
.col-gray-800 {
  color: #000000;
}

.col-gray-700 {
  color: #202532;
}

.col-gray-600 {
  color: #50545E;
}

.col-gray-500 {
  color: #838791;
}

.col-gray-400 {
  color: #BEC0C5;
}

.col-gray-300 {
  color: #D9DADD;
}

.col-gray-200 {
  color: #EAEAEB;
}

.col-gray-100 {
  color: #F6F6F6;
}

.col-green-100 {
  color: #008556;
}

.col-red-100 {
  color: #E83F4E;
}

//主题色/白色背景
.bg-white-800 {
  background-color: rgba(255, 255, 255, 1);
}

.bg-white-700 {
  background-color: rgba(255, 255, 255, 0.88);
}

.bg-white-600 {
  background-color: rgba(255, 255, 255, 0.72);
}

.bg-white-500 {
  background-color: rgba(255, 255, 255, 0.64);
}

.bg-white-400 {
  background-color: rgba(255, 255, 255, 0.48);
}

.bg-white-300 {
  background-color: rgba(255, 255, 255, 0.32);
}

.bg-white-200 {
  background-color: rgba(255, 255, 255, 0.24);
}

.bg-white-100 {
  background-color: rgba(255, 255, 255, 0);
}

//主题色/背景
.bg-primary-800 {
  background-color: #051773;
}

.bg-primary-700 {
  background-color: #0E2A99;
}

.bg-primary-600 {
  background-color: #1B41BF;
}

.bg-primary-500 {
  background-color: #2C5DE5;
}

.bg-primary-400 {
  background-color: #5584F2;
}

.bg-primary-300 {
  background-color: #82ACFF;
}

.bg-primary-200 {
  background-color: #ABCAFF;
}

.bg-primary-100 {
  background-color: #EDF4FF;
}

// 成功色
.bg-success {
  background-color: #008556;
}

// 错误色
.bg-error {
  background-color: #E83F4E;
}

// 警告色
.bg-warning {
  background-color: #EE843E;
}

// 链接色
.col-link {
  color: #2C5DE5;
}
