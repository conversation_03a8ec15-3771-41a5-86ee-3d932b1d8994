@font-face {
  font-family: "iconfont"; /* Project id 3024535 */
  src: url('//at.alicdn.com/t/font_3024535_yyyh43jesw.eot?t=1640765684009'); /* IE9 */
  src: url('//at.alicdn.com/t/font_3024535_yyyh43jesw.eot?t=1640765684009#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('//at.alicdn.com/t/font_3024535_yyyh43jesw.woff2?t=1640765684009') format('woff2'),
       url('//at.alicdn.com/t/font_3024535_yyyh43jesw.woff?t=1640765684009') format('woff'),
       url('//at.alicdn.com/t/font_3024535_yyyh43jesw.ttf?t=1640765684009') format('truetype'),
       url('//at.alicdn.com/t/font_3024535_yyyh43jesw.svg?t=1640765684009#iconfont') format('svg');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-search-line1:before {
  content: "\e61b";
}

.icon-a-LeftIcon:before {
  content: "\e619";
}

.icon-arrow-left-s-line:before {
  content: "\e618";
}

.icon-loader-2-fill:before {
  content: "\e616";
}

.icon-arrow-down-line:before {
  content: "\e615";
}

.icon-seedling-fill:before {
  content: "\e614";
}

.icon-refresh-fill:before {
  content: "\e613";
}

.icon-timer-fill:before {
  content: "\e612";
}

.icon-phone-fill:before {
  content: "\e611";
}

.icon-close-circle-fill:before {
  content: "\e610";
}

.icon-arrow-up-line:before {
  content: "\e60f";
}

.icon-calendar-todo-fill:before {
  content: "\e60e";
}

.icon-search-line:before {
  content: "\e60d";
}
