# 日历状态标识功能说明

## 功能概述

在 `pages/flightPlan/index.vue` 页面的 van-calendar 日历组件中添加了三个状态标识功能，可以在每个日期下方显示红色、黄色、绿色三个小圆点，用于表示不同的状态。

## 实现原理

1. **数据存储**：使用 `dateStatusMap` 对象存储每个日期的状态信息
2. **显示逻辑**：通过 van-calendar 的 `formatter` 函数自定义日期显示内容
3. **样式控制**：使用 CSS 控制圆点的颜色和布局

## 状态说明

- **红色圆点**：表示状态1（例如：紧急任务、重要事项）
- **黄色圆点**：表示状态2（例如：待处理任务、提醒事项）
- **绿色圆点**：表示状态3（例如：已完成任务、正常状态）

## 数据配置

### API 数据映射

在 `getDateDots` 方法中，需要根据后端 API 返回的数据结构配置状态映射：

```javascript
// 示例1：如果API返回布尔值字段
this.dateStatusMap[dateStr] = {
  status1: item.status1 || false, // 红色状态
  status2: item.status2 || false, // 黄色状态  
  status3: item.status3 || false, // 绿色状态
}

// 示例2：如果API返回其他字段名
this.dateStatusMap[dateStr] = {
  status1: item.hasUrgentTask || false,    // 红色：紧急任务
  status2: item.hasPendingTask || false,   // 黄色：待处理任务  
  status3: item.hasCompletedTask || false, // 绿色：已完成任务
}

// 示例3：如果API返回数字状态码
this.dateStatusMap[dateStr] = {
  status1: item.taskStatus === 1 || item.taskStatus === 4, // 红色
  status2: item.taskStatus === 2 || item.taskStatus === 5, // 黄色
  status3: item.taskStatus === 3 || item.taskStatus === 6, // 绿色
}
```

### 测试数据

开发阶段可以使用以下测试数据：

```javascript
// 添加测试数据（开发阶段使用）
if (process.env.NODE_ENV === 'development') {
  const today = dayjs().format('YYYY-MM-DD')
  const tomorrow = dayjs().add(1, 'day').format('YYYY-MM-DD')
  const dayAfter = dayjs().add(2, 'day').format('YYYY-MM-DD')
  
  this.dateStatusMap[today] = { status1: true, status2: false, status3: true }
  this.dateStatusMap[tomorrow] = { status1: false, status2: true, status3: false }
  this.dateStatusMap[dayAfter] = { status1: true, status2: true, status3: true }
}
```

## 样式自定义

### 修改圆点颜色

在 CSS 中修改以下类的颜色值：

```scss
.status-dot.red {
  color: #ff4444; // 红色圆点颜色
}

.status-dot.yellow {
  color: #ffaa00; // 黄色圆点颜色
}

.status-dot.green {
  color: #00aa44; // 绿色圆点颜色
}
```

### 修改圆点大小

调整 `.status-dot` 的 `font-size` 属性：

```scss
.status-dot {
  font-size: 6px; // 调整圆点大小
}
```

### 修改圆点间距

调整 `.status-indicators` 的 `gap` 属性：

```scss
.status-indicators {
  gap: 1px; // 调整圆点间距
}
```

## 使用方法

1. **确保 API 数据正确**：检查 `getFlightPlansDateDots` API 返回的数据结构
2. **配置状态映射**：在 `getDateDots` 方法中正确映射 API 数据到状态
3. **测试显示效果**：使用测试数据验证圆点显示是否正确
4. **调整样式**：根据设计需求调整圆点颜色、大小等样式

## 注意事项

1. **兼容性**：使用了简单的字符和CSS，兼容性良好
2. **性能**：状态数据存储在内存中，切换月份时会重新获取
3. **扩展性**：可以轻松添加更多状态或修改现有状态逻辑
4. **维护性**：代码结构清晰，易于维护和修改

## 故障排除

### 圆点不显示
1. 检查 `dateStatusMap` 是否有对应日期的数据
2. 检查 API 数据是否正确返回
3. 检查状态映射逻辑是否正确

### 圆点颜色不正确
1. 检查 CSS 样式是否正确加载
2. 检查状态值是否为 true
3. 检查是否有其他样式覆盖

### 圆点位置不正确
1. 检查 `.van-calendar__day` 的最小高度设置
2. 检查 `.status-indicators` 的布局样式
3. 调整 `margin-top` 等间距属性
