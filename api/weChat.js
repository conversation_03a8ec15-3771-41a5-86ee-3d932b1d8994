import api from './index.js'
//通过电话获取验证码
export const getVerificationCodeByPhoneNumber = (params) =>
  api._get('/wechat/getVerificationCodeByPhoneNumber/:phoneNumber', {
    params,
  })
//验证码登录
// export const login = (body) => api._post('/wechat/login', {
// 	body
// })
//退出登录
export const loginOut = (body) =>
  api._post('/wechat/loginOut', {
    body,
  })

//任务次数、时间
export const getFlyTimes = (query) =>
  api._get('/wechat/pilot/fly/details', {
    query,
  })
//任务列表信息
export const getFlyLists = (body) =>
  api._post('/wechat/flight/history/list', {
    body,
  })
// 任务详情
export const getFlyDetail = (params) =>
  api._get('/wechat/getDetailFlight/:flightSortiesId', {
    params,
  })
// 获取各种航班状态航班总数
export const getFlyNums = (query) =>
  api._get('/wechat/flight/history/status/count', {
    query,
  })

// 任务详情——记录
export const getDetailFlightLog = (params) =>
  api._get('/wechat/getDetailFlightLog/:flightSortiesId', {
    params,
  })

//我的--记录
export const getMyFlightLog = (params) =>
  api._get('/wechat/getDetailFlightLog', {
    params,
  })

//提交答题
export const uploadAnswer = (body) =>
  api._post('/wechat/uploadAnswer', {
    body,
  })
//获取首页信息
export const getHomeInfo = (params) =>
  api._get('/wechat/getFlights/:userId/:status/:pageNum/:pageSize', {
    params,
  })

// 通过航班班次id获取详细航班信息接口
export const getFlightDetailById = (params) =>
  api._get('/wechat/getDetailFlight/:flightSortiesId', {
    params,
  })

// 随机获取题库指定题数量接口
export const getRandomSubject = (params) =>
  api._get('/wechat/getQuestionBank/:flightSortiesId/:questionNumber', {
    params,
  })

// 获取航线资料、气象资料、电子舱单、放行单等各种格式数据接口
export const getHomeItemData = (query) =>
  api._get('/wechat/getData', {
    query,
  })

// 确认航线资料阅读完成接口
export const getRouteInfoReadSure = (body) =>
  api._put('/wechat/routeInfoReadSure', {
    body,
  })

// 气象资料、电子舱单、放行单审核确认接口
export const getVerifySure = (body) =>
  api._put('/wechat/verifySure', {
    body,
  })

//录入时刻接口
export const uploadFlyTime = (body) =>
  api._post('/wechat/uploadFlyTime', {
    body,
  })
// 飞行完成、取消
export const flightStatus = (body) =>
  api._post('/wechat/flightSorties/update', {
    body,
  })

// 获取文件目录树级接口
export const getTreeList = (params) =>
  api._get('/wechat/treeListDir', {
    params,
  })

// 获取目录文件资料接口
export const getTreeNode = (params) =>
  api._get('/wechat/treeListDir/:parentId', {
    params,
  })

// 获取目录文件模糊查询
export const queryTree = (body) =>
  api._post('/wechat/file/query/fuzzy', {
    body,
  })

// 获取基地查询
export const areaList = (body) =>
  api._post2('/tw/area/list', {
    body,
  })

// 扫码
export const scanVerifyTicket = (body) =>
  api._post2('/tw/passenger/scanVerifyTicket', {
    body,
  })

// 乘客列表
export const passengerList = (body) =>
  api._post2('/tw/passenger/list', {
    body,
  })

export const getFlightPlans = (params) =>
  api._get('/wechat/getFlightPlanList/:userId/:pageNum/:pageSize', {
    params,
  })
// export const getFlightPlansDate = (params) => api._get('/wechat/getFlightPlanList/:userId/:pageNum/:pageSize/:flightDate', {
// 	params
// })

// export const getFlightPlanSingle = (params) => api._get('/wechat/getFlightPlan/:flightplanId', {
// 	params
// })
export const getFlightPlanSingleCheck = (params) =>
  api._get('/wechat/prepareForFlightplan/:flightplanId/:type', {
    params,
  })
export const getFlightDetail = (params) =>
  api._get('/wechat/flightSorties/list/:flightplanId', {
    params,
  })
export const getMessageList = (body) =>
  api._post('/wechat/message/list', {
    body,
  })
// export const getFlight = (params) => api._get('/wechat/flightDetail/getFlightDetail/:flightSortiesId', {
// 	params
// })

export const prepareForFlightplan = (body) =>
  api._post('/wechat/prepareForFlightplan/add', {
    body,
  })
// export const prepareForFlightplanUpdates = (body) => api._post('/wechat/prepareForFlightplan/add', {
// 	body
// })
export const updateFlightPlan = (body) =>
  api._post('/wechat/updateFlightPlan', {
    body,
  })

export const captainDirectPreparation = (body) =>
  api._post('/wechat/captainDirectPreparation/add', {
    body,
  })
export const maintenanceDirectPreparation = (body) =>
  api._post('/wechat/maintenanceDirectPreparation/add', {
    body,
  })
export const ocDirectPreparation = (body) =>
  api._post('/wechat/ocDirectPreparation/add', {
    body,
  })
export const flightSortiesSyn = (body) =>
  api._post('/wechat/flightSorties/add', {
    body,
  })
// export const flightSortiesSynnew = (body) => api._post('/wechat/flightSorties/update', {
// 	body
// })
export const flightSortiesUpdateSyn = (body) =>
  api._post('/wechat/flightSorties/update', {
    body,
  })
export const flightSortiesSynBook = (body) =>
  api._post('/wechat/flightSorties/update', {
    body,
  })
export const emergency = (body) =>
  api._post('/wechat/flightSorties/update', {
    body,
  })
export const emergencyList = (params) =>
  api._get('/wechat/getflightSorties/:flightSortiesId', {
    params,
  })
export const prepareForFlightplanAll = (body) =>
  api._post('/wechat/prepareForFlightplan/list', {
    body,
  })

export const captainDirectPreparationOnly = (body) =>
  api._post('/wechat/captainDirectPreparation/list', {
    body,
  })

export const copilotDirectPreparationOnly = (body) =>
  api._post('/wechat/copilotDirectPreparation/list', {
    body,
  })
export const maintenanceDirectPreparationOnly = (body) =>
  api._post('/wechat/maintenanceDirectPreparation/list', {
    body,
  })
export const ocDirectPreparationOnly = (body) =>
  api._post('/wechat/ocDirectPreparation/list', {
    body,
  })
export const uploadAnswerOnly = (body) =>
  api._post('/wechat/QBank/uploadAnswer', {
    body,
  })
export const getQuestion = (params) =>
  api._get('/wechat/QBank/getQuestion/:aircraftStyle', {
    params,
  })
export const getAircraftStyle = (params) =>
  api._get('/wechat/Aircraft/getAircraftStyle', {
    params,
  })
export const updateUser = (body) =>
  api._post('/wechat/SysUser/updateUser', {
    body,
  })
export const getUser = (params) =>
  api._get('/wechat/SysUser/getUser/:userId', {
    params,
  })
export const getUserAprrol = (body) =>
  api._post('/wechat/wxUser/getUserByPlanId', {
    body,
  })

// 改版后新接口
// export const getStudyFile = (body) => api._post('/wechat/learningFile/list', {
// 	body
// })
export const getStudyFileDir = (body) =>
  api._post('/wechat/learningFile/fileDir', {
    body,
  })
export const getStudyFile = (body) =>
  api._post('/wechat/learningFile/fileList', {
    body,
  })

// 学习文件
export const addStudyFile = (body) =>
  api._post('/wechat/prepareForFlightplan/addFlightStudy', {
    body,
  })
export const prepareForFlightplanUpdates = (body) =>
  api._post('/wechat/captainDirectPreparation/add', {
    body,
  })
export const copilotDirectPreparation = (body) =>
  api._post('/wechat/copilotDirectPreparation/add', {
    body,
  })
export const safecopilotDirectPreparation = (body) =>
  api._post('/wechat/safetyDirectPreparation/add', {
    body,
  })
export const addMaintenancePrepare = (body) =>
  api._post('/wechat/prepareForFlightplan/addMaintenancePrepare', {
    body,
  })
export const getFlightPlansDate = (body) =>
  api._post('/wechat/flightPlan/queryFlightPlan', {
    body,
  })
export const getFlightPlansDateDots = (body) =>
  api._post('/wechat/flightPlan/dateMark', {
    body,
  })
export const safetyDirectPreparation = (body) =>
  api._post('/wechat/safetyDirectPreparation/list', {
    body,
  })
export const getFlightPlanSingle = (body) =>
  api._post('/wechat/flightPlan/queryFlightPlanDetail', {
    body,
  })
export const flightCommentary = (body) =>
  api._post('/wechat/flightCommentary/add', {
    body,
  })
export const addFlyMoment = (body) =>
  api._post('/wechat/dataArchive/addFlyMoment', {
    body,
  })
export const dataArchive = (body) =>
  api._post('/wechat/dataArchive/selectFlyMoment', {
    body,
  })
export const confirmFlyMoment = (body) =>
  api._post('/wechat/dataArchive/confirmFlyMoment', {
    body,
  })
export const getFlight = (body) =>
  api._post('/wechat/flightPlan/queryFlightPlanDetail', {
    body,
  })
export const getFlightcancelFlight = (body) =>
  api._post('/wechat/flightPlan/cancelFlight', {
    body,
  })
export const getFlightflyEnd = (body) =>
  api._post('/wechat/flightPlan/flyEnd', {
    body,
  })

// 查询登录授权
export const authLogin = (body) =>
  api._post('/wechat/auth/login', {
    body,
  })

// 查询航司
export const allCompany = (body) =>
  api._post('/wechat/company/allCompany', {
    body,
  })
// 申请
export const addWxUser = (body) =>
  api._post('/wechat/auth/addWxUser', {
    body,
  })

//低空研学 接口
//查询航班 study
export const studyFlightPlanList = (body) =>
  api._post('/wechat/ocFlightPlanYth/list', {
    body,
  })

export const studyFlightPlanDetails = (body) =>
  api._post('/wechat/ocFlightPlanYth/details', {
    body,
  })

//查询旅客列表
export const studyPassengerList = (body) =>
  api._post('/wechat/ocPassengerYth/list', {
    body,
  })

//旅客列表 扫码登机
export const studyPassengerScanCodeToBoarding = (body) =>
  api._post('/wechat/ocPassengerYth/scanCodeToBoarding', {
    body,
  })

//旅客列表 手动登机
export const studyPassengerBoarding = (body) =>
  api._post('/wechat/ocPassengerYth/manualBoarding', {
    body,
  })
//旅客列表 取消登机
export const studyPassengerCancelBoarding = (body) =>
  api._post('/wechat/ocPassengerYth/cancelBoarding', {
    body,
  })

//体重更新
export const studyPassengerWeightUpdate = (body) =>
  api._post('/wechat/ocPassengerYth/updateWeight', {
    body,
  })

//获取低空研学订单
export const studyOrderList = (body) =>
  api._post('/wechat/study/order/query', {
    body,
  })
//创建低空研学订单
export const studyOrderCreate = (body) =>
  api._post('/wechat/study/order/create', {
    body,
  })
//低空研学 下拉菜单 代理人列表
export const studyOrderAgentList = (body) =>
  api._post('/wechat/study/order/agentList', {
    body,
  })
//低空研学 下拉菜单 机场人列表
export const studyOrderAirportList = (body) =>
  api._post('/wechat/study/order/airportList', {
    body,
  })

//低空研学  旅客列表
export const studyOrderPassengerList = (body) =>
  api._post('/wechat/study/order/passengerList', {
    body,
  })

//低空研学  订单查询 根据订单号
export const studyOrderByOrderNo = (body) =>
  api._post('/wechat/study/order/queryByOrderNo', {
    body,
  })

//低空研学  添加旅客
export const studyOrderPassengerAddUrl =
  api.baseUrl + '/wechat/study/order/addPassenger'

export const baseUrl = api.baseUrl
// export const studyOrderPassengerAdd = (body) => api._post('/wechat/study/order/addPassenger', {
// 	body
// })

//低空研学  删除旅客
export const studyOrderPassengerDel = (body) =>
  api._post('/wechat/study/order/delPassenger', {
    body,
  })

//架次管理 查询未排班的旅客
export const sortieUngroupedList = (body) =>
  api._post('/wechat/ocSortieYth/ungrouped/list', {
    body,
  })

//架次管理  查询架次
export const sortieDetailsList = (body) =>
  api._post('/wechat/ocSortieYth/details', {
    body,
  })

//架次管理  新增或修改架次
export const sortieSubmit = (body) =>
  api._post('/wechat/ocSortieYth/submit', {
    body,
  })

//架次管理  删除架次
export const sortieDelete = (body) =>
  api._post('/wechat/ocSortieYth/delete', {
    body,
  })
