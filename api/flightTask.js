import api from './index.js'

//获取机型
export const getAircraftStyle = (params) =>
  api._post('/wechat/Aircraft/queryAll', {
    params,
  })
//查询所有航线信息
export const getRouteAll = (body) =>
  api._post('/wechat/route/queryAll', {
    body,
  })
//查询所有空域信息
export const getAirspaceAll = (body) =>
  api._post('/wechat/airspace/queryAll', {
    body,
  })
//查询所有机场信息
export const getAirportAll = (body) =>
  api._post('/wechat/airport/queryAll', {
    body,
  })
//查询任务类型
export const getFlightPurpose = (body) =>
  api._post('/wechat/flightPurpose/queryAll', {
    body,
  })
//新增预填飞行任务书
export const addFlightTask = (body) =>
  api._post('/wechat/flightTaskConfig/add', {
    body,
  })
//通过ID查询单条飞行任务书
export const getFlightTaskById = (id, body) =>
  api._post(`/wechat/flightTaskConfig/getOne/${id}`, {
    body,
  })
//解析文本
export const parseTextFlightTask = (body) =>
  api._post('/wechat/flightTaskConfig/parseText', {
    body,
  })
//任务书内容确认
export const planConfirmFlightTask = (body) =>
  api._post('/wechat/flightTaskConfig/planConfirm', {
    body,
  })
//任务书内容确认2
export const planConfirmFlightTask2 = (body) =>
  api._post('/wechat/flightTaskConfig/planConfirm2', {
    body,
  })
//任务书接收内容确认
export const receiveConfirmFlightTask = (body) =>
  api._post('/wechat/flightTaskConfig/receiveConfirm', {
    body,
  })
//修改机组人员
export const updateCrewFlightTask = (body) =>
  api._post('/wechat/flightTaskConfig/updateCrew', {
    body,
  })
//查询机组人员
export const queryCrewInfo = (body) =>
  api._post('/wechat/flightTaskConfig/queryCrewInfo', {
    body,
  })
//查询预填飞行任务书详情
export const queryFlightTaskConfigDetail = (body) =>
  api._post('/wechat/flightTaskConfig/queryFlightTaskConfigDetail', {
    body,
  })
//查询预填飞行任务书列表
export const queryFlightTaskConfig = (body) =>
  api._post('/wechat/flightTaskConfig/queryFlightTaskConfig', {
    body,
  })
//查询预填飞行任务书列表
export const listUserByRole = (body) =>
  api._post('/wechat/wxUser/listUserByRole', {
    body,
  })
//查询低空套餐产品列表
export const getTravelPackage = (body) =>
  api._post('/wechat/flightTaskQueryOtherSys/getTravelPackage', {
    query: body,
  })
//查询低空产品列表
export const getTravelProduct = (body) =>
  api._post('/wechat/flightTaskQueryOtherSys/getTravelProduct', {
    query: body,
  })
//通过日期短途订单列表
export const getTravelBookOrder = (body) =>
  api._post('/wechat/flightTaskQueryOtherSys/getTravelBookOrder', {
    query: body,
  })
//通过订单信息返回预创建参数
export const getFlightTaskSetting = (body) =>
  api._post('/wechat/flightTaskQueryOtherSys/getFlightTaskSetting', {
    body,
  })
//通过日期和基地三字码查询低空订单列表
export const getTravelBookOrder2 = (body) =>
  api._post('/wechat/flightTaskQueryOtherSys/getTravelBookOrder2', {
    query: body,
  })
//分配机组人员
export const assignCrew = (body) =>
  api._post('/wechat/flightPlan/assignCrew', {
    body,
  })
//查询机组人员
export const queryCrew = (body) =>
  api._post('/wechat/flightPlan/queryCrew', {
    body,
  })
//航班计划角标
export const queryPlannedNumber = (body) =>
  api._post('/wechat/flightPlan/queryPlannedNumber', {
    body,
  })
//航班计划角标
export const queryNumberToBeConfirmed = (body) =>
  api._post('/wechat/flightTaskConfig/queryNumberToBeConfirmed', {
    body,
  })
//删除飞行任务书
export const deleteFlightTask = (flightTaskConfigId) =>
  api._post(`/wechat/flightTaskConfig/delete/${flightTaskConfigId}`, {})
//修改删除架次
export const updateTaskSorties = (body) =>
  api._post(`/wechat/flightTaskConfig/updateTaskSorties`, { body })
