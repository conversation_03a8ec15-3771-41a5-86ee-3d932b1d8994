export const typeIs = (value) => (target) => {
  if (target === null) return value === 'null';
  if (target === undefined) return value === 'undefined';
  return Object.prototype.toString.call(target).slice(8, -1) === value;
};
export const isString = typeIs('String');
export const isObject = typeIs('Object');
export const isPromise = typeIs('Promise');
export const isFunction = typeIs('Function');
export const indentify = (args) => args;
export const toArr = (target) =>
  Array.isArray(target) ? target : [target];

export const has = (obj, key) => {
  return Boolean(obj[key]);
};

export const firstToUpper = (str) =>
  str.toLowerCase().replace(/( |^)[a-z]/g, L => L.toUpperCase());

export const assign = Object.assign;
export const keys = Object.keys;
