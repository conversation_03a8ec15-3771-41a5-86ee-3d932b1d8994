import { compile } from "./path-to-regexp";
import compose from "./compose";
import queryStringify from "./queryString";
import applyMiddleware from "./applyMiddleware";
import { isFunction, indentify, toArr, assign } from "./utils";

const MONKOS_header = {
  "X-Requested-With": "XMLHttpRequest",
  "Content-Type": "application/json; charset=utf-8"
};

const includesBodyMethods = ["POST", "PUT", "PATCH"];


class Request {
  constructor(options) {
    // 抽象请求参数适配
    this.requestAdapter = options.requestAdapter || indentify;
    // 抽象请求方法
    this.httpRequest = options.httpRequest;
    // 路径请求参数序列化
    this.paramsSerializer = options.paramsSerializer || compile;
    // 查询参数序列化
    this.querySerializer = options.querySerializer || queryStringify;
    /**
     * 请求中间件
     * @param {Object} ctx 数据上下文
     * @param {AsyncFunction} next 下一个中间件
     */
    this.requestMiddleware = async (ctx, next) => {
      try {
        // 根据抽象函数，获取请求参数
        const option = this.requestAdapter(ctx, this.option.header);
        // 最终执行请求，并挂载至指定上下文
        ctx.response = await this.httpRequest(option);
      } catch (err) {
        ctx.error = err;
      }

      await next();
    };
    // 中间件

    this.middlewares = [this.requestMiddleware];
    // 请求参数全局配置
    this.option = {
      timeout: options.timeout || 5000,
      baseURL: options.baseURL || "",
      header: assign({}, MONKOS_header, options.header),
      method: "GET",
      params: {},
      query: {}
      // body: {},
    };
    this.originOption = JSON.parse(JSON.stringify(this.option));
  }

  /**
   * 反序列化请求URL
   * @param {Object} config URL配置
   * @param {Object} option 序列化参数
   */
  stringifyURL(config, option) {
    const { params, query, url } = config;
    const toPath = this.paramsSerializer(url);

    const queryString = this.querySerializer(assign({}, option.query, query));
    return (
      option.baseURL +
      toPath(assign({}, option.params, params)) +
      (queryString ? "?" + queryString : "")
    );
  }

  /**
   * 路径请求参数
   * @param {Object} params
   */
  param(params) {
    assign(this.option.params, params);
    return this;
  }

  /**
   * 请求查询参数
   * @param {Object} q
   */
  query(q) {
    assign(this.option.query, q);
    return this;
  }

  stringifyBody(config) {
    const { body = {} } = config;

    return body;
  }

  /**
   * 带参，发起HTTP-request请求
   * @param {Object} config
   */
  send(config) {
    const { use = [], ...restConfig } = config;
    // 在中间件头部，批量插入
    this.patchUse(use);

    // 重置请求参数
    const _options = assign({}, this.option);
    assign(this.option, this.originOption);
    // 构建请求参数
    const context = assign(
      {
        header: _options.header
      },
      {
        ...restConfig,
        url: this.stringifyURL(restConfig, _options)
      }
    );
    // body内容需特定请求方式：POST、PUT、PATCH
    if (includesBodyMethods.includes(String(restConfig.method).toUpperCase())) {
      assign(context, {
        body: this.stringifyBody(restConfig)
      });
    }

    /**
     * 顺序执行中间件
     */
    const fn = compose(this.middlewares);

    return applyMiddleware(context, fn).then(() => context);
  }

  /**
   * 插入中间件
   * @param {AsyncFunction} fn
   */
  use(fn) {
    if (!isFunction(fn)) {
      throw new TypeError("Middleware must be composed of functions!");
    }

    // 在核心请求前，插入自定义中间件
    const len = this.middlewares.length < 1 ? 1 : this.middlewares.length;
    this.middlewares.splice(len - 1, 0, fn);
    return this;
  }
  
  patchUse(fns = []) {
      const arr = toArr(fns);
      this.middlewares.unshift(...arr);
    }


  // public _get (method: string, instance): Promise<> {};
  // public _put = (method: string, instance) => Promise;
  // public _patch = (method: string, instance) => Promise;
  // public _delete = (method: string, instance) => Promise;
}

export default Request;
