<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">申请</text>
			</view>
		</view>
		<!-- 内容主体 -->
		<view class="content" :style="'height:calc(100vh - ' + navH + 'px)'" style="overflow: auto;">
			<view style="border-top:5px solid #eee">
				<view class="uni-form-item uni-column">
					<view class="title">唯一标识码:{{openId}}</view>
				</view>
				<view class="uni-form-item uni-column">
					<view class="title">用户名</view>
					<view class="uni-textarea">
						<input  v-model='form.userName'  placeholder="请填写用户名" placeholder-style='font-size:12px' />
					</view>
				</view>
				<view class="uni-form-item uni-column">
					<view class="title">手机号</view>
					<view class="uni-textarea">
						<input  v-model='form.phoneNumber' max-length='11'  placeholder="请填写手机号" placeholder-style='font-size:12px' />
					</view>
				</view>
				<view class="uni-form-item uni-column">
					<view class="title">航司或机场{{form.companyName == null ? '' : '：'+form.companyName}}</view>
					<view class="uni-textarea">
						<view style="width: 300rpx;">
						    <qiaoSelect ref="showObjRefs" phText='请选择航司或机场' :keyId="1" :dataList="orgArray" placeholder-style='font-size:12px'  showField="companyName" searchKey="companyName"
						        :showObj="showObj" :showBorder="false" :disabled="false" @change="selectChange" @input='inputChange'></qiaoSelect>
						</view>
					</view>
				</view>
				<view class="uni-form-item uni-column">
					<view class="title">备注</view>
					<view class="uni-textarea">
						<input  v-model='form.remark'  placeholder="请填写备注" placeholder-style='font-size:12px' />
					</view>
				</view>
			</view>
			<view class="login-container" style="margin-top: 10px;">
				<view class="login-in">
					<view class="weChat-in">
						<button style="background-color: #2979FF;color: #fff;" class="sys_btn" @click="codeLogin">
							提交
						</button>
					</view>
				</view>
			</view>
		</view>
		<!-- 整体背景放在最后 -->
		<view class="bg-main"><image class="background" src="../../static/images/background.png"></image></view>
	</view>
</template>

<script>
	import { allCompany,addWxUser } from '../../api/weChat.js';
	import qiaoSelect from '@/uni_modules/qiao-select/components/qiao-select/qiaoSelect.vue';
const App = getApp();
export default {
	components: {
	    qiaoSelect
	},
	data() {
		return {
			showObj:null,
			orgArray:[],
			navH: 0,
			openId:'',
			form:{
				phoneNumber:'',
				userName:''
				
			},
		};
	},
	onShow: function(options) {
		this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
		this.openId = uni.getStorageSync('openId')
		console.log(uni.getStorageSync('userInfo'))
		this.getData()
	},
	methods: {
		async codeLogin(){
			if(this.form.companyCode == ''){
				this.$refs.showObjRefs.clickClear()
				uni.showToast({
					title: '请在下拉框内选择航司！',
					icon: 'none'
				})
				return false
			}else if(this.form.phoneNumber.length=='' || this.form.phoneNumber.length!=11){
				uni.showToast({
					title: '请输入正确的手机号码！',
					icon: 'none'
				})
				return false
			}else if(this.form.userName == ''){
				uni.showToast({
					title: '请输入用户名！',
					icon: 'none'
				})
				return false
			}
			this.form.openId = this.openId
			
			try {
				const res = await addWxUser(this.form);
				uni.showToast({
					title: res.response.msg
				})
				setTimeout(()=>{
					uni.setStorageSync('userInfo', null);
					uni.navigateBack({
					    delta: 1,
					});
				},1500)
			} catch (e) {
				console.error(e)
			}
		},
		async getData(){
			try {
				const res = await allCompany();
				this.orgArray = res.response.data
				if(uni.getStorageSync('userInfo')){
					this.form = uni.getStorageSync('userInfo')
					this.openId = this.form.openId
				}
				
			} catch (e) {
				console.error(e)
			}
			
		},
		selectChange(e) {//返回选择的对象，如果输入框清空，返回null
			if (e) {
		        this.form.companyName = e.companyName
		        this.form.companyCode = e.companyCode
		        this.form.companyType = e.companyType
		    }
		},
		inputChange(e) {//返回搜索结果集合,一般用不到
			if(e.length == 0){
				this.form.companyName = ''
				this.form.companyCode = ''
				this.form.companyType = ''
			}
		},
		
		// 返回"我的"主页面
		backMyPage() {
			uni.navigateBack({
				 url: '/pages/home/<USER>'
			});
		},
	}
};
</script>

<style lang="less" >

.qiaoSelect input{
	font-size: 12px!important;
}

page {
	height: 100%;
}

.background {
	width: 100%;
	height: 100%;
	// position: fixed;
	background-size: 100% 100%;
	z-index: -1;
	position: absolute;
	top: 0px;
	bottom: 0px;
}

.nav {
	width: 100%;
	overflow: hidden;
	position: relative;
	top: 0;
	left: 0;
	z-index: 10;
}

.nav-title {
	width: 100%;
	height: inherit;
	position: relative;
	z-index: 10;
	font-family: SF Pro Text;
	font-style: normal;
	font-weight: 600;
	font-size: 16px;
	color: #000000;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 15px;

	.nav-title-text {
		// padding-top: 10px;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;

		&:first-child {
			font-weight: normal;
			position: absolute;
			left: 25px;
			font-size: 22px;
		}
	}
}

.uni-column{
	    background: #ffffff86;
		line-height: 30px;
		padding: 10px 20px;
		font-size: 13px;

}
.content{
	padding: 10px;
}
textarea{
	    border: 1px solid #efefef;
	    width: 100%;
	    padding: 10px;
	    box-sizing: border-box;
}
</style>
