<template>
	<view class="empty">

	</view>
</template>

<script>
	export default {
		data() {
			return {}
		},
		created() {
			console.log('App Launch')
			// token标志来判断
			let token = uni.getStorageSync('token');
			let userUrl = uni.getStorageSync('userUrl');
			console.log(token);
			if (!token) {
				userUrl = ''
				console.log('没有token')
				//跳到登录页面.relaunch可以打开任何界面
				uni.reLaunch({
					url: '/pages/login/login'
				})
				// uni.showModal({
				// 	title: '未登录/登录过期，请先登录',
				// 	content: '',
				// 	success: function(res) {
				// 		if (res.confirm) {
				// 			//跳到登录页面.relaunch可以打开任何界面
				// 			uni.reLaunch({
				// 				url: '/pages/login/login'
				// 			})
				// 			console.log('用户点击确定');
				// 		} else if (res.cancel) {
				// 			wx.navigateBack({
				// 				delta: 1
				// 			})
				// 		}
				// 	}
				// });
			} else {
				console.log('有token')
				//跳到首页,跳转tabbar界面,使用这个方法
				uni.switchTab({
					url: '/pages/home/<USER>'
				})
			}

		}
	}
</script>

<style acoped>
	.empty {
		width: 100%;
		height: 100vh;
		background-color: #fff;
	}
</style>
