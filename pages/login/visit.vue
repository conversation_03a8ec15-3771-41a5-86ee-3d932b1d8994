<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">欢迎</text>
			</view>
		</view>
		<!-- 内容主体 -->
		<view class="content" :style="'height:calc(100vh - ' + navH + 'px)'" style="overflow: auto;">
			<form @submit="submit" >
				
				<view class="" style="text-align: center;font-weight: 800;">
					<view class="">
						<img src="../../static/images/logos.png" alt="" />
					</view>
					运控通小程序欢迎您！
				</view>
			</form>
		</view>
		<!-- 整体背景放在最后 -->
		<view class="bg-main"><image class="background" src="../../static/images/background.png"></image></view>
	</view>
</template>

<script>
	import api from "../../api/index.js"
	import { getFlightDetailById,getVerifySure,getHomeItemData,flightSortiesSynBook} from '../../api/weChat.js';
	const FormData = require('../../utils/formData/formData.js')

const App = getApp();
export default {
	data() {
		return {
			
			navH: 0,
			timer: '',
			taskIndex: 1,
			inputValue: '',
			taskInfo: '',
			componentData: '',
			flightSortiesId:'',
			flightPlanId:'',
			imgUrl:'',
			state:'',
			userRole:'',
			form:{
				checkedPassengerLegacy:0,
				checkedItemDamage:0,
				completedCleaning:0,
			},
			currentTime:'',
			path:[],
		};
	},
	mounted() {
		console.log(this.currentTime)
		this.getCurrentTime()
		this.form.taxiStartTimeDate = this.currentTime
		this.form.departureTimeDate = this.currentTime
		this.form.landingTimeDate = this.currentTime
		this.form.shutdownTimeDate = this.currentTime
	},
	onLoad: function(options) {
		this.state = options.state;
		this.flightSortiesId = options.flightSortiesId;
		this.flightPlanId = options.flightplanId
		
		console.log(options)
		this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
		// this.userRole = uni.getStorageSync('userRole')
		this.userRole = 1
		
				
	},
	methods: {
		flyFit(param){
			this.form[param] = this.form[param] == 0 ? 1 : 0 
			this.$forceUpdate()
		},
		clearFiles(type){
			this.path = []
		},
		getCurrentTime() {
			var myDate = new Date();
			var y = myDate.getFullYear()
			var m = myDate.getMonth() + 1
			m = m < 10 ? '0' + m : m
			var d = myDate.getDate()
			d = d < 10 ? ('0' + d) : d
			var week = myDate.getDay()
			var x; 
			switch (week) {
				case 0:
					x = '周日';
					break;
				case 1:
					x = '周一';
					break;
				case 2:
					x = '周二';
					break;
				case 3:
					x = '周三';
					break;
				case 4:
					x = '周四';
					break;
				case 5:
					x = '周五';
					break;
				case 6:
					x = '周六';
					break;
			}
			this.currentTime = y + '-' + m + '-' + d
		},
		uploadFiles(type){
			let that = this
			uni.chooseImage({
				count: 5, //默认9
				sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
				// sourceType: ['album'], //从相册选择
				name:'',
				success: function (res) {
					res.tempFilePaths.map((i)=>{
						if(that.path.length > 4){
							uni.showToast({
								title: '最多上传5张图片！'
							})
							return false
						}
						that.path.push(i)
					})
					
				}
			});
		},
		bindTimeChange(e,id){
			console.log(e.target.value,id)
			if (id == 1) {
				this.form.taxiStartTime1 = e.target.value;
				console.log(this.form.taxiStartTime)
			}
			if (id == 2) {
				this.form.departureTime1 = e.target.value;
			}
			if (id == 3) {
				this.form.landingTime1 = e.target.value;
			}
			if (id == 4) {
				this.form.shutdownTime1 = e.target.value;
			}
			if(id == 5){
				this.form.taxiStartTimeDate = e.target.value;
			}
			if(id == 6){
				this.form.departureTimeDate = e.target.value;
			}
			if(id == 7){
				this.form.landingTimeDate = e.target.value;
			}
			if(id == 8){
				this.form.shutdownTimeDate = e.target.value;
			}
		},
		// 返回"我的"主页面
		backMyPage() {
			uni.navigateBack({
				 url: '/pages/home/<USER>'
			});
		},
		
		async getData() {
			let param = {
				flightSortiesId: this.flightSortiesId,
			};
			try {
				const res = await getFlightDetailById(param);
				this.taskInfo = res.response.data;
			} catch (e) {
				console.error(e)
			}
		},
		previews(item){
			uni.previewImage({
				urls:item
			})
		},
		//提交答题
		submit(type) {
			uni.showLoading({
			    title: '加载中'  
			});
			let that = this
			let formDatasss = new FormData()
			formDatasss.append("flightPlanId", this.flightPlanId)
			formDatasss.append("checkedPassengerLegacy", this.form.checkedPassengerLegacy)
			formDatasss.append("checkedItemDamage", this.form.checkedItemDamage)
			formDatasss.append("completedCleaning", this.form.completedCleaning)
			for(let i=0;i<that.path.length;i++){
				formDatasss.appendFile('bigBooks',that.path[i])
			}
			formDatasss = formDatasss.getData(); 
			console.log(formDatasss)
			uni.request({
			    url: api.baseUrl+'/wechat/dataArchive/uploadBigBook', 
			    data: formDatasss.buffer,
				method:'POST',
			    header: {
					'AuthCode':uni.getStorageSync('token'),
					'AuthID':uni.getStorageSync('userInfo').userId,
			        'Content-Type':formDatasss.contentType,
			    },
			    success: (res) => {
					uni.hideLoading()
					if(res.data.code != 200){
						uni.showToast({
							title: res.data.msg,
							icon:'error'
						})	
						return false
					}
					uni.showToast({
						title: res.data.msg,
					})	
					setTimeout(()=>{
						uni.navigateTo({
						    url: '/pages/home/<USER>'
						});
					},1500)
			    }
			});
			// let formData = { assignments:this.path,flightPlanId :91,missionStatements:[],
			// releasePermits :[],sailingNotices:[],weathers:[]}
			// formData = JSON.stringify(formData)
			// let formData2 = {flightPlanId :91,assignments:this.path[0]}
			// console.log(formData)
			// // const baseUrl = 'https://ga.swcares.com.cn/trade/oc/wechat'
			// uni.uploadFile({
			// 	url:baseUrl+'/wechat/dataArchive/uploadAssignment',
			// 	filePath:'',
			// 	formData:formData2,
			// 	name:'file',
			// 	header:{
			// 		AuthCode:uni.getStorageSync('token'),
			// 		AuthID:uni.getStorageSync('userInfo').userId
			// 	},
			// 	async success(res){
			// 		res = JSON.parse(res.data)
			// 		console.log(res)
			// 		if(that.path.length == 1){
			// 			uni.showToast({
			// 				title: res.msg
			// 			})	
			// 			setTimeout(()=>{
			// 				uni.navigateBack({
			// 				    delta: 1,
			// 				});
			// 			},1500)
			// 		}else{
			// 			var idFile = res.data.id
			// 			that.path.map((i,o)=>{
			// 				if(o!=0){
			// 					uni.uploadFile({
			// 						url:baseUrl+'/wechat/flightDetail/importAssignmentFile1change',
			// 						filePath:i,
			// 						formData:{flightSortiesId:that.flightSortiesId,file:i},
			// 						name:'file',
			// 						header:{
			// 							AuthCode:uni.getStorageSync('token'),
			// 							AuthID:uni.getStorageSync('userInfo').userId
			// 						},
			// 						success(respond){
			// 							respond = JSON.parse(respond.data)
			// 							if(o == that.path.length-1){
			// 								uni.showToast({
			// 									title: respond.msg
			// 								})	
			// 								setTimeout(()=>{
			// 									uni.navigateBack({
			// 									    delta: 1,
			// 									});
			// 								},1500)
			// 							}
			// 						},error(error){
			// 							uni.showToast({
			// 								title: '上传失败'
			// 							})
			// 						} 
			// 					})
			// 				}
			// 			})
			// 		}
					
			// 	},error(error){
			// 		uni.showToast({
			// 			title: '上传失败'
			// 		})
			// 	} 
			// })
			
			
			// uni.uploadFile({
			// 	url:'https://ga.swcares.com.cn/trade/oc/wechat/wechat/flightDetail/importAssignmentFile',
			// 	filePath:that.path,
			// 	formData:formData,
			// 	name:'file',
			// 	header:{
			// 		AuthCode:uni.getStorageSync('token'),
			// 		AuthID:uni.getStorageSync('userInfo').userId
			// 	},
			// 	async success(responce){
			// 		// param.id= JSON.parse(responce.data).data.id
			// 		const res =await flightSortiesSynBook(param)
			// 		uni.showToast({
			// 			title: res.response.msg
			// 		})
			// 		setTimeout(()=>{
			// 			uni.navigateBack({
			// 			    delta: 1,
			// 			});
			// 		},1500)
			// 	},error(error){
			// 		console.log(error)
			// 	} 
			// })
			
		}
	}
};
</script>

<style lang="less" scoped>

page {
	height: 100%;
}

.background {
	width: 100%;
	height: 100%;
	// position: fixed;
	background-size: 100% 100%;
	z-index: -1;
	position: absolute;
	top: 0px;
	bottom: 0px;
}

.nav {
	width: 100%;
	overflow: hidden;
	position: relative;
	top: 0;
	left: 0;
	z-index: 10;
}

.nav-title {
	width: 100%;
	height: inherit;
	position: relative;
	z-index: 10;
	font-family: SF Pro Text;
	font-style: normal;
	font-weight: 600;
	font-size: 16px;
	color: #000000;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 15px;

	.nav-title-text {
		// padding-top: 10px;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;

		&:first-child {
			font-weight: normal;
			position: absolute;
			left: 25px;
			font-size: 22px;
		}
	}
}

.uni-column{
	    background: #ffffff86;
		line-height: 30px;
		padding: 10px 20px;
		font-size: 13px;

}
.content{
	padding: 10px;
}
textarea{
	    border: 1px solid #efefef;
	    width: 100%;
	    padding: 10px;
	    box-sizing: border-box;
}
</style>
