<template>
  <view class="add-fight-task">
    <!-- 自定义导航栏 -->
    <CustomerNav :title="pageId ? '任务修改' : '任务提交'" />

    <!-- 内容主体 -->
    <view class="content">
      <view class="tips mb-16" v-if="pageId">*只能修改架次信息相关</view>
      <view class="form-box">
        <FormItem label="注册号" required show-icon label-width="65px">
          <input
            class="input-box"
            v-model="formData.registrationNumber.text"
            placeholder="请选择注册号"
            disabled
            @click="
              pageId
                ? ''
                : openPicker(
                    '注册号',
                    registrationOptions,
                    'registrationNumber'
                  )
            "
          />
        </FormItem>
        <FormItem label="机型" required label-width="65px">
          <input
            class="input-box"
            v-model="formData.registrationNumber.value"
            placeholder="请选择机型"
            disabled
          />
        </FormItem>
        <FormItem label="任务性质" required label-width="65px">
          <view>
            <view class="btn-groups">
              <view
                class="custom-tag"
                v-for="item in taskTypeList"
                :class="formData.taskTypeValue === item.value ? 'active' : ''"
                :key="item.value"
                @click="onTaskTypeChange(item)"
              >
                {{ item.text }}
              </view>
            </view>

            <view
              class="input-box-line"
              v-if="formData.taskTypeValue === '其他'"
            >
              <input
                v-model="formData.taskType"
                placeholder="请选择"
                disabled
                @click="openPicker('任务性质', flightPurposeList, 'taskType')"
              />
              <van-icon name="arrow-down" class="right-icon" />
            </view>
          </view>
        </FormItem>
        <FormItem label="计划时间" required label-width="65px">
          <view>
            <view class="btn-groups">
              <view
                class="custom-tag"
                v-for="item in planTimeList"
                :class="formData.flightDateValue === item.value ? 'active' : ''"
                :key="item.value"
                @click="onPlanTimeChange(item)"
              >
                {{ item.text }}
              </view>
            </view>

            <view
              class="input-box-line"
              v-if="formData.flightDateValue === '其他日期'"
            >
              <input
                v-model="formData.flightDate"
                placeholder="请选择"
                disabled
                @click="datePickerShow = true"
              />
              <van-icon name="arrow-down" class="right-icon" />
            </view>
          </view>
        </FormItem>

        <FormItem label="起飞基地" label-width="65px" required show-icon>
          <input
            class="input-box"
            v-model="formData.departure.text"
            placeholder="请选择"
            disabled
            @click="
              pageId ? '' : openPicker('起飞基地', baseOptions, 'departure')
            "
          />
        </FormItem>
        <FormItem
          label="降落基地"
          label-width="65px"
          required
          show-icon
          v-if="formData.taskType.indexOf('空中游览') < 0"
        >
          <view class="flex-row">
            <input
              class="input-box"
              v-model="formData.arrive.text"
              placeholder="请选择"
              disabled
              @click="
                pageId ? '' : openPicker('降落基地', baseOptions, 'arrive')
              "
            />
          </view>
        </FormItem>
        <!--        <FormItem label="航线选择" show-icon v-else label-width="65px">-->
        <!--          <input-->
        <!--            class="input-box"-->
        <!--            v-model="formData.routeOrAirspaceName.text"-->
        <!--            placeholder="请选择航线"-->
        <!--            disabled-->
        <!--            @click="openPicker('航线', routeOptions, 'routeOrAirspaceName')"-->
        <!--          />-->
        <!--        </FormItem>-->

        <FormItem label="预计架次" label-width="65px">
          <input
            v-model="formData.flightFrequency"
            placeholder="请输入预计架次"
            type="number"
            disabled
          />
        </FormItem>
        <view class="plan-time">
          <view class="plan-time-title"
            >计划起降时间
            <text>（时间输入格式如：0830）</text>
          </view>
          <view class="plan-time-content">
            <view
              v-for="(item, index) in formData.takeOffAndLanding"
              :key="index"
              class="plan-time-item"
            >
              <van-row class="text-row">
                <van-col span="14">
                  <FormItem
                    :label="'架次' + (index + 1)"
                    class="text-row"
                    label-width="50px"
                    required
                  >
                    <input
                      class="input-box"
                      v-model="item.planDepartTime"
                      placeholder="预计起飞时间"
                    />
                  </FormItem>
                </van-col>
                <van-col span="10">
                  <FormItem class="text-row" label-width="0px">
                    <input
                      class="input-box"
                      v-model="item.planArriveTime"
                      placeholder="预计降落时间"
                    />
                  </FormItem>
                </van-col>
              </van-row>
              <van-row class="text-row">
                <van-col span="20">
                  <FormItem label=" " class="text-row" label-width="50px">
                    <input
                      class="input-box"
                      v-model="item.departure.text"
                      placeholder="起飞基地"
                      @click="
                        openPicker('起飞基地', baseOptions, 'departure', index)
                      "
                    />
                  </FormItem>
                </van-col>
              </van-row>
              <van-row
                class="text-row"
                v-if="formData.taskType.indexOf('空中游览') < 0"
              >
                <van-col span="20">
                  <FormItem label=" " class="text-row" label-width="50px">
                    <input
                      class="input-box"
                      v-model="item.arrive.text"
                      placeholder="降落基地"
                      @click="
                        openPicker('降落基地', baseOptions, 'arrive', index)
                      "
                    />
                  </FormItem>
                </van-col>
              </van-row>
              <van-row>
                <van-col span="20">
                  <FormItem label=" " class="text-row" label-width="50px">
                    <input
                      class="input-box"
                      v-model="item.sortiesRemark"
                      placeholder="备注"
                    />
                  </FormItem>
                </van-col>
                <van-col
                  span="4"
                  class="reduce-box"
                  v-if="formData.takeOffAndLanding.length > 1"
                >
                  <view
                    class="reduce-icon"
                    @click="reduceTakeOffAndLanding(index)"
                    >-
                  </view>
                </van-col>
              </van-row>
            </view>
          </view>

          <van-row>
            <view class="add-btn" @click="addTakeOffAndLanding()"> +</view>
          </van-row>
        </view>

        <FormItem label="起降间隔" label-width="65px">
          <view>
            <view class="btn-groups">
              <view
                class="custom-tag"
                v-for="item in timeIntervalList"
                :class="formData.timeInterval === item.value ? 'active' : ''"
                :key="item.value"
                @click="onTimeIntervalChange(item)"
              >
                {{ item.text }}
              </view>
            </view>
          </view>
        </FormItem>
        <FormItem label="智能解析" multi-line>
          <textarea
            v-model="parseText"
            placeholder="请输入"
            :maxlength="-1"
            class="textarea-box"
          />
        </FormItem>
        <view class="flex-right-row mt-8">
          <view class="normal-btn" @click="handleParseText">解析</view>
        </view>
        <view class="submit-btn margin-top-20" @click="submitForm"
          >确定提交
        </view>
      </view>
    </view>
    <Background />
    <!-- 下拉选择-->
    <van-popup :show="pickerData.show" position="bottom">
      <van-picker
        :columns="pickerData.list"
        @confirm="onPickerConfirm"
        @cancel="closePicker"
        show-toolbar
        :title="pickerData.title"
      />
    </van-popup>
    <!-- 日期选择器 -->
    <van-calendar
      :show="datePickerShow"
      @close="datePickerShow = false"
      @confirm="onDateConfirm"
      color="#2C5DE5"
    />
    <!--提示    -->
    <van-dialog id="van-dialog" />
  </view>
</template>

<script>
import CustomerNav from '../../components/CutomerNav/index.vue'
import FormItem from './compoents/FormItem.vue'
import dayjs from 'dayjs'
import { DATE_FORMAT, SUCCESS_CODE } from '../../utils/constant'
import {
  addFlightTask,
  getAircraftStyle,
  getAirportAll,
  getFlightPurpose,
  getRouteAll,
  parseTextFlightTask,
  queryFlightTaskConfigDetail,
  updateTaskSorties,
} from '../../api/flightTask'
import Background from '../../components/Background/index.vue'
import Dialog from '../../wxcomponents/vant/dialog/dialog'
import { transferDateText } from './contant'

export default {
  name: 'createFlightTask',
  components: { Background, FormItem, CustomerNav },
  data() {
    return {
      pageId: null,
      // 表单数据
      formData: {
        registrationNumber: { text: '', value: '' }, // 注册号
        aircraftType: '', // 机型
        taskType: '空中交通', // 任务性质
        taskTypeValue: '空中交通', //任务性质选中按钮的值--页面展示用
        flightDate: dayjs().format(DATE_FORMAT), //计划时间
        flightDateValue: '今天', //计划时间选中按钮的值--页面展示用
        timeInterval: '', //  起降间隔--页面展示用
        flightFrequency: 1, //架次
        takeOffAndLanding: [
          {
            planArriveTime: '',
            planDepartTime: '',
            sortiesRemark: '',
            arrive: { text: '', value: '' },
            departure: { text: '', value: '' },
            id: null,
          },
        ], //起降时间
        // remark: '',
        flightType: 1, //航线类型
        routeOrAirspaceName: { text: '', value: '' }, //航线选择
        departure: { text: '', value: '' }, //起飞基地
        arrive: { text: '', value: '' }, //降落基地
      },
      routeOptions: [], //航线
      //机型
      registrationOptions: [],
      //任务类型
      taskTypeList: [
        { text: '空中游览', value: '空中游览' },
        { text: '空中交通', value: '空中交通' },
        { text: '包机', value: '包机' },
        { text: '调机', value: '调机' },
        { text: '其他', value: '其他' },
      ],
      //任务性质
      flightPurposeList: [],
      //计划时间
      planTimeList: [
        { text: '今天', value: '今天' },
        { text: '明天', value: '明天' },
        { text: '其他日期', value: '其他日期' },
      ],
      //起降间隔
      timeIntervalList: [
        { text: '8分钟', value: 8 },
        { text: '10分钟', value: 10 },
        { text: '20分钟', value: 20 },
        { text: '30分钟', value: 30 },
      ],
      baseOptions: [], //起飞/降落/经停点基地
      //下拉选择数据
      pickerData: {
        show: false,
        title: '',
        list: [],
        formKey: '',
        index: null,
      },
      datePickerShow: false,
      timePicker: {
        show: false,
        index: 0,
        formKey: '',
        value: '12:00',
      },
      parseText:
        '【机型】BELL429 B-7613\n' +
        '【任务性质】空中游览\n' +
        '【任务地点】复兴岛\n' +
        '【日期】2025年8月22日\n' +
        '【架次】14:00-14:10 包机\n' +
        '【架次】14:10-14:20 包机\n' +
        '【架次】16:00-16:10 包机\n' +
        '【架次】16:30-16:40 包机\n' +
        '【架次】19:30-16:40 预约客人一位\n' +
        '【架次】20:30-16:40 预约达人6位', //解析
    }
  },
  created() {
    this.getPickerListData()
  },
  onLoad: function (option) {
    this.pageId = option.id
    option.id && this.getEditData()
  },
  methods: {
    openPicker(title, list, formKey, index = null) {
      this.pickerData = {
        show: true,
        title: title,
        list: list,
        formKey: formKey,
        index: index,
      }
    },
    closePicker() {
      this.pickerData = {
        show: false,
        title: '',
        list: [],
        formKey: '',
        index: null,
      }
    },
    onPickerConfirm(ev) {
      if (typeof this.pickerData.index === 'number') {
        this.formData.takeOffAndLanding[this.pickerData.index][
          this.pickerData.formKey
        ] = ev.detail.value
        this.closePicker()
        return ''
      }
      this.formData[this.pickerData.formKey] = ev.detail.value
      if (this.pickerData.formKey === 'taskType') {
        this.formData.taskType = ev.detail.value.text
      }
      this.closePicker()
    },
    //日历选择
    onDateConfirm(ev) {
      this.formData.flightDate = dayjs(ev.detail).format(DATE_FORMAT)
      this.datePickerShow = false
    },
    //任务性质按钮
    onTaskTypeChange(item) {
      if (this.pageId) return
      this.formData.taskTypeValue = item.value
      this.formData.taskType = item.value
    },
    //计划时间按钮
    onPlanTimeChange(item) {
      if (this.pageId) return
      this.formData.flightDateValue = item.value
      if (item.value === '今天') {
        this.formData.flightDate = dayjs().format(DATE_FORMAT)
      } else if (item.value === '明天') {
        this.formData.flightDate = dayjs().add(1, 'day').format(DATE_FORMAT)
      }
    },
    //起降时间间隔
    onTimeIntervalChange(item) {
      this.formData.timeInterval = item.value
      const intervalMinutes = parseInt(item.value) // 提取间隔分钟数

      this.formData.takeOffAndLanding.map((item) => {
        if (item.planDepartTime) {
          // 创建dayjs时间对象并加上间隔分钟数
          item.planArriveTime = dayjs(
            `${this.formData.flightDate} ${item.planDepartTime}`
          )
            .add(intervalMinutes, 'minute')
            .format('HHmm')
        }
      })
    },
    //架次新增
    addTakeOffAndLanding() {
      if (
        this.formData.taskTypeValue === '调机' &&
        this.formData.takeOffAndLanding.length === 1
      ) {
        Dialog.confirm({
          message: '是否添加多个架次',
        }).then(() => {
          this.formData.takeOffAndLanding.push({
            planArriveTime: '',
            planDepartTime: '',
            sortiesRemark: '',
            arrive: { text: '', value: '' },
            departure: { text: '', value: '' },
            id: null,
          })
          this.formData.flightFrequency++
        })
        return ''
      }
      this.formData.takeOffAndLanding.push({
        planArriveTime: '',
        planDepartTime: '',
        sortiesRemark: '',
        arrive: { text: '', value: '' },
        departure: { text: '', value: '' },
        id: null,
      })
      this.formData.flightFrequency++
    },
    //架次删除
    reduceTakeOffAndLanding(index) {
      this.formData.takeOffAndLanding.splice(index, 1)
      this.formData.flightFrequency--
    },
    //获取数据
    async getPickerListData() {
      //任务性质
      const res = await getFlightPurpose()
      if (res.response.code === SUCCESS_CODE) {
        this.flightPurposeList = res.response.data.map((item) => {
          return {
            text: item.name || '',
            value: item.id || '',
          }
        })
      }
      // 机型机号
      const res2 = await getAircraftStyle()
      if (res2.response.code === SUCCESS_CODE) {
        this.registrationOptions = res2.response.data.map((item) => {
          return {
            text: item.aircraftTailNo || '',
            value: item.aircraftStyle || '',
          }
        })
      }
      //获取航线
      const res3 = await getRouteAll()
      if (res3.response.code === SUCCESS_CODE) {
        this.routeOptions = res3.response.data.map((item) => {
          return {
            ...item,
            text: item.routeCode || '',
            value: item.id || '',
          }
        })
      }
      //基地列表
      const res5 = await getAirportAll()
      if (res5.response.code === SUCCESS_CODE) {
        this.baseOptions = res5.response.data.map((item) => {
          return {
            text: item.name || '',
            value: item.threeAirportCode || '',
          }
        })
      }
    },
    //获取编辑数据
    async getEditData() {
      const { response: res } = await queryFlightTaskConfigDetail({
        flightTaskConfigId: Number(this.pageId),
      })
      if (res.code === SUCCESS_CODE) {
        this.transferFormData(res.data)
      }
    },
    //解析数据
    async handleParseText() {
      if (!this.parseText) return
      const { response: res } = await parseTextFlightTask({
        flightTaskText: this.parseText,
      })
      if (res.code === SUCCESS_CODE) {
        this.transferFormData(res.data)
      }
    },
    //处理详情/解析数据格式转换
    transferFormData(data) {
      this.formData = {
        ...this.formData,
        registrationNumber: {
          text: data.registrationNumber,
          value: data.aircraftType,
        }, // 注册号
        aircraftType: data.aircraftType, // 机型
        taskType: data.taskType,
        flightDate: data.flightDate,
        flightDateValue: transferDateText(data.flightDate),
        flightFrequency: data.flightFrequency,
        departure: { text: data.departure, value: data.departureCode },
        arrive: { text: data.arrive, value: data.arriveCode },
      }
      // this.taskTypeValue = this.taskTypeList.find((item) => item.value === data.taskType).value || '其他'
      this.taskTypeValue = this.taskTypeList.find((item) => {
        if (item.value === data.taskType) {
          return item.value
        }
        return '其他'
      })
      this.formData.takeOffAndLanding = data.takeOffAndLanding.map((item) => {
        return {
          sortiesRemark: item.sortiesRemark,
          planArriveTime:
            item.planArriveTime.split(':')[0] +
            item.planArriveTime.split(':')[1],
          planDepartTime:
            item.planDepartTime.split(':')[0] +
            item.planDepartTime.split(':')[1],
          departure: { text: item.departure, value: item.departureCode },
          arrive: { text: item.arrive, value: item.arriveCode },
          id: item.id,
        }
      })
    },

    //表单校验
    validateForm() {
      // 表单验证
      if (!this.formData.registrationNumber.text) {
        uni.showToast({
          title: '请选择注册号',
          icon: 'none',
        })
        return false
      }

      if (!this.formData.taskType) {
        uni.showToast({
          title: '请选择任务性质',
          icon: 'none',
        })
        return false
      }

      if (!this.formData.flightDate) {
        uni.showToast({
          title: '请选择计划时间',
          icon: 'none',
        })
        return false
      }
      if (!this.formData.takeOffAndLanding?.length < 0) {
        uni.showToast({
          title: '请输入计划起降时间',
          icon: 'none',
        })
        return false
      }

      return true
    },

    // 提交表单
    async submitForm() {
      if (!this.validateForm()) return
      // const area = this.routeOptions.filter((item) => {
      //   if (item.id === this.formData.routeOrAirspaceName.value) {
      //     return item
      //   }
      // })[0]
      //格式化时间
      const takeOffAndLanding = JSON.parse(
        JSON.stringify(this.formData.takeOffAndLanding)
      )
      takeOffAndLanding.map((item) => {
        if (item.planArriveTime) {
          // 创建dayjs时间对象并加上间隔分钟数
          item.planArriveTime = dayjs(
            `${this.formData.flightDate} ${item.planArriveTime}`
          ).format('HH:mm')
        }
        if (item.planDepartTime) {
          // 创建dayjs时间对象并加上间隔分钟数
          item.planDepartTime = dayjs(
            `${this.formData.flightDate} ${item.planDepartTime}`
          ).format('HH:mm')
        }
        if (item.arrive) {
          item.arrive = item.arrive.text || ''
          item.arriveCode = item.arrive.value || ''
        }
        if (item.departure) {
          item.departure = item.departure.text || ''
          item.departureCode = item.departure.value || ''
        }
      })
      const params = {
        aircraftType: this.formData.registrationNumber.value,
        departure: this.formData.departure.text,
        departureCode: this.formData.departure.value,
        arrive: this.formData.departure.text,
        arriveCode: this.formData.departure.value,
        flightDate: this.formData.flightDate,
        flightFrequency: this.formData.flightFrequency,
        flightType: this.formData.flightType,
        routeOrAirspaceId: this.formData.routeOrAirspaceName.value,
        routeOrAirspaceName: this.formData.routeOrAirspaceName.text,
        registrationNumber: this.formData.registrationNumber.text,
        takeOffAndLanding: takeOffAndLanding,
        taskType: this.formData.taskType,
      }
      if (this.pageId) {
        const { response } = await updateTaskSorties({
          flightTaskConfigId: this.pageId,
          takeOffAndLanding: takeOffAndLanding,
        })
        if (response.code === SUCCESS_CODE) {
          uni.showToast({
            title: '修改成功',
            icon: 'success',
          })
          this.getEditData()
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        }
      } else {
        const { response } = await addFlightTask(params)
        if (response.code === SUCCESS_CODE) {
          uni.showToast({
            title: '创建成功',
            icon: 'success',
          })
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.add-fight-task {
  width: 100%;
  min-height: 100vh;

  .tips {
    font-size: 12px;
    color: rgba(50, 50, 51, 0.6);
  }

  .mb-16 {
    margin-bottom: 12px;
  }
}

// 内容区域
.content {
  box-sizing: border-box;
  padding: 16px;

  .form-box {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .btn-groups {
    width: 100%;
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
  }

  .custom-tag {
    padding: 0 4px;
    height: 24px;
    line-height: 24px;
    border: 1px solid #2c5de5;
    color: #2c5de5;
    background: transparent;
    font-size: 12px;
    border-radius: 4px;

    &.active {
      background: #2c5de5;
      color: #fff;
    }
  }

  input {
    font-size: 14px;
    font-weight: normal;

    &::placeholder {
      font-weight: normal;
      color: #999;
    }
  }

  .input-box-line {
    padding: 4px;
    //border: 1px solid #999;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
  }

  .mt-8 {
    margin-top: 8px;
  }

  .right-icon {
    color: #999;
    font-size: 12px;
  }

  .flex-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 6px;
  }

  /deep/ .text-row {
    .value-box {
      border-bottom: none;
    }

    .input-box {
      border-bottom: 1px solid #ccc;
    }
  }

  .flex-right-row {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .textarea-box {
    width: 100%;
    margin-top: 8px;
    border: 1px solid #eee;
    box-sizing: border-box;
    padding: 8px;
    border-radius: 4px;
  }

  .plan-time {
    .plan-time-title {
      font-size: 14px;
      color: rgba(50, 50, 51, 1);
      word-break: break-all;
      position: relative;
      padding: 8px 0;

      text {
        font-size: 12px;
        color: rgba(50, 50, 51, 0.6);
      }
    }

    .plan-time-content {
      padding: 8px 1em;
      border: 1px dashed #ccc;
      border-radius: 4px;

      .plan-time-item {
        border-bottom: 1px dashed #ccc;

        &:last-child {
          border-bottom: none;
        }
      }
    }

    .reduce-icon {
      width: 16px;
      height: 16px;
      border: 1px solid #2c5de5;
      border-radius: 50%;
      color: #2c5de5;
      font-size: 12px;
      text-align: center;
      line-height: 14px;
      margin: 8px auto;
    }

    .add-btn {
      width: 100%;
      height: 28px;
      border-radius: 4px;
      border: 1px solid #2c5de5;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 12px auto;
      color: #2c5de5;

      &:active {
        opacity: 0.8;
      }
    }
  }
}
</style>
