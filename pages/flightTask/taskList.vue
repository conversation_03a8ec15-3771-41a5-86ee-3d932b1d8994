<template>
  <view class="receive-page page-bg">
    <!-- 自定义导航栏 -->
    <CustomerNav
      :title="pageType === 1 ? '待确认任务' : '任务列表'"
      custom-go-back
      @onBack="onBack"
    />
    <!--筛选    -->
    <view class="filter-box">
      <FormItem label="时间筛选">
        <view>
          <view class="btn-groups">
            <view
              class="custom-tag"
              v-for="item in dateOptionList"
              :class="
                filterForm.selectedDateType === item.value ? 'active' : ''
              "
              :key="item.value"
              @click="selectDate(item)"
            >
              {{ item.text }}
            </view>
          </view>
        </view>
      </FormItem>
      <FormItem
        label=" "
        clearable
        @clear="filterForm.flightDate = ''"
        v-if="filterForm.selectedDateType === '其他日期'"
      >
        <view class="input-box-line">
          <input
            v-model="filterForm.flightDate"
            placeholder="请选择"
            disabled
            @click="datePickerShow = true"
          />
        </view>
      </FormItem>
      <FormItem label="机型">
        <view class="btn-groups">
          <view
            class="custom-tag"
            v-for="item in aircraftTypeTextList"
            :class="filterForm.aircraftTypeText === item.value ? 'active' : ''"
            :key="item.value"
            @click="onAircraftTypeTextChange(item)"
          >
            {{ item.text }}
          </view>
        </view>
      </FormItem>
      <FormItem
        label=" "
        v-if="filterForm.aircraftTypeText === '其他'"
        clearable
        @clear="filterForm.aircraftType = ''"
      >
        <view class="input-box-line">
          <input
            v-model="filterForm.aircraftType"
            placeholder="请选择"
            disabled
            @click="openPicker('机型', aircraftTypeList, 'aircraftType')"
          />
        </view>
      </FormItem>
      <FormItem label="发布类型">
        <view class="btn-groups">
          <view
            class="custom-tag"
            v-for="item in publishTypeList"
            :class="filterForm.releaseType === item.value ? 'active' : ''"
            :key="item.value"
            @click="onPublishTypeTextChange(item)"
          >
            {{ item.text }}
          </view>
        </view>
      </FormItem>
      <view class="btn-groups">
        <view class="submit-btn" @click="addTask()" v-if="pageType === 2"
          >新增
        </view>
        <view class="submit-btn normal-btn" @click="getData()">查询</view>
      </view>
    </view>
    <!-- 任务列表 -->
    <view class="task-list" v-if="taskList.length > 0">
      <TaskItem
        v-for="(task, index) in taskList"
        :key="index"
        :task-item="task"
        :pageType="pageType"
        @refresh="getData"
      />
    </view>
    <Empty v-else />
    <!-- 日期选择器 -->
    <van-calendar
      :show="datePickerShow"
      @close="datePickerShow = false"
      @confirm="onDateConfirm"
      color="#2C5DE5"
    />
    <!-- 下拉选择-->
    <van-popup :show="pickerData.show" position="bottom">
      <van-picker
        :columns="pickerData.list"
        @confirm="onPickerConfirm"
        @cancel="closePicker"
        show-toolbar
        :title="pickerData.title"
      />
    </van-popup>
    <van-dialog id="van-dialog" />
    <Background />
  </view>
</template>

<script>
import CustomerNav from '../../components/CutomerNav/index.vue'
import { getAircraftStyle, queryFlightTaskConfig } from '../../api/flightTask'
import { getRelativeDateText } from '../../utils'
import Empty from '../../components/Empty/index.vue'
import Background from '../../components/Background/index.vue'
import TaskItem from './compoents/TaskItem.vue'
import FormItem from '../flightTask--copy/compoents/FormItem.vue'
import dayjs from 'dayjs'
import { DATE_FORMAT, SUCCESS_CODE } from '../../utils/constant'

export default {
  name: 'taskList',
  components: { FormItem, TaskItem, Background, Empty, CustomerNav },
  data() {
    return {
      pageType: 2, //1:未确认, 2:所有
      taskList: [],
      filterForm: {
        selectedDateType: '',
        flightDate: '',
        aircraftType: '',
        aircraftTypeText: '',
        releaseType: 2,
      },
      datePickerShow: false, //日期选择器
      aircraftTypeTextList: [
        { text: '所有', value: '' },
        { text: 'BELL429', value: 'BELL429' },
        {
          text: 'BELL505',
          value: 'BELL505',
        },
        { text: 'AW139', value: 'AW139' },
        { text: '其他', value: '其他' },
      ],
      aircraftTypeList: [], //机型列表
      publishTypeList: [
        { text: '我的', value: 1 },
        { text: '全部', value: 2 },
      ], //发布类型列表
      dateOptionList: [
        { text: '所有', value: '' },
        { text: '今天', value: '今天' },
        { text: '明天', value: '明天' },
        { text: '后天', value: '后天' },
        { text: '其他日期', value: '其他日期' },
      ],
      //下拉选择数据
      pickerData: {
        show: false,
        title: '',
        list: [],
        formKey: '',
      },
    }
  },
  onLoad: function (options) {
    console.log(options)
    this.pageType = Number(options.pageType) || 1
  },
  onShow() {
    this.getAircraftType()
    this.getData()
  },
  onPullDownRefresh() {
    this.filterForm = {
      flightDate: '',
      aircraftType: '',
      aircraftTypeText: '',
      releaseType: 2,
    }
    this.getData().finally(() => {
      uni.stopPullDownRefresh()
    })
  },
  methods: {
    getRelativeDateText,
    onBack() {
      uni.switchTab({
        url: '/pages/home/<USER>',
      })
    },
    // 选择日期
    selectDate(type) {
      this.filterForm.selectedDateType = type.value
      switch (type) {
        case '今天':
          this.selectedDate = dayjs().format(DATE_FORMAT)
          break
        case '明天':
          this.selectedDate = dayjs().add(1, 'day').format(DATE_FORMAT)
          break
        case '后天':
          this.selectedDate = dayjs().add(2, 'day').format(DATE_FORMAT)
          break
        case '其他日期':
          this.datePickerShow = true
          break
      }
    },
    //获取机型
    async getAircraftType() {
      const res = await getAircraftStyle()
      if (res.response.code === SUCCESS_CODE) {
        this.aircraftTypeList = res.response.data.map((item) => {
          return {
            text: item.aircraftTailNo || '',
            value: item.aircraftStyle || '',
          }
        })
      }
    },
    // 加载任务列表
    async getData() {
      const params = {
        aircraftType: this.filterForm.aircraftType,
        flightDate: this.filterForm.flightDate,
        releaseType: this.filterForm.releaseType,
      }
      if (this.pageType === 1) {
        params.taskStatus = 0
      }
      const res = await queryFlightTaskConfig(params)
      if (res.response.code === 200) {
        this.taskList = res.response.data || []
      }
    },
    //新增任务
    addTask() {
      uni.navigateTo({
        url: '/pages/flightTask/create',
      })
    },
    openPicker(title, list, formKey) {
      this.pickerData = {
        show: true,
        title: title,
        list: list,
        formKey: formKey,
      }
    },
    closePicker() {
      this.pickerData = {
        show: false,
        title: '',
        list: [],
        formKey: '',
      }
    },
    onPickerConfirm(ev) {
      this.filterForm[this.pickerData.formKey] = ev.detail.value.value
      this.closePicker()
    },
    //日期弹窗确定
    onDateConfirm(ev) {
      this.filterForm.flightDate = dayjs(ev.detail).format(DATE_FORMAT)
      this.datePickerShow = false
    },
    //机型点击
    onAircraftTypeTextChange(item) {
      this.filterForm.aircraftTypeText = item.value
      this.filterForm.aircraftType = ''
    },
    //发布类型点击
    onPublishTypeTextChange(item) {
      this.filterForm.releaseType = item.value
    },
  },
}
</script>

<style scoped lang="scss">
@import '../../assets/css/common.less';

.receive-page {
  min-height: 100vh;
}

.task-list {
  padding: 0 16px 16px 16px;
}

.filter-box {
  width: calc(100% - 32px);
  box-sizing: border-box;
  padding: 16px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  margin: 16px auto;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.input-box-line {
  //padding: 4px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 8px;
  gap: 8px;
  color: #666;
  font-size: 14px;

  .right-icon {
    font-size: 12px;
    color: #999;
  }

  input {
    width: 100%;
  }
}

.btn-groups {
  width: 100%;
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.custom-tag {
  padding: 0 4px;
  height: 24px;
  line-height: 24px;
  border: 1px solid #2c5de5;
  color: #2c5de5;
  background: transparent;
  font-size: 12px;
  border-radius: 4px;

  &.active {
    background: #2c5de5;
    color: #fff;
  }
}

.search-btn {
  width: 100%;
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.submit-btn {
  flex: 1;
  background: #2c5de5;
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
  box-sizing: border-box;
  padding: 8px 16px;
  margin-top: 16px;
  text-align: center;
  //margin-left: 50%;
  //transform: translateX(-50%);

  &:active {
    opacity: 0.8;
  }
}

.normal-btn {
  background: #fff;
  border: 1px solid #2c5de5;
  color: #2c5de5;
}
</style>
