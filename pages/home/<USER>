<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <view class="nav bg-white" :style="'height:' + navH + 'px'">
      <view class="nav-title">
        <text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
        <text class="nav-title-text">我的待办</text>
      </view>
    </view>
    <!-- 内容主体 -->
    <scroll-view class="bg-gray overflow" :style="'height:calc(100vh - ' + navH + 'px)'" scroll-y
                 :refresher-enabled=true
                 @refresherrefresh="DownRefresh" :refresher-triggered='flagger'>
      <!-- <button type="default" @click='authapprol'>订阅待办消息</button> -->
      <view class="uni-flex uni-column" style="overflow: hidden;padding-top: 10px;">
        <view class="flex-item flex-item-V uni-bg-red" @click="goDetail(item)" :key='index'
              v-for="(item,index) in DataList"
              style="overflow: hidden;border-bottom: 1px solid #eee;padding:10px 5px;
					background: #ffffff96;    border-radius: 8px;margin:0 15px 8px;">
          <view class="contentBox" style="width: 40px;">
            <icon type="waiting" size="20" style="margin: 8px 10px 0;"/>
          </view>
          <view class="contentBox" style="width: calc(100% - 40px);">
            <view class="tops" style="margin-bottom: 5px;">
              {{ item.title }}
            </view>
            <view class="bottoms">
              {{ item.content }}
            </view>
          </view>
        </view>

      </view>
      <!-- <task-card v-for="(item, index) in taskData" :key="index" :taskIndex="index" :taskInfo="item">
      </task-card> -->
      <view class="no-data-box" v-if="noData">
        <image class="image" src="../../static/images/nodata.png" mode="widthFix"></image>
        <text class="col-gray-500">当前无数据</text>
      </view>
    </scroll-view>
    <!-- 整体背景放在最后 -->
    <view class="bg-main">
      <image class="background" src="../../static/images/background.png"></image>
    </view>
  </view>
</template>

<script>
import {getFlightDetailById, getVerifySure, getMessageList} from '../../api/weChat.js';

const App = getApp();
export default {
  data() {
    return {
      navH: 0,
      timer: '',
      taskIndex: 1,
      inputValue: '',
      taskInfo: '',
      componentData: '',
      flightSortiesId: '',
      imgUrl: '',
      currentTime: '',
      noData: false,
      flagger: false,
      flaggerFirst: true,
      DataList: [
        // {aircraftStyle: null,
        // content: "请完成2022-05-20的F444的171次班次的飞行直接准备",
        // examId: null,
        // flightSortiesId: 206,
        // flightplanId: 262,
        // roleType: null,
        // state: 32,
        // title: "机长飞行直接准备"},
      ]
    };
  },
  mounted() {
  },
  onLoad: function (options) {
    this.flightSortiesId = options.flightSortiesId;
    this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
  },
  onShow() {
    this.getData();
    console.log(123)
    let that = this
    // setTimeout(function(){
    // 	that.authapprol()
    // },1000)
    // 十三点
  },
  methods: {
    // authapprol(){
    // 	uni.requestSubscribeMessage({  //消息订阅
    // 		tmplIds:['b3qWKH5e8gTejY0wrW6HOAZk-depvgOMoldHItovqPc'],
    // 		success:function(res){
    // 			console.log(res)
    // 		}
    // 	})
    // },
    async DownRefresh() {
      console.log(1)
      this.flagger = true

      this.getData();
      this.$set(this.$data, 'navH', Number(App.globalData.navHeight));

    },
    goDetail(item) {
      /**
       * 11：机长/副驾驶飞行学习
       * 12：机长/副驾驶预先准备
       * 13：放行员预先准备
       * 21：机长直接准备
       * 22：副驾驶直接准备
       * 23：放行员直接准备
       * 24：保障员直接准备
       * 31：机长飞行给讲评
       * 41：副驾驶填写飞行时间
       * 42：副驾驶上传任务书
       * 43：保障员上传维修记录本
       * 44：机长确认飞行时间
       * 45：责任运控确认飞行时间
       */

      if (item.state == 11) {
        uni.navigateTo({
          url: '/pages/todo/studyPre?flightplanId=' + item.flightplanId + '&roleType=' + item.roleType + '&flightSortiesId=' + item.flightSortiesId
        });
      } else if (item.state == 12) {
        uni.navigateTo({
          url: '/pages/todo/advance?flightplanId=' + item.flightplanId + '&roleType=' + item.roleType + '&flightSortiesId=' + item.flightSortiesId
        });
      } else if (item.state == 13) {
        uni.navigateTo({
          url: '/pages/todo/advance?flightSortiesId=' + item.flightSortiesId + '&flightplanId=' + item.flightplanId + '&roleType=' + item.roleType
        });
      } else if (item.state == 21) {
        uni.navigateTo({
          url: '/pages/todo/captionDerect?flightSortiesId=' + item.flightSortiesId + '&flightplanId=' + item.flightplanId + '&roleType=' + item.roleType
        });
      } else if (item.state == 22) {
        uni.navigateTo({
          url: '/pages/todo/copolitDerect?flightSortiesId=' + item.flightSortiesId + '&flightplanId=' + item.flightplanId + '&roleType=' + item.roleType
        });
      } else if (item.state == 23) {
        uni.navigateTo({
          url: '/pages/todo/maintenanceDerect?flightSortiesId=' + item.flightSortiesId + '&flightplanId=' + item.flightplanId + '&roleType=' + item.roleType
        });
      } else if (item.state == 24) {
        uni.navigateTo({
          url: '/pages/todo/safemaintenanceDerect?flightSortiesId=' + item.flightSortiesId + '&flightplanId=' + item.flightplanId + '&roleType=' + item.roleType
        });
      } else if (item.state == 31) {
        uni.navigateTo({
          url: '/pages/todo/commentary?flightSortiesId=' + item.flightSortiesId + '&flightplanId=' + item.flightplanId + '&roleType=' + item.roleType
        });
      } else if (item.state == 41) {
        uni.navigateTo({
          url: '/pages/todo/commitments?flightSortiesId=' + item.flightSortiesId + '&flightplanId=' + item.flightplanId + '&roleType=' + item.roleType
        });
      } else if (item.state == 42) {
        uni.navigateTo({
          url: '/pages/todo/emergency?flightSortiesId=' + item.flightSortiesId + '&flightplanId=' + item.flightplanId + '&roleType=' + item.roleType
        });
      } else if (item.state == 43) {
        uni.navigateTo({
          url: '/pages/todo/daben?flightSortiesId=' + item.flightSortiesId + '&flightplanId=' + item.flightplanId + '&roleType=' + item.roleType
        });
      } else if (item.state == 44) {
        uni.navigateTo({
          url: '/pages/todo/givenFlight?flightSortiesId=' + item.flightSortiesId + '&flightplanId=' + item.flightplanId + '&roleType=' + item.roleType
        });
      } else if (item.state == 45) {
        uni.navigateTo({
          url: '/pages/todo/givenFlight?flightSortiesId=' + item.flightSortiesId + '&flightplanId=' + item.flightplanId + '&roleType=' + item.roleType
        });
      } else if (item.state == 6666) {
        uni.navigateTo({
          url: '/pages/todo/deliveryDerect?flightSortiesId=' + item.flightSortiesId + '&flightplanId=' + item.flightplanId + '&roleType=' + item.roleType
        });
      } else if (item.state == 44444) {
        uni.navigateTo({
          url: '/pages/todo/givenFlight?flightSortiesId=' + item.flightSortiesId + '&flightplanId=' + item.flightplanId + '&roleType=' + item.roleType
        });
      } else if (item.state == 8888) {
        uni.navigateTo({
          url: '/pages/todo/emergency?flightSortiesId=' + item.flightSortiesId + '&flightplanId=' + item.flightplanId + '&roleType=' + item.roleType
        });
      } else if (item.state == 81) {
        uni.navigateTo({
          url: '/pages/todo/emergencyAprrol?flightSortiesId=' + item.flightSortiesId + '&type=exceptionalCaseForCaptain' + '&roleType=' + item.roleType
        });
      } else if (item.state == 82222) {
        uni.navigateTo({
          url: '/pages/todo/emergencyAprrol?flightSortiesId=' + item.flightSortiesId + '&type=exceptionalCaseForCopilot' + '&roleType=' + item.roleType
        });
      } else if (item.state == 83333) {
        uni.navigateTo({
          url: '/pages/todo/emergencyAprrol?flightSortiesId=' + item.flightSortiesId + '&type=exceptionalCaseForMaintenance' + '&roleType=' + item.roleType
        });
      } else if (item.state == 84444) {
        uni.navigateTo({
          url: '/pages/todo/emergencyAprrol?flightSortiesId=' + item.flightSortiesId + '&type=exceptionalCaseForOc' + '&roleType=' + item.roleType
        });
      } else if (item.state == 95555) {
        uni.navigateTo({
          url: '/pages/todo/flyTraining?flightplanId=' + item.flightplanId + '&roleType=' + item.roleType + '&flightSortiesId=' + item.flightSortiesId + '&roleType=' + item.roleType
        });
      }
      // if(state == 9){
      // 	uni.navigateTo({
      // 	    url: '/pages/my/flyTraining?type="before"'
      // 	});
      // }else{
      // 	uni.navigateTo({
      // 	    url: '/pages/home/<USER>'+state
      // 	});
      // }

    },
    // 返回"我的"主页面
    backMyPage() {
      uni.switchTab({
        url: '/pages/home/<USER>'
      })
    },
    getCurrentTime() {
      var myDate = new Date();
      var y = myDate.getFullYear()
      var m = myDate.getMonth() + 1
      m = m < 10 ? '0' + m : m
      var d = myDate.getDate()
      d = d < 10 ? ('0' + d) : d
      this.currentTime = y + '-' + m + '-' + d;
    },
    async getData() {
      this.getCurrentTime()
      let param = {
        userId: uni.getStorageSync('userInfo').userId,
        date: this.currentTime
      };
      try {
        const res = await getMessageList(param);
        console.log(res.response.data)
        this.DataList = res.response.data;
        if (this.DataList.length == 0) {
          this.noData = true
        } else {
          this.noData = false
        }


      } catch (e) {
        console.error(e)
      }
      this.flagger = false
    },

    clearRmk() {
      this.inputValue = '';
    },
    //提交答题
    async submit(type) {
      let param = {
        flightSortiesId: this.flightSortiesId,
        verifyType: '3',
        verifyTime: this.getCurrentDate(),
        pass: type,
      };
      if (!type) {
        param.refuse = this.inputValue
      }
      try {
        const res = await getVerifySure(param);
        uni.showToast({
          title: res.response.msg
        })
        setTimeout(() => {
          uni.navigateBack({
            delta: 1,
          });
        }, 1500)
      } catch (e) {
        console.error(e)
      }
    }
  }
};
</script>

<style lang="less" scoped>

page {
  height: 100%;
}

.background {
  width: 100%;
  height: 100%;
  // position: fixed;
  background-size: 100% 100%;
  z-index: -1;
  position: absolute;
  top: 0px;
  bottom: 0px;
}

.nav {
  width: 100%;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
  z-index: 10;
}

.nav-title {
  width: 100%;
  height: inherit;
  position: relative;
  z-index: 10;
  font-family: SF Pro Text;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;

  .nav-title-text {
    // padding-top: 10px;
    font-family: SF Pro Text;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #000000;

    &:first-child {
      font-weight: normal;
      position: absolute;
      left: 25px;
      font-size: 22px;
    }
  }
}

.no-data-box {
  background: rgba(255, 255, 255, 0.64);
  height: 343px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .image {
    width: 140px;
    height: 140px;
  }

  .col-gray-500 {
    font-size: 14px;
  }
}

.scroll-view {
  height: calc(100vh - 135px) !important;
}

.main-container {
  padding: 0 16px;
  box-sizing: border-box;

  .data-warp {
    background-color: rgba(255, 255, 255, 0.64);
    border: 1px solid rgba(255, 255, 255, 0.72);
    border-radius: 8px;
  }
}

.refuse-rmk {
  padding: 12px 16px;
  height: 76px;
  background-color: rgba(255, 255, 255, 0.64);
  border: 1px solid rgba(255, 255, 255, 0.72);
  border-radius: 8px;

  .tips {
    color: #838791;
    font-size: 12px;
    line-height: 16px;
  }

  .content {
    height: 35px;
    position: relative;
    border-bottom: 1px solid #2c5de5;

    .rmk-input {
      height: 35px;
      line-height: 35;
    }

    .iconfont {
      color: #bec0c5;
      position: absolute;
      right: 0;
      top: 50%;
      z-index: 999;
      transform: translateY(-50%);
    }
  }
}

.footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  order: 0;
  padding: 0 16px;
  margin-bottom: 8px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 40px;
  color: #fff;

  .refuse {
    width: calc(50% - 4px);
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 4px;
    color: #fff;
    background: #e83f4e;
  }

  .pass {
    width: calc(50% - 4px);
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 4px;
    color: #fff;
    background: #2c5de5;
  }
}

.statistics {
  margin-bottom: 12px;
  padding: 0 16px;
  background-color: #fff;
  background: rgba(255, 255, 255, 0.64);
  border: 1px solid rgba(255, 255, 255, 0.72);
  border-radius: 8px;
}

.contentBox {
  float: left;

  .tops {
    font-size: 13px;
    color: #333;
  }

  .bottoms {
    font-size: 12px;
    color: #949393;
  }
}
</style>
