<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">飞前问答</text>
			</view>
		</view>
		<!-- 内容主体 -->
		<scroll-view class="scroll-view bg-gray overflow" :style="'height:calc(100vh - ' + navH + 'px)'" scroll-y>
			<view class="main-container">
				<fly-test-card :taskInfo="taskInfo" :taskIndex="taskIndex"></fly-test-card>
				<view class="container-title">
					<radio-choose :chooseList="chooseList" @getChildList="getDataList"></radio-choose>
				</view>
			</view>
		</scroll-view>
		<!-- 底部 -->
		<view class="footer" @click="submit">
			<view :class="['footer-container', {'submit-color': isSubmitColor}]">
				提交答题</view>
		</view>

		<uni-modal :modalInfo="modalInfo" @cancel="operation(1)" @confirm="operation(2)" v-show="showModal">
		</uni-modal>
		<!-- 整体背景放在最后 -->
		<view class="bg-main">
			<image class="background" src="../../static/images/background.png"></image>
		</view>
	</view>
</template>

<script>
	import {
		getRandomSubject,
		getFlightDetailById,
		uploadAnswer
	} from '../../api/weChat.js';
	const App = getApp();
	export default {
		data() {
			return {
				navH: 0,
				showModal: false,
				flightSortiesId: '',
				modalInfo: {
					content: '',
					type: '2' //1为答题合格，2为不合格。
				},
				taskIndex: 1,
				taskInfo: '',
				//题数据
				chooseList: [],
				answerList: [],
				componentData: '',
				isSubmitColor: false,
				update: true,
			};
		},
		mounted() {
			this.getData();
			this.getTestList();
		},
		onLoad: function(options) {
			this.flightSortiesId = options.flightSortiesId;
			this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
		},
		methods: {
			// 返回"我的"主页面
			backMyPage() {
				uni.navigateBack({
					delta: 1
				});
			},
			async getData() {
				let param = {
					flightSortiesId: this.flightSortiesId,
				};
				try {
					const res = await getFlightDetailById(param);
					this.taskInfo = res.response.data;
				} catch (e) {
					console.error(e)
				}
			},
			async getTestList() {
				let param = {
					flightSortiesId: this.flightSortiesId,
					questionNumber: 40,
				};
				try {
					const res = await getRandomSubject(param);
					this.chooseList = res.response.data.rows;
				} catch (e) {
					console.error(e)
				}
			},
			getDataList(data) {
				data = data.filter(val => val != ''); //去除空数组
				this.componentData = data;
				if (data.length != this.chooseList.length) {
					this.isSubmitColor = false;
				} else {
					this.isSubmitColor = true;
				}
			},
			//提交答题
			async submit() {
				if (this.componentData.length === this.chooseList.length) {
					this.answerList.length = this.componentData.length;
					this.componentData.forEach((val, index) => {
						this.answerList[index] = this.componentData[index];
					})
					let param = {
						flightSortiesId: this.flightSortiesId,
						isOnlyGrade: true,
						answerList: this.answerList
					}
					try {
						const res = await uploadAnswer(param)
						this.modalInfo.content = res.response.msg
						if (res.response.msg === '成绩合格') {
							this.modalInfo.type = '1';
						} else {
							this.modalInfo.type = '2';
						}
						this.showModal = true;
					} catch (e) {
						//TODO handle the exception
					}
				}
			},
			operation(id) {
				if (id === 1) {
					this.showModal = false;
					uni.navigateBack({
						delta: 1
					});
				} else {
					this.showModal = false;
					this.chooseList = [];
					this.answerList = [];
					this.componentData = '';
					this.getTestList();
					//重新答题
				}
			},
		}
	};
</script>

<style lang="less" scoped>
	.container {
		// position: relative;
		// height: 100vh;
	}

	page {
		height: 100%;
	}

	.background {
		width: 100%;
		height: 100%;
		// position: fixed;
		background-size: 100% 100%;
		z-index: -1;
		position: absolute;
		top: 0px;
		bottom: 0px;
	}

	.nav {
		width: 100%;
		overflow: hidden;
		position: relative;
		top: 0;
		left: 0;
		z-index: 10;
	}

	.nav-title {
		width: 100%;
		height: inherit;
		position: relative;
		z-index: 10;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 15px;

		.nav-title-text {
			font-family: SF Pro Text;
			font-style: normal;
			font-weight: 600;
			font-size: 16px;
			color: #000000;

			&:first-child {
				font-weight: normal;
				position: absolute;
				left: 25px;
				font-size: 22px;
			}
		}
	}

	.scroll-view {
		height: calc(100vh - 135px) !important;
	}

	.main-container {
		padding: 0 16px;
		box-sizing: border-box;

		.container-title {
			height: auto;
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			padding: 12px 16px;
			width: 100%;
			border: 1px solid rgba(255, 255, 255, 0.72);
			box-sizing: border-box;
			border-radius: 8px;
			background: rgba(255, 255, 255, 0.64);
		}
	}

	.footer {
		position: fixed;
		bottom: 0;
		width: 100%;
		order: 0;
		padding: 0 16px;
		margin-bottom: 8px;

		.footer-container {
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			width: 100%;
			height: 40px;
			border-radius: 4px;
			background: #ABCAFF;
			color: #fff;
		}

		.submit-color {
			background: #2C5DE5;
		}
	}
</style>
