<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">航班详情</text>
			</view>
		</view>
		<!-- 内容主体 -->
		<view class="content" :style="'height:calc(100vh - ' + navH + 'px)'">
			<view class="box">
				<view class="leftBox">计划起飞时间</view>
				<view class="rightBox">{{taskInfo.flightSorties.planDepartTime}}</view>
			</view>
			<view class="box">
				<view class="leftBox">计划到达时间</view>
				<view class="rightBox">{{taskInfo.flightSorties.planArriveTime}}</view>
			</view>
			<view class="box">
				<view class="leftBox">机型</view>
				<view class="rightBox">{{taskInfo.flightSorties.aircraftStyle}}</view>
			</view>
			<view class="box">
				<view class="leftBox">机尾号</view>
				<view class="rightBox">{{taskInfo.flightSorties.aircraftTailNo}}</view>
			</view>
			<view class="box">
				<view class="leftBox">飞行架次</view>
				<view class="rightBox">{{taskInfo.flightSorties.flightFrequency}}</view>
			</view>
			<view class="box">
				<view class="leftBox">滑行开始时间</view>
				<view class="rightBox">{{taskInfo.flightSorties.slideStartTime}}</view>
			</view>
			<view class="box">
				<view class="leftBox">起飞时间</view>
				<view class="rightBox">{{taskInfo.flightSorties.flyStartTime}}</view>
			</view>
			<view class="box">
				<view class="leftBox">着陆时间</view>
				<view class="rightBox">{{taskInfo.flightSorties.flyEndTime}}</view>
			</view>
			<view class="box">
				<view class="leftBox">滑行结束时间</view>
				<view class="rightBox">{{taskInfo.flightSorties.slideEndTime}}</view>
			</view>
			<view class="box">
				<view class="leftBox">空中飞行时间</view>
				<view class="rightBox">{{taskInfo.flightSorties.flyTime == null ? '' : taskInfo.flightSorties.flyTime}}分钟</view>
			</view>
			<view class="box">
				<view class="leftBox">飞行时间(含滑行)</view>
				<view class="rightBox">{{taskInfo.flightSorties.slideTime  == null ? '' : taskInfo.flightSorties.slideTime}}分钟</view>
			</view>
			<view class="box">
				<view class="leftBox">夜航时间</view>
				<view class="rightBox">{{taskInfo.flightSorties.nightFlyTime}}</view>
			</view>
			<view class="box">
				<view class="leftBox">剩余燃油</view>
				<view class="rightBox">{{taskInfo.flightSorties.fuelExpend == null ? '' : taskInfo.flightSorties.fuelExpend}}L</view>
			</view>
			<view class="box">
				<view class="leftBox">旅客人数</view>
				<view class="rightBox">{{taskInfo.flightSorties.passengerNumber}}</view>
			</view>
			<view class="box">
				<view class="leftBox">航班延误</view>
				<view class="rightBox">{{taskInfo.flightSorties.flightDelay == 0 ? '未延误': '延误' }}</view>
			</view>
			<view class="box">
				<view class="leftBox">延误原因</view>
				<view class="rightBox">{{taskInfo.flightSorties.delayReason}}</view>
			</view>
			<!-- 返程 -->
			<view class="" v-show='taskInfo.flightSorties.routeType==1'>
				<view class="box">
					<view class="leftBox">返程滑行开始时间</view>
					<view class="rightBox">{{taskInfo.flightSorties.secondSlideStartTime}}</view>
				</view>
				<view class="box">
					<view class="leftBox">返程起飞时间</view>
					<view class="rightBox">{{taskInfo.flightSorties.secondFlyStartTime}}</view>
				</view>
				<view class="box">
					<view class="leftBox">返程着陆时间</view>
					<view class="rightBox">{{taskInfo.flightSorties.secondFlyEndTime}}</view>
				</view>
				<view class="box">
					<view class="leftBox">返程滑行结束时间</view>
					<view class="rightBox">{{taskInfo.flightSorties.secondSlideEndTime}}</view>
				</view>
				<view class="box">
					<view class="leftBox">返程空中飞行时间</view>
					<view class="rightBox">{{taskInfo.flightSorties.secondFlyTime == null ? '' : taskInfo.flightSorties.secondFlyTime}}分钟</view>
				</view>
				<view class="box">
					<view class="leftBox">返程飞行时间(含滑行)</view>
					<view class="rightBox">{{taskInfo.flightSorties.secondSlideTime  == null ? '' : taskInfo.flightSorties.secondSlideTime}}分钟</view>
				</view>
				<view class="box">
					<view class="leftBox">返程夜航时间</view>
					<view class="rightBox">{{taskInfo.flightSorties.secondNightFlyTime}}</view>
				</view>
				<view class="box">
					<view class="leftBox">返程剩余燃油</view>
					<view class="rightBox">{{taskInfo.flightSorties.secondFuelExpend == null ? '' : taskInfo.flightSorties.secondFuelExpend}}L</view>
				</view>
				<view class="box">
					<view class="leftBox">返程旅客人数</view>
					<view class="rightBox">{{taskInfo.flightSorties.secondPassengerNumber}}</view>
				</view>
				<view class="box">
					<view class="leftBox">返程航班延误</view>
					<view class="rightBox">{{taskInfo.flightSorties.secondFlightDelay == 0 ? '未延误': '延误' }}</view>
				</view>
				<view class="box">
					<view class="leftBox">返程延误原因</view>
					<view class="rightBox">{{taskInfo.flightSorties.secondDelayReason}}</view>
				</view>
			</view>
			<view class="box">
				<view class="leftBox">任务书</view>
				<view class="rightBox" style="width: calc(100% - 150px);height: 20px;">
					<image style="height: 20px;width: 30px;" v-for="(itemx,indexx) in taskInfo.flightSorties.assignmentFileUrl" :key='indexx' @click='previews(taskInfo.flightSorties.assignmentFileUrl)'
					 :src="itemx"></image>
					<!-- <image @click="previews(taskInfo.flightSorties.uploadAssignmentFileUrl)" style="width: 100%;height: 100%;" :src="taskInfo.flightSorties.uploadAssignmentFileUrl" mode=""></image> -->
				</view>
			</view>
			<view class="box">
				<view class="leftBox">气象资料</view>
				<view class="rightBox" style="width: calc(100% - 150px);height: 20px;">
					<image style="height: 20px;width: 30px;" v-for="(itemx,indexx) in taskInfo.flightSorties.weatherFileUrl" :key='indexx' 
						@click='previews(taskInfo.flightSorties.weatherFileUrl)' :src="itemx"></image>
				</view>
			</view>
			<view class="box">
				<view class="leftBox">航行通告</view>
				<view class="rightBox" style="width: calc(100% - 150px);height: 20px;">
					<image style="height: 20px;width: 30px;" v-for="(itemx,indexx) in taskInfo.flightSorties.sailingNoticeFileUrl" :key='indexx' 
						@click='previews(taskInfo.flightSorties.sailingNoticeFileUrl)' :src="itemx"></image>
				</view>
			</view>
			<view class="box">
				<view class="leftBox">放行单</view>
				<view class="rightBox" style="width: calc(100% - 150px);height: 20px;">
					<image style="height: 20px;width: 30px;" v-for="(itemx,indexx) in taskInfo.flightSorties.releasePermitFileUrl" :key='indexx' 
						@click='previews(taskInfo.flightSorties.releasePermitFileUrl)' :src="itemx"></image>
				</view>
			</view>
			<view class="box">
				<view class="leftBox">电子舱单</view>
				<view class="rightBox" style="width: calc(100% - 150px);height: 20px;">
					<image style="height: 20px;width: 30px;" v-for="(itemx,indexx) in taskInfo.flightSorties.missionStatementFileUrl" :key='indexx' 
						@click='previews(taskInfo.flightSorties.missionStatementFileUrl)' :src="itemx"></image>
				</view>
			</view>
			<view class="box">
				<view class="leftBox">维修记录本</view>
				<view class="rightBox" style="width: calc(100% - 150px);height: 20px;">
					<image style="height: 20px;width: 30px;" v-for="(itemx,indexx) in taskInfo.flightSorties.bigBookFileUrl" :key='indexx' 
						@click='previews(taskInfo.flightSorties.bigBookFileUrl)' :src="itemx"></image>
				</view>
			</view>
			
			
			
			
			<view class="no-data-box" v-if="noData">
				<image class="image" src="../../static/images/nodata.png" mode="widthFix"></image>
				<text class="col-gray-500">当前无数据</text>
			</view>
		</view>
		
		<!-- 整体背景放在最后 -->
		<view class="bg-main"><image class="background" src="../../static/images/background.png"></image></view>
	</view>
</template>

<script>
	import { getFlight} from '../../api/weChat.js';

const App = getApp();
export default {
	data() {
		return {
			navH: 0,
			timer: '',
			taskIndex: 1,
			inputValue: '',
			taskInfo: {
				flightSorties:{
					releaseStatus:'',
					flyTime:'',
				    slideTime:''
				},
				
			},
			componentData: '',
			flightSortiesId:'',
			imgUrl:'',
			flightPlanId:''
			
		};
	},
	mounted() {
		
	},
	onLoad: function(options) {
		console.log(options)
		this.flightPlanId = options.flightSortiesId;
		this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
		this.getData();
	},
	onShow: function(options) {
		// uni.setStorageSync('flightSortiesId', this.flightSortiesId);
	},
	methods: {
		previews(item){
			uni.previewImage({
				urls:item
			})
		},
		backStatus(type){
			if(type == 0){
				return '未查看'
			}else if(type==1){
				return '同意'
			}else if(type ==2){
				return '拒绝'
			}else if(type == 3){
				return '班次取消'
			}
		},
		backFlag(type){
			if(type == 0){
				return '否'
			}else if(type==1){
				return '是'
			}else if(type ==2){
				return '其他'
			}
		},
		
		// 返回"我的"主页面
		backMyPage() {
			uni.navigateBack({
				 delta:1
			});
		},
		
		async getData() {
			let param = {
				flightPlanId: this.flightPlanId,
			};
			try {
				const res = await getFlight(param);
				console.log(res)
				this.taskInfo.flightSorties = res.response.data;
			} catch (e) {
				console.error(e)
			}
		},
		
		clearRmk() {
			this.inputValue = '';
		},
		
	}
};
</script>

<style lang="less" scoped>
.content{
	overflow: auto;
	.box{
		font-size: 13px;
		padding: 10px 10px 10px 30px;
		background-color: #ffffff85;
		border-bottom: 1px solid #efefef;
		.leftBox{
			width: 150px;
			display: inline-block;
		}
		.rightBox{
			display: inline-block;
		}
	}
}
page {
	height: 100%;
}

.background {
	width: 100%;
	height: 100%;
	// position: fixed;
	background-size: 100% 100%;
	z-index: -1;
	position: absolute;
	top: 0px;
	bottom: 0px;
	z-index: -1;
}

.nav {
	width: 100%;
	overflow: hidden;
	position: relative;
	top: 0;
	left: 0;
	z-index: 10;
}

.nav-title {
	width: 100%;
	height: inherit;
	position: relative;
	z-index: 10;
	font-family: SF Pro Text;
	font-style: normal;
	font-weight: 600;
	font-size: 16px;
	color: #000000;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 15px;

	.nav-title-text {
		// padding-top: 10px;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;

		&:first-child {
			font-weight: normal;
			position: absolute;
			left: 25px;
			font-size: 22px;
		}
	}
}

</style>
