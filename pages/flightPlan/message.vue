<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">机组信息</text>
			</view>
		</view>
		<!-- 内容主体 -->
		<view class="content" :style="'height:calc(100vh - ' + navH + 'px)'" style="overflow: auto;">
			<message-list v-for="(item, index) in dateList" :key="index" :taskIndex="index"
				:taskInfo="item" :types='1' :flight='1'>
			</message-list>
			
		</view>
		<!-- 整体背景放在最后 -->
		<view class="bg-main"><image class="background" src="../../static/images/background.png"></image></view>
	</view>
</template>

<script>
	import { getUserAprrol,getFlightPlanSingle,updateFlightPlan,emergencyList,prepareForFlightplanAll} from '../../api/weChat.js';

const App = getApp();
export default {
	data() {
		return {
			navH: 0,
			timer: '',
			taskIndex: 1,
			inputValue: '',
			taskInfo: '',
			componentData: '',
			flightSortiesId:'',
			imgUrl:'',
			form:{},
			list:[],
			taskData:[],
			dateList:[]
		};
	},
	mounted() {
		this.getData3()
	},
	onLoad: function(options) {
		this.flightSortiesId = options.flightSortiesId;
		this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
	},
	methods: {
		async getData3(type) {
		    let homeParam = {
		        flightPlanId : this.flightSortiesId,
		    };
			console.log(homeParam)
		    try {
		        const res = await getUserAprrol(homeParam);
		        // this.taskData = res.response.data.flightplanList;
		        this.dateList = res.response.data
		        
		    } catch (e) {
		        console.error(e)
		    }
		},
		
		// 返回"我的"主页面
		backMyPage() {
			uni.navigateBack({
				 url: '/pages/home/<USER>'
			});
		},
		
	}
};
</script>

<style lang="less" scoped>

page {
	height: 100%;
}

.background {
	width: 100%;
	height: 100%;
	// position: fixed;
	background-size: 100% 100%;
	z-index: -1;
	position: absolute;
	top: 0px;
	bottom: 0px;
}

.nav {
	width: 100%;
	overflow: hidden;
	position: relative;
	top: 0;
	left: 0;
	z-index: 10;
}

.nav-title {
	width: 100%;
	height: inherit;
	position: relative;
	z-index: 10;
	font-family: SF Pro Text;
	font-style: normal;
	font-weight: 600;
	font-size: 16px;
	color: #000000;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 15px;

	.nav-title-text {
		// padding-top: 10px;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;

		&:first-child {
			font-weight: normal;
			position: absolute;
			left: 25px;
			font-size: 22px;
		}
	}
}

.uni-column{
	    background: #ffffff86;
		line-height: 30px;
		padding: 10px 20px;
		font-size: 13px;

}
.content{
	padding: 10px;
}
textarea{
	    border: 1px solid #efefef;
	    width: 100%;
	    padding: 10px;
	    box-sizing: border-box;
}
</style>
