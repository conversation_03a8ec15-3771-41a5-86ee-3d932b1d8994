<template>
  <view class="warp" style="background: transparent;">
    <view class="nav bg-white" :style="'height:' + navH + 'px'">
      <view class="nav-title">
        <text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
        <text class="nav-title-text">班次</text>
      </view>
    </view>
    <view class="" :style="'height:calc(100vh - ' + navH + 'px)'">
      <view class="main" style="border-bottom: 1px solid #e4e4e4;" v-for="(item,index) in list" :key='index'
            @click="goDetail(item.flightsortiesId)">
        <!-- <span class="index">{{taskIndex + 1}}</span> -->
        <view class="trip-address flex-row">
          <image class='trip-bar' src="../../static/images/tripBar.png"></image>
          <view class="flex-col justify-between">
            <view>
              <p class="fz-16 col-gray-500">起飞时间：{{ item.planDepartTime }}</p>
            </view>
            <view>
              <p class="fz-12 col-gray-500">到达时间：{{ item.planArriveTime }}</p>
            </view>
          </view>
        </view>

      </view>
      <view class="no-data-box" v-if="noData">
        <image class="image" src="../../static/images/nodata.png" mode="widthFix"></image>
        <text class="col-gray-500">当前无数据</text>
      </view>
    </view>
    <image class="background" src="../../static/images/background.png"></image>
  </view>
</template>

<script>
import {getFlightDetail} from '../../api/weChat.js';

const App = getApp();
export default {
  name: "Filght",
  props: {
    taskInfo: {
      type: Object,
    },
    taskIndex: {
      type: Number
    }
  },
  data() {
    return {
      showMore: false,
      navH: 0,
      flightplanId: '',
      list: [],
      noData: false
    }
  },
  onLoad(options) {
    this.flightplanId = options.flightplanId;
    this.getData()
    console.log(App.globalData.navHeight)
    this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
  },

  methods: {
    goDetail(item) {
      uni.navigateTo({
        url: '/pages/flightPlan/detail?flightSortiesId=' + item
      });
    },
    changeShow() {
      this.showMore = !this.showMore;
    },
    backMyPage() { //飞前问答
      uni.navigateTo({
        url: '/pages/flightPlan/index'
      });
    },
    async getData(type) {
      let homeParam = {
        flightplanId: this.flightplanId
      };
      try {
        const res = await getFlightDetail(homeParam);
        // this.homeInfo.lastUpdateTime = res.response.data.lastUpdateTime
        // this.homeInfo.total = res.response.data.total
        // this.taskData = res.response.data.flightplanList;
        this.list = res.response.data
        if (this.list.length == 0) {
          this.noData = true
        } else {
          this.noData = false;
        }
      } catch (e) {
        console.error(e)
      }
      console.log(this.flag)
    },

  }
}
</script>

<style lang="less" scoped>
.warp {
  position: relative;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.64);
  border: 1px solid rgba(255, 255, 255, 0.72);
  border-radius: 8px;
  margin-bottom: 12px;
}

.plane {
  width: 190px;
  position: absolute;
  top: 0;
  right: 0;
}

.main {
  padding: 24rpx 32rpx 0 32rpx;
  height: 110px;
  background-color: rgba(0, 0, 0, 0);

  .header {
    padding-right: 84rpx;
    height: 16px;

    .index {
      width: 16px;
      height: 16px;
      line-height: 16px;
      text-align: center;
      margin-right: 13px;
      border-radius: 50%;
      color: #838791;
      background-color: #EAEAEB;
      display: inline-block;
    }

    .marRight4 {
      margin-right: 8rpx;
    }

    .marRight33 {
      margin-right: 66rpx;
    }
  }

  .trip-address {
    margin-top: 8px;
    margin-bottom: 12px;

    .fz-16 {
      font-weight: 800;
    }
  }

  .trip-bar {
    width: 16px;
    height: 60px;
    margin-right: 12px;
  }
}

.detail-row {
  padding: 12px 16px;
  display: flex;
  width: 100%;
  backdrop-filter: blur(16px);
  justify-content: space-between;
}

.show-view {
  height: 330px;
  transition: .5s;
  backdrop-filter: blur(16px);
}

.close-view {
  height: 0;
  transition: .5s;
  overflow: hidden;
}

.detail-block {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: top;
  align-items: center;
  position: relative;

  .phone {
    position: absolute;
    bottom: -17px;
    font-size: 12px;
    color: #2C5DE5;
  }

  .icon-phone-fill {
    font-size: 12px !important;
  }

  .fz-14 {
    font-weight: 600;
  }
}

//机型机号等样式调整
.no-fold {
  padding-bottom: 12px;
}

.fold {
  padding: 0 16px;

  .detail-row {
    padding: 0;

    &:not(:last-child) {
      margin-bottom: 12px;
    }

    &:last-child {
      padding-top: 12px;
    }

    .fz-14 {
      margin-top: 4px;
    }

    &:nth-of-type(2) {
      .fz-14 {
        margin-bottom: 4px;
      }
    }
  }
}

.handel-section {
  width: 100%;
  padding: 12px 0;
  display: flex;
  flex-wrap: wrap;

  .handle-block {
    padding: 8px 10.5px;
    width: calc(50% - 8px);
    height: 60px;
    border: 1px solid #D9DADD;
    border-radius: 4px;
    display: flex;
    margin-bottom: 12px;
  }

  .handle-block:nth-child(odd) {
    margin-right: 16px;
  }

  .status {
    height: 20px;
    line-height: 20px;
    text-align: center;
    border-radius: 50px;
    width: fit-content;
    padding: 0 8px;
  }

  .complete { //已完成的状态
    background: #E9FBF1;
    color: #5BBC72;
    border: 1px solid #D6F3E2;
  }

  .incomplete { //未完成的状态
    background: #F6F6F6;
    color: #50545E;
    border: 1px solid #D9DADD;
  }

  .refuse { //已拒绝状态
    background: #FFEFF0;
    color: #E83F4E;
    border: 1px solid #FFD6D7;
  }
}

.arrow {
  height: 20px;
  text-align: center;
  backdrop-filter: blur(16px);
}

.nav {
  width: 100%;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
  z-index: 10;
}

.nav-title {
  width: 100%;
  height: inherit;
  position: relative;
  z-index: 10;
  font-family: SF Pro Text;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;

  .nav-title-text {
    // padding-top: 10px;
    font-family: SF Pro Text;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #000000;

    &:first-child {
      font-weight: normal;
      position: absolute;
      left: 25px;
      font-size: 22px;
    }
  }
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.no-data-box {
  background: rgba(255, 255, 255, 0.64);
  height: 343px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .image {
    width: 140px;
    height: 140px;
  }

  .col-gray-500 {
    font-size: 14px;
  }
}
</style>
