<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">个人信息</text>
			</view>
		</view>
		<!-- 内容主体 -->
		<view class="content" :style="'height:calc(100vh - ' + navH + 'px)'" style="overflow: auto;">
			<form @submit="submit" >
			<view class="" >
				<view class="uni-form-item uni-column">
					<view class="title">执照有效期截至日期:</view>
					<picker class="time-picker" mode="date" :value="form.validityOfLicense" @change="bindTimeChange($event, 1)">
						<text class="iconfont icon-calendar-todo-fill"></text>
						<view class="time" style="display: inline;">{{ form.validityOfLicense }}</view>
					</picker>
				</view>
				<view class="uni-form-item uni-column">
					<view class="title">熟练检查有效期截至日期:</view>
					<picker class="time-picker" mode="date" :value="form.proficiencyCheckValidity "  @change="bindTimeChange($event, 2)">
						<text class="iconfont icon-calendar-todo-fill"></text>
						<view class="time" style="display: inline;">{{ form.proficiencyCheckValidity }}</view>
				</picker>
				</view>	
				
				<view class="uni-form-item uni-column">
					<view class="title">仪表熟练检查有效期截至日期:</view>
					<picker class="time-picker" mode="date" :value="form.validityPeriodOfInstrumentProficiencyInspection "  @change="bindTimeChange($event, 4)">
						<text class="iconfont icon-calendar-todo-fill"></text>
						<view class="time" style="display: inline;">{{ form.validityPeriodOfInstrumentProficiencyInspection }}</view>
					</picker>
				</view>
				<view class="uni-form-item uni-column">
					<view class="title">体检合格证有效期截至日期:</view>
					<picker class="time-picker" mode="date" :value="form.validityPeriodOfPhysicalExamination "  @change="bindTimeChange($event, 5)">
						<text class="iconfont icon-calendar-todo-fill"></text>
						<view class="time" style="display: inline;">{{ form.validityPeriodOfPhysicalExamination }}</view>
					</picker>
				</view>
				<view class="uni-form-item uni-column">
					<view class="title">航线练检查有效期截止日期:</view>
					<picker class="time-picker" mode="date" :value="form.validityPeriodOfRouteTrainingInspection "  @change="bindTimeChange($event, 6)">
						<text class="iconfont icon-calendar-todo-fill"></text>
						<view class="time" style="display: inline;">{{ form.validityPeriodOfRouteTrainingInspection }}</view>
					</picker>
				</view>
				<view class="uni-form-item uni-column">
					<view class="title">危险品训练有效期截止日期:</view>
					<picker class="time-picker" mode="date" :value="form.dangerTrainingInspection"  @change="bindTimeChange($event, 7)">
						<text class="iconfont icon-calendar-todo-fill"></text>
						<view class="time" style="display: inline;">{{ form.dangerTrainingInspection }}</view>
					</picker>
				</view>
				<view class="uni-form-item uni-column">
					<view class="title">空勤登记证有效期截止日期:</view>
					<picker class="time-picker" mode="date" :value="form.validityOfRegistrationCertificate "  @change="bindTimeChange($event, 3)">
						<text class="iconfont icon-calendar-todo-fill"></text>
						<view class="time" style="display: inline;">{{ form.validityOfRegistrationCertificate }}</view>
					</picker>
				</view>
				<view class="uni-form-item uni-column">
					<view class="title">Y12放行授权有效期截止日期:</view>
					<picker class="time-picker" mode="date" :value="form.y12Inspection"  @change="bindTimeChange($event, 8)">
						<text class="iconfont icon-calendar-todo-fill"></text>
						<view class="time" style="display: inline;">{{ form.y12Inspection }}</view>
					</picker>
				</view>
				<view class="uni-form-item uni-column">
					<view class="title">C208放行授权有效期截止日期:</view>
					<picker class="time-picker" mode="date" :value="form.c208Inspection"  @change="bindTimeChange($event, 9)">
						<text class="iconfont icon-calendar-todo-fill"></text>
						<view class="time" style="display: inline;">{{ form.c208Inspection }}</view>
					</picker>
				</view>
				<view class="uni-form-item uni-column">
					<view class="title">B300放行授权有效期截止日期:</view>
					<picker class="time-picker" mode="date" :value="form.b300Inspection"  @change="bindTimeChange($event, 10)">
						<text class="iconfont icon-calendar-todo-fill"></text>
						<view class="time" style="display: inline;">{{ form.b300Inspection }}</view>
					</picker>
				</view>
				<!-- <view class="uni-form-item uni-column">
					<view class="title">上传舱单:</view>
					<button type="default" size="mini" @click='uploadFiles'>上传图片</button>
					<image @click='previews(path)' v-show="path != ''" :src="path" mode=""></image>
				</view> -->
				
			</view>
			
			<view class="uni-btn-v" >
				<button type="primary" plain="true" form-type="submit" style="margin-bottom: 10px;">提交</button>
				<!-- <button type="primary" plain="true" @click='test' style="margin-bottom: 10px;">测试订阅消息</button> -->
				<!-- <button form-type="submit">Submit</button> -->
				<!-- <button type="default" form-type="reset" >清空</button> -->
			</view>
			</form>
		</view>
		<!-- 整体背景放在最后 -->
		<view class="bg-main"><image class="background" src="../../static/images/background.png"></image></view>
	</view>
</template>

<script>
	import {getFlight,updateUser,getUser} from '../../api/weChat.js';

const App = getApp();
export default {
	data() {
		return {
			navH: 0,
			flightSortiesId:'',
			imgUrl:'',
			state:'',
			userRole:'',
			form:{
				validityPeriodOfRouteTrainingInspection:"",
				validityPeriodOfPhysicalExamination:"",
				validityPeriodOfInstrumentProficiencyInspection:"",
				validityOfRegistrationCertificate:"",
				proficiencyCheckValidity:"",
				validityOfLicense:"",
				dangerTrainingInspection:'',
				y12Inspection:'',
				c208Inspection:'',
				b300Inspection:''
				
				
			},
			currentTime:'',
			taskData:[],
			path:''
		};
	},
	mounted() {
		// this.getCurrentTime()
	},
	onLoad: function(options) {
		this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
		// this.userRole = uni.getStorageSync('userRole')
		this.getData2()
	},
	methods: {
		
		// previews(item){
		// 	uni.previewImage({
		// 		urls:[item]
		// 	})
		// },
		
		async getData2(type) {
		    let homeParam = {
		        userId :uni.getStorageSync('userInfo').userId
		    };
			console.log(homeParam)
		    try {
		        const res = await getUser(homeParam);
		        // this.taskData = res.response.data.flightplanList;
		        this.form = res.response.data
		        
		    } catch (e) {
		        console.error(e)
		    }
		},
		
		// 时间
		bindTimeChange(e,id){
			if (id == 1) {
				this.form.validityOfLicense = e.target.value;
			}
			if (id == 2) {
				this.form.proficiencyCheckValidity = e.target.value;
			}
			if (id == 3) {
				this.form.validityOfRegistrationCertificate = e.target.value;
			}
			if (id == 4) {
				this.form.validityPeriodOfInstrumentProficiencyInspection = e.target.value;
			}
			if (id == 5) {
				this.form.validityPeriodOfPhysicalExamination = e.target.value;
			}
			if (id == 6) {
				this.form.validityPeriodOfRouteTrainingInspection = e.target.value;
			}
			if(id == 7){
				this.form.dangerTrainingInspection = e.target.value;
			}
			if(id == 8){
				this.form.y12Inspection = e.target.value 
			}
			if(id == 9){
				this.form.c208Inspection = e.target.value 
			}
			if(id == 10){
				this.form.b300Inspection = e.target.value
			}
		},
		// 返回"我的"主页面
		backMyPage() {
			uni.navigateBack({
				 url: '/pages/home/<USER>'
			});
		},
	
		//提交答题
		async submit(type) {
			let that = this
			that.form.userId = uni.getStorageSync('userInfo').userId
			try {
				const res = await updateUser(that.form);
				uni.showToast({
					title: res.response.msg
				})
				// setTimeout(()=>{
				// 	uni.navigateBack({
				// 	    delta: 1,
				// 	});
				// },1500)
			} catch (e) {
				console.error(e)
			}
			
		}
	}
};
</script>

<style lang="less" scoped>

page {
	height: 100%;
}

.background {
	width: 100%;
	height: 100%;
	// position: fixed;
	background-size: 100% 100%;
	z-index: -1;
	position: absolute;
	top: 0px;
	bottom: 0px;
}

.nav {
	width: 100%;
	overflow: hidden;
	position: relative;
	top: 0;
	left: 0;
	z-index: 10;
}

.nav-title {
	width: 100%;
	height: inherit;
	position: relative;
	z-index: 10;
	font-family: SF Pro Text;
	font-style: normal;
	font-weight: 600;
	font-size: 16px;
	color: #000000;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 15px;

	.nav-title-text {
		// padding-top: 10px;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;

		&:first-child {
			font-weight: normal;
			position: absolute;
			left: 25px;
			font-size: 22px;
		}
	}
}

.uni-column{
	    background: #ffffff86;
		line-height: 30px;
		padding: 10px 20px;
		font-size: 13px;

}
.content{
	padding: 10px;
}
textarea{
	    border: 1px solid #efefef;
	    width: 100%;
	    padding: 10px;
	    box-sizing: border-box;
}
</style>
