<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class='nav bg-white' :style="'height:'+navH+'px'">
			<view class='nav-title'>
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">操作记录</text>
			</view>
		</view>
		<!-- 内容主体 -->
		<scroll-view class='bg-gray overflow' :style="'height:calc(100vh - '+navH+'px)'" scroll-y>
			<view class="main-container">
				<time-line :recordList="recordList"></time-line>
			</view>
			<view class="no-data-box" v-if="noData">
				<image class="image" src="../../static/images/nodata.png" mode="widthFix"></image>
				<text class="col-gray-500">当前无数据</text>
			</view>
		</scroll-view>
		<!-- 整体背景放在最后 -->
		<view class='bg-main'>
			<image class='background' src="../../static/images/background.png"></image>
		</view>
	</view>
</template>

<script>
	const App = getApp();
	import {
		getMyFlightLog
	} from '../../api/weChat.js';
	export default {
		data() {
			return {
				noData:false,
				navH: 0,
				recordList: [],
				timeYear: "",
				timeHour: "",
				timeLineHeight: "80px"
			}
		},
		onLoad: function(options) {
			this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
			this.timeFormat1(this.recordList)
			this.getRecordList();
		},
		methods: {
			//操作记录数据
			async getRecordList() {
				const data = await getMyFlightLog();
				this.recordList = data.response.data;
				if(this.recordList == null || this.recordList.length == 0){
					this.noData = true
				}else{
					this.noData = false
				}
			},
			// 返回"我的"主页面
			backMyPage() {
				uni.switchTab({
					url: '/pages/my/index'
				})
			},
			timeFormat1(list) {
				list.forEach(item => {
					let time = item.time;
					this.timeYear = time.split('').splice(0, 10).join('');
					this.timeHour = time.split('').splice(11).join('')
				})
			},
		}
	}
</script>

<style lang="less" scoped>
	page {
		height: 100%;
	}

	.background {
		width: 100%;
		height: 100%;
		// position: fixed;
		background-size: 100% 100%;
		z-index: -1;
		position: absolute;
		top: 0px;
		bottom: 0px;
	}

	.nav {
		width: 100%;
		overflow: hidden;
		position: relative;
		top: 0;
		left: 0;
		z-index: 10;
	}

	.nav-title {
		width: 100%;
		height: inherit;
		position: absolute;
		z-index: 10;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 15px;

		.nav-title-text {
			// padding-top: 10px;
			font-family: SF Pro Text;
			font-style: normal;
			font-weight: 600;
			font-size: 16px;
			color: #000000;

			&:first-child {
				font-weight: normal;
				position: absolute;
				left: 25px;
				font-size: 22px;
			}
		}
	} 
	.main-container {
		padding: 0 16px;
	}
</style>
