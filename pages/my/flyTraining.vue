<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class='nav bg-white' :style="'height:'+navH+'px'">
			<view class='nav-title'>
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">飞行训练答题</text>
			</view>
		</view>
		<view class="" v-show='styleFlag'>
			<scroll-view class='scroll-view bg-gray overflow' :style="'height:calc(100vh - '+navH+'px)'" scroll-y>
				<view class="tipsbox">
					请先选择机型后进行答题
				</view>
				<view class="tipsbox name" v-for="(item,index) in stylelist" :key=index @click='chooseStyle(item)'>
					{{item.aircraftStyle}}
				</view>
			</scroll-view>
		</view>
		<!-- 内容主体 -->
		<view class="" v-show='!styleFlag'>
			<scroll-view class='scroll-view bg-gray overflow' :style="'height:calc(100vh - '+navH+'px)'" scroll-y>
				<view class="main-container">
					<view class="container-title1" v-if="!showComponent">
						<image class="container-title1-img" :src="theImg" mode="aspectFit"></image>
						<view class="textExplain">
							<view class="textExplain-view">
								你的得分为
								<text class="textExplain-view-text" style="color: #5BBC72"
									:class="{'text-color': isTextColor}">{{grade}}</text>
								分
							</view>
							<text class="textExplain-text">题目解析如下</text>
						</view>
					</view>
					<view class="container-title">
						<radio-choose :chooseList="chooseList" @getChildList="getDataList" v-if="showComponent">
						</radio-choose>
						<radio-result :chooseList="chooseList1" v-else></radio-result>
					</view>
				</view>
			</scroll-view>
			<!-- 底部 -->
			<view class="footer">
				<view class="footer-container" @click="submit"   
					:class="{ 'submit-disable-color': isDisable , 'submit-able-color': isAble}" v-if="showComponent">
					提交答题
				</view>
				<view class="footer-container1" v-else>
					<text v-if='grade == 100' class="footer-container1-text" style="width: 100%;" @click="backMyPage">完成答题</text>
					<text v-else='grade != 100' class="footer-container1-text" style="width: 100%;" @click="goTrain">重新答题</text>
				</view>
			</view>
		</view>

		<!-- 整体背景放在最后 -->
		<view class='bg-main'>
			<image class='background' src="../../static/images/background.png"></image>
		</view>
	</view>
</template>

<script>
	// cons
	const App = getApp();
	import {
		getAircraftStyle,
		getQuestion,
		uploadAnswerOnly
	} from '../../api/weChat.js'
	export default {
		data() {
			return {
				navH: 0,
				//题数据
				styleFlag:true,
				stylelist:[],
				chooseList: [],
				componentData: '',
				isDisable: true,
				isAble: false,
				showComponent: true,

				//详情数据
				answerList: [], //答题参数
				grade: 0,
				chooseList1: [],
				theImg: "../../static/images/submit_success.png",
				isTextColor: false,
				type:''
			}
		},
		onLoad: function(options) {
			this.type = options.type
			this.flightplanId = options.flightplanId
			console.log(options)
			this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
			this.getAircraftStyle()
			//匹配选项
		},
		methods: {
			async getAircraftStyle() {
				try {
					const data = await getAircraftStyle();
					this.stylelist = data.response.data;
				} catch (e) {
					//TODO handle the exception
				}
			},
			// 选择机型
			chooseStyle(item){
				this.getQuestionBank(item.aircraftStyle); //获得题库
				console.log(item.aircraftStyle)
			},
			//题库数据
			async getQuestionBank(aircraftStyle) {
				let param = {
					aircraftStyle:aircraftStyle
				}
				try {
					const data = await getQuestion(param);
					this.styleFlag = false
					this.chooseList = data.response.data.rows;
				} catch (e) {
					//TODO handle the exception
				}
			},
			// 返回"我的"主页面
			backMyPage() {
				// uni.switchTab({
				// 	url: '/pages/my/index'
				// })
				uni.navigateBack({
					delta: 1
				});
				
			},
			getDataList(data) {
				this.componentData = data;
				var numbesArr = []
				data = data.filter((val,num)=>{
					numbesArr.push(num)
				}); //去除空数组
				if (numbesArr.length != this.chooseList.length) {
					this.isDisable = true;
					this.isAble = false;
				} else {
					this.isDisable = false;
					this.isAble = true;
				}
			},
			//提交答题
			async submit() {
				if (!this.isDisable) {
					this.answerList.length = this.componentData.length;
					this.componentData.forEach((val, index) => {
						this.answerList[index] = this.componentData[index];
					})
					let param = {
						isOnlyGrade: false,
						answerList: this.answerList,
						flightplanId: this.flightplanId,
					}
					try {
						const data = await uploadAnswerOnly(param);
						console.log(8888, data)
						this.chooseList1 = data.response.data.qBankAndAnswerPOList;
						this.grade = data.response.data.grade;
						this.grade = this.grade.toFixed(2);
						this.handleChoose(this.chooseList1); //拿到数据控制分数颜色、图片
						this.showComponent = false;
					} catch (e) {
						//TODO handle the exception
					}
				}else{
					uni.showToast({
						icon:'error',
					    title: '请先完成答题！'  ,
						duration: 2000,
					});
				}
			},
			handleChoose(data) {
				data.forEach(val => {
					if (val.uploadAnswer != val.answer) {
						this.theImg = "../../static/images/submit_err.png";
						this.isTextColor = true;
					}
				})
			},
			goBackIndex() {
				uni.switchTab({
					url: '/pages/my/index',
				})
				
			},
			goTrain() {
				this.showComponent = true;
				this.isDisable = true;
				this.isAble = false;
				this.componentData = [];
				this.getQuestionBank(); //重新获得题库
			},
		}
	}
</script>

<style lang="less" scoped>
	page {
		height: 100%;
	}

	.background {
		width: 100%;
		height: 100%;
		// position: fixed;
		background-size: 100% 100%;
		z-index: -1;
		position: absolute;
		top: 0px;
		bottom: 0px;
	}

	.nav {
		width: 100%;
		overflow: hidden;
		position: relative;
		top: 0;
		left: 0;
		z-index: 10;
	}

	.nav-title {
		width: 100%;
		height: inherit;
		position: absolute;
		z-index: 10;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 15px;

		.nav-title-text {
			font-family: SF Pro Text;
			font-style: normal;
			font-weight: 600;
			font-size: 16px;
			color: #000000;

			&:first-child {
				font-weight: normal;
				position: absolute;
				left: 25px;
				font-size: 22px;
			}
		}
	}

	.scroll-view {
		height: calc(100vh - 135px) !important;
	}

	.main-container {
		padding: 0 16px;
		box-sizing: border-box;

		.container-title {
			height: auto;
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			padding: 12px 16px;
			width: 100%;
			border: 1px solid rgba(255, 255, 255, 0.72);
			box-sizing: border-box;
			border-radius: 8px;
			background: rgba(255, 255, 255, 0.64);
		}

		.container-title1 {
			display: flex;
			flex-direction: row;
			align-items: center;
			padding: 12px 16px;
			width: 100%;
			height: 88px;
			background: rgba(255, 255, 255, 0.64);
			border: 1px solid rgba(255, 255, 255, 0.72);
			box-sizing: border-box;
			border-radius: 8px;
			margin-bottom: 12px;

			.container-title1-img {
				width: 64px;
				height: 64px;
				margin-right: 16px;
			}

			.textExplain {
				font-family: SF Pro Text;
				font-style: normal;

				.textExplain-view {
					font-weight: 600;
					font-size: 16px;
					line-height: 24px;
					margin-bottom: 5px;
					color: #202532;

					.textExplain-view-text {
						color: #5BBC72;
					}
				}

				.textExplain-text {
					font-weight: normal;
					font-size: 12px;
					line-height: 16px;
					color: #202532;
					order: 1;
					margin-top: 8px;
				}
			}
		}

	}

	.footer {
		position: absolute;
		bottom: 0;
		width: 100%;
		order: 0;
		padding: 0 16px;
		margin-bottom: 8px;

		.footer-container {
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			width: 100%;
			height: 40px;
			color: #FFFFFF;
			border-radius: 4px;
		}
		
		.submit-disable-color {
			background: #ABCAFF;
		}
		
		.submit-able-color {
			background: #2C5DE5;
		}

		.footer-container1 {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			box-sizing: border-box;

			.footer-container1-text {
				box-sizing: border-box;
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				width: 167.5px;
				height: 40px;
				border-radius: 4px;
				align-self: stretch;
				flex-grow: 0;

				//点击改变按钮颜色
				&:first-child {
					border: 1px solid #2C5DE5;
					color: #2C5DE5;

					&:hover {
						background: #2C5DE5;
						color: #FFFFFF;
					}
				}

				&:last-child {
					border: 1px solid #2C5DE5;
					color: #2C5DE5;

					&:hover {
						background: #2C5DE5;
						color: #FFFFFF;
					}
				}
			}
		}
	}

	.text-color {
		color: #E83F4E;
	}
	.tipsbox{
		height: 40px;
		line-height: 40px;
		text-align: center;
		font-size: 13px;
		border-bottom: 1px dashed #9E9E9E;
	}
	.tipsbox.name{
		font-weight: 600;height: 50px;line-height: 50px;
	}
</style>
