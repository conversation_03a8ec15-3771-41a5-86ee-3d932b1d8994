<template>
	<view class="container">
		<!-- 导航 -->
		<view class='nav bg-white' :style="'height:'+navH+'px'">
			<view class='nav-title'>

			</view>
		</view>
		<!-- 头像信息 -->
		<view class='navigation-bar'>
			<view class="user">
				<image @click="uploadFiles" class="avatar" :src="useAvatarUrl" mode=""></image>
				<view class="name-phone">
					<text class="name-phone-text">{{userInfo.userName}}</text>
					<view>{{newPhoneNumber}}</view>
				</view>
			</view>
			<view class="logout">
				<image class="logout-img" src="../../static/images/login_out.png" mode="aspectFit" @click="logOut">
				</image>
			</view>
		</view>
		<!-- 主题库、记录入口 -->
		<view class="main-out">
			<view class="main-in" v-for="item in myList" :key="item.myId" @click="goRecord(item.myId)" v-if='item.show'>
				<view class="in-name" >
					<image class="in-name-img" :src="item.icon" mode="widthFix"></image>
					<text>{{item.title}}</text>
				</view>
			</view>
		</view>

		<!-- 整体背景放在最后 -->
		<view class='bg-main'>
			<image class='background' src="../../static/images/background.png"></image>
		</view>
	</view>
</template>

<script>
	import api from "../../api/index.js"
	const FormData = require('../../utils/formData/formData.js')
	const App = getApp();
	import {
		loginOut
	} from '../../api/weChat.js'
	export default {
		data() {
			return {
				navH: 0,
				myList: [
					// {
					// 	icon: "../../static/images/test.png",
					// 	title: "飞前考试题库训练",
					// 	myId: 1,
					// 	show:true
					// }, 
					// {
					// 	icon: "../../static/images/record.png",
					// 	title: "操作记录",
					// 	myId: 2,
					// 	show:true
					// }, 
					{
						icon: "../../static/images/tab4_on.png",
						title: "个人信息",
						myId: 3,
						show:true
					},
				],
				token: '',
				userInfo: '',
				userUrl: '',
				newPhoneNumber: '',
				useAvatarUrl: '../../static/images/userUrl.png',
				roleFlag:false,
				userRole:''
			}
		},
		created(){
			this.userRole = uni.getStorageSync('userRole');
		},
		onLoad: function(options) {
			this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
			this.token = uni.getStorageSync('token');
			this.userInfo = uni.getStorageSync('userInfo');
			
			
			
			this.useAvatarUrl = this.userInfo.avatar != '' ? this.userInfo.avatar : '../../static/images/userUrl.png';
		},
		methods: {
			uploadFiles(type){
				let that = this
				uni.chooseMedia({
					sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
					mediaType:['image'],
					sourceType: ['album','camera'], //从相册选择
					name:'',
					success: function (res) {
						console.log(123)
						res.tempFiles.map((i)=>{
							that.useAvatarUrl = i.tempFilePath
						})
						let formDatasss = new FormData()
						formDatasss.appendFile('avatar',that.useAvatarUrl)
						formDatasss = formDatasss.getData(); 
						uni.request({
						    url: api.baseUrl+'/wechat/wxUser/uploadAvatar', 
						    data: formDatasss.buffer,
							method:'POST',
						    header: {
								'AuthCode':uni.getStorageSync('token'),
								'AuthID':uni.getStorageSync('userInfo').userId,
						        'Content-Type':formDatasss.contentType,
						    },
						    success: (res) => {
								uni.hideLoading()
								console.log(res)
								if(res.data.code != 200){
									uni.showToast({
										title: res.data.msg,
										icon:'error'
									})	
									return false
								}
								uni.showToast({
									title: res.data.msg,
								})	
						    }
						});
					}
				})
			},
			goRecord(id) {
				if (id == 1) {
					uni.navigateTo({
						url: '/pages/my/flyTraining'
					})

				} else if (id == 2) {
					uni.navigateTo({
						url: '/pages/my/record'
					})
				}else if (id == 3) {
					uni.navigateTo({
						url: '/pages/login/approal'
					})
				}
			},
			// //电话号码处理
			// handlePhone(data) {
			// 	console.log(123)
			// 	let arr = data.phonenumber.split('');
			// 	arr.splice(3, 4, '****');
			// 	this.newPhoneNumber = arr.join('')
			// },
			//退出登录
			 logOut() {
				try {
					uni.showModal({
						title: '是否退出登录？',
						content: '',
						success:async function(res) {
							if (res.confirm) {
								const data = await loginOut();
								uni.clearStorageSync();
								uni.reLaunch({
									url: '/pages/login/login'
								})
								console.log('用户点击确定');
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					});

				} catch (e) {
					//TODO handle the exception
				}
			}
		}
	}
</script>

<style lang="less" scoped>
	page {
		height: 100%;
	}

	.background {
		width: 100%;
		height: 100%;
		background-size: 100% 100%;
		z-index: -1;
		position: absolute;
		top: 0px;
		bottom: 0px;
	}

	.main {
		width: 100%;
		height: 100%;
	}

	.nav {
		width: 100%;
		overflow: hidden;
		position: relative;
		top: 0;
		left: 0;
		z-index: 10;

		.nav-title {
			width: 100%;
			/* height: 45px;
			line-height: 45px; */
			text-align: center;
			position: absolute;
			bottom: 16px;
			left: 0;
			z-index: 10;
			font-family: SF Pro Text;
			font-style: normal;
			font-weight: 600;
			font-size: 32rpx;
			color: #000000;
		}
	}

	.navigation-bar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 16px;
		padding-bottom: 16px;
		border-bottom: 1px solid rgba(255, 255, 255, 0.72);

		.user {
			display: flex;

			.user-btn {
				outline: none;
				border: none;
				margin: unset;
				padding: unset;
				height: auto;
				width: auto;
				display: inline-flex;
				background: none;
				box-sizing: border-box;
				color: unset;
				line-height: unset;
				// position: static;
			}

			.avatar {
				width: 64px;
				height: 64px;
				border-radius: 50%;
			}

			.name-phone {
				display: flex;
				flex-direction: column;
				justify-content: center;
				margin-left: 16px;

				.name-phone-text {
					&:last-child {
						margin-top: 5px;
					}
				}
			}
		}

		.logout {
			.logout-img {
				width: 24px;
				height: 24px;
			}
		}
	}


	.main-out {
		padding: 0 16px;

		.main-in {
			display: flex;
			align-items: flex-start;
			justify-content: space-between;
			padding: 12px 8px;
			width: 100%;
			height: 44px;
			left: 0px;
			top: 0px;
			background: rgba(255, 255, 255, 0.48);
			border: 1px solid rgba(255, 255, 255, 0.72);
			box-sizing: border-box;
			border-radius: 8px;
			flex: none;
			order: 0;
			flex-grow: 0;
			margin: 0 auto;
			margin-top: 12px;

			.in-name {
				display: flex;
				align-items: center;
				justify-content: cneter;
				margin-top: -2px;

				.in-name-img {
					width: 18px;
					height: auto;
					margin-right: 5px;
				}
			}
		}
	}
</style>
