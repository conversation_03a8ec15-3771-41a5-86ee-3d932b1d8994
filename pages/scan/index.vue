<template>
	<view class="orderListBox">
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">扫码验票</view>
		</view>

		<view class="no-data-box" v-if="!noAuth">
			<image class="image" src="../../static/images/nodata.png" mode="widthFix"></image>
			<text class="col-gray-500">当前无权限</text>
		</view>
		<view class="" v-if="noAuth">
			<van-dropdown-menu close-on-click-overlay @click-overlay='priceShow=false'>
				<van-dropdown-item :style="{display:  priceShow2 ? 'block' : 'none' }" @close="priceShow2=false"
					@open="priceShow2=true" @change="clickMenuItem2" v-model="quaryParam.areaCode" :options="option2">
				</van-dropdown-item>
			</van-dropdown-menu>
			
			
			
			<view class="proList">
				<view class="probox">
			
					<view style="">
						<view style="float: left; width: 60%; ">
							<view class="proName">
								<view>产品：<text v-if="verifyTicket.passengerProduct.productName">
										{{verifyTicket.passengerProduct.productName}}
									</text></view>
			
							</view>
							<view class="proName">
								<view>套餐：<text v-if="verifyTicket.passengerProduct.packageName">
										{{verifyTicket.passengerProduct.packageName}}
									</text></view>
			
							</view>
							<view class="proName">
								<view>姓名：<text v-if="verifyTicket.passengerName">{{verifyTicket.passengerName}}
									</text></view>
			
							</view>
			
							<!-- <view class="proName">
								<view>产品日期：<text v-if="verifyTicket.flightDate">{{verifyTicket.flightDate}}</text></view>
			
							</view> -->
							<view class="proName">
								<view>预约跳伞日期：<text v-if="verifyTicket.appointmentTime">
										{{verifyTicket.appointmentTime}}
									</text></view>
			
							</view>
							<view class="proName">
								<text>状态：</text>
								<text>
									<van-tag style="margin-left: 3px;"
											type="success">{{verifyTicketMessage}}</van-tag>
								
								</text>
			
			
							</view>
						</view>
						<view style="float: left;width: 40%;text-align: center;">
							<image @click="openImage(verifyTicket.realFaceImageUrl)" style="width: 100%;height: 130px;"  :src="verifyTicket.realFaceImage==null?'../../static/images/photo_img.png':verifyTicket.realFaceImageUrl" ></image>
							<!-- <van-button plain type="default" style='margin-right: 5px;' size='small'
								@click='scan()'>扫一扫</van-button> -->
						</view>
						<view style="width: 100%;text-align: center;">
							<van-button plain type="default" style='margin-right: 5px;' size='small'
								@click='scan()'>扫一扫</van-button>
						</view>
					</view>
				</view>
			</view>
			
			
			
			
			<scroll-view class="listBox" :style="'height:calc(100vh - ' + (navH+257) + 'px)'" scroll-y
				@scrolltolower="onBottomRefresh" @refresherrefresh="onRefresh" :refresher-enabled="isRefresh"
				:refresher-triggered="triggered" @refresherrestore="onRestore" @refresherpulling="openLoading"
				refresher-default-style="none" @refresherabort="onAbort">
				<empty-card v-show="empty"></empty-card>
			
				<!-- 	<van-cell-group class="boxer" inset v-for="(item,index) in list" :key='index'>
					<van-cell :title="'姓名:'+item.openingTime" value="日期:2023-12-12" />
					<van-cell title="单元格" value="内容" />
					<van-cell title="单元格" value="内容" label="描述信息" />
					<van-cell title="单元格" value="内容" label="描述信息" />
				</van-cell-group> -->
			
			
				<view class=""
					style="    font-size: 30rpx;background-color: #FFF; margin:0 20px;margin-top: 5rpx;border-radius: 20rpx;border: 1px solid rgba(186, 186, 186, 0.7);overflow: auto;padding: 5px 5px;margin-bottom: 5px;"
					v-for="(item,index) in list" :key='index'>
			
					<view style="width: 60%;float: left;" @tap="toPassengerDetail(item)">
						<view class="cu-bar solid-bottom" style="overflow: hidden;">
							<view style="padding-right:10rpx">
								<view>姓名: {{item.passengerName}}</view>
								<view @tap.stop="callPhone(item.phone)">电话: ☎️{{item.phone==null?'':item.phone}}</view>
								<view>产品日期:{{item.flightDate}}</view>
								<view>预约日期:{{item.appointmentTime}}</view>
								<view style="padding-right:10rpx">客票状态:
									<van-tag style="margin-left: 3px;"
											:type="item.ticketStatus==2?'success':'danger'">{{transformTicketStatus(item.ticketStatus)}}</van-tag>
								</view>
								<view style="padding-right:10rpx">实名状态:
									<van-tag style="margin-left: 3px;"
											:type="item.isRealAuth==1?'success':'danger'">{{item.isRealAuth==1?'已实名':'未实名'}}</van-tag>
								</view>
								<view style="padding-right:10rpx">跳伞协议:
									<van-tag style="margin-left: 3px;"
											:type="item.agreement==null?'danger':'success'">{{item.agreement==null?'未签署':'已签署'}}</van-tag>
								</view>
								<view style="padding-right:10rpx">使用状态: <van-tag style="margin-left: 3px;"
										:type="item.isUsed==1?'success':'danger'">{{transformIsUsed(item.isUsed)}}</van-tag>
								</view>
							</view>
						</view>
					</view>
					<view style="width: 40%;float: left;">
						<image @click="openImage(item.realFaceImageUrl)" style="width: 100%;height: 130px;padding-top: 30px;"
							:src="item.realFaceImage==null?'../../static/images/photo_img.png':item.realFaceImageUrl"></image>
					</view>
			
			
			
			
			
				</view>
			
			
			
			
			
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import {
		areaList,
		scanVerifyTicket,
		passengerList
	} from '../../api/weChat.js';
	const App = getApp();
	export default {
		data() {
			return {
				verifyTicket: {},
				verifyTicketMessage: '',
				active: 'home-o',
				value1: '',
				value2: 0,
				value3: '',
				option1: [{
					text: '预约日期',
					value: 1,
					icon: ''
				}, {
					text: '购买日期',
					value: 0,
					icon: ''
				}],
				option3: [{
						text: '全部',
						value: '',
						icon: ''
					},
					{
						text: '出票',
						value: 2,
						icon: ''
					},
					{
						text: '取消',
						value: 1,
						icon: ''
					},
					{
						text: '退票',
						value: 4,
						icon: ''
					},
					{
						text: '已使用',
						value: 5,
						icon: ''
					}, {
						text: '改签',
						value: 6,
						icon: ''
					},
				],
				option2: [{
					text: '默认排序',
					value: '',
					icon: ''
				}],
				priceShow: false,
				priceShow3: false,
				priceShow2: false,
				navH: 0,
				isRefresh: true,
				triggered: true, // 开启下拉
				_freshing: false,
				flag: true,
				// 查询条件
				quaryParam: {
					timeType: 1,
					ticketStatus: '',
					pageNum: 1,
					pageSize: 10,
					areaCode: '',
					startDate: this.$util.getDate(),
					endDate: this.$util.getDate(),
					phone: uni.getStorageSync('userInfo') != null ? uni.getStorageSync('userInfo').mobile : null,
					openId: uni.getStorageSync('userInfo') != null ? uni.getStorageSync('userInfo').weixinOpenid : null
				},
				list: [],
				moreFlag: true,
				empty: false,
				noAuth: false
			}
		},
		onShow() {
			this.noAuth = false
			if(uni.getStorageSync('wxMenus')!=null&&uni.getStorageSync('wxMenus').length>0){
				uni.getStorageSync('wxMenus').map(i=>{
					if(i.menuName == '扫码验票'){
						this.noAuth = true
					}
				})
			}
			if(!this.noAuth){
				return false
			}
			console.log(this.$util.url)
			this.quaryParam.pageNum == 1
			if (!uni.getStorageSync('userInfo')) {
				this.list = []
				uni.showToast({
					duration: 3000,
					title: '请登录后在查询',
					icon: 'none'
				})
			} else {
				this.getData(1)
			}

			this.getAreaList()
		},
		created() {
			this.list = []
			this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
			this.flag = true
		},

		methods: {
			openImage(img) {
				uni.previewImage({
					urls: [img]
				})
			},
			async getAreaList() {
				try {
					const res = await areaList();
					console.log(res)
					if (res.response.data == null) {
						return false
					}
					this.option2 = [{
						text: '全部基地',
						value: '',
						icon: ''
					}]
					res.response.data.map((i) => {
						this.option2.push({
							text: i.areaName,
							value: i.areaCode,
							icon: ''
						})
					})

					// this.$request.post('/tw/area/list').then(res => {
					// 	this.option2 = [{
					// 		text: '全部基地',
					// 		value: '',
					// 		icon: ''
					// 	}]
					// 	res.data.data.map((i) => {
					// 		this.option2.push({
					// 			text: i.areaName,
					// 			value: i.areaCode,
					// 			icon: ''
					// 		})
					// 	})
					// }).catch(err => {})

				} catch (e) {
					console.error(e)
				}
			},
			toPassengerDetail(pass) {
			
				uni.navigateTo({
					url: `/pages/scan/passengerDetail?passenger=${encodeURIComponent(JSON.stringify(pass))}`
				});

			},
			scan() {
				let that = this
				uni.scanCode({
					sound: 'default',
					onlyFromCamera: true,
					scanType: ['qrCode'],
					success: async function(res2) {
						try {
							const res = await scanVerifyTicket({
								ticketNo: res2.result
							});
							console.log(res)
							if (res.response.flag) {
								uni.showToast({
									duration:2000,
									title:'成功验票',
									icon:'success'
								})
								that.verifyTicket = res.response.data
								if (that.verifyTicket.realFaceImage) {
									that.verifyTicket.realFaceImageUrl = that.$util.images + that.verifyTicket.realFaceImage +
										'?' + Math.random()
								}
								that.verifyTicketMessage = '使用成功'
								that.getData(1)
							} else {
								uni.showToast({
									duration: 2000,
									title: res.response.messages,
									icon: 'none'
								})
								that.verifyTicket = {}
								that.verifyTicketMessage = res.response.messages
							}

						} catch (e) {
							uni.showToast({
								duration: 2000,
								title: "请求异常",
								icon: 'none'
							})
							that.verifyTicket = {}
							that.verifyTicketMessage = "请求异常"
						}
					}
				})

			},
			transformTicketStatus(status) {
				let value = this.option3.filter(e => {
					if (e.value == status) {
						return e;
					}
				})
				return value[0].text

			},
			transformIsUsed(e) {
				if (e == 0) {
					return "未使用"
				} else if (e == 1) {
					return "已使用"
				} else {
					return e
				}

			},
			callPhone(phone) {
				if (phone != null && phone != '') {
					uni.makePhoneCall({
						// 手机号 这里可以直接写号码如 12345 也可以写获取号码的字段如this.mobile
						phoneNumber: phone,
						// 成功回调
						success: (res) => {
							console.log('调用成功!')
						},
						// 失败回调
						fail: (res) => {
							console.log('调用失败!')
						}
					});

				}
			},
			async queryPassenger(type) {
				let that = this
				try {
					const res = await passengerList(this.quaryParam);
					if (res.response.flag) {
						if (type == 1) {
							this.list = []
							if (res.response.data.rows.length == 0) {
								this.empty = true
							} else {
								this.empty = false
							}
						}
						this.list = this.list.concat(res.response.data.rows)

						this.list.forEach((item) => {
							if (item.realFaceImage) {
								item.realFaceImageUrl = that.$util.images + item.realFaceImage +
									'?' + Math.random()
							}
						});

						if (res.response.data.rows.length == 10) {
							this.moreFlag = true
						} else {
							this.moreFlag = false
						}

					} else {
						uni.showToast({
							duration: 2000,
							title: res.response.messages,
							icon: 'none'
						})
					}

				} catch (e) {
					uni.showToast({
						duration: 2000,
						title: "请求异常",
						icon: 'none'
					})
				}



			},
			openCalendar() {
				this.$refs.calendar.open();
			},
			confirmCalendar(e) {
				if (e.range.after == '' && e.range.before == '') {

				} else if (e.range.after == '' && e.range.before != '') {
					this.quaryParam.startDate = e.range.before
					this.quaryParam.endDate = e.range.before
				} else if (e.range.after != '' && e.range.before == '') {
					this.quaryParam.startDate = e.range.after
					this.quaryParam.endDate = e.range.after
				} else if (e.range.after != '' && e.range.before != '') {
					this.quaryParam.startDate = e.range.before
					this.quaryParam.endDate = e.range.after
				}
				this.quaryParam.pageNum = 1
				this.getData(1);

			},
			// 获取产品列表
			getData(type) {

				this.queryPassenger(type);

				this.quaryParam.openId = uni.getStorageSync('userInfo').weixinOpenid
				this.quaryParam.phone = uni.getStorageSync('userInfo').mobile
				// this.$request.post('/tw/product/productList', this.quaryParam).then(res => {
				// 	console.log(res)
				// 	if (res.data.flag == 0) {
				// 		uni.showToast({
				// 			duration: 3000,
				// 			title: res.data.messages,
				// 			icon: 'none'
				// 		})
				// 		return false;
				// 	}
				// 	if (type == 1) {
				// 		this.list = []
				// 		if (res.data.data.rows.length == 0) {
				// 			this.empty = true
				// 		} else {
				// 			this.empty = false
				// 		}
				// 	}
				// 	this.list = this.list.concat(res.data.data.rows)
				// 	if (res.data.data.rows.length == 10) {
				// 		this.moreFlag = true
				// 	} else {
				// 		this.moreFlag = false
				// 	}
				// 	console.log(this.list)
				// }).catch(err => {
				// 	this.moreFlag = true
				// 	this.quaryParam.pageNum = this.quaryParam.pageNum == 1 ? 1 : (this.quaryParam.pageNum - 1)
				// })


			},
			// 触底刷新
			onBottomRefresh() {
				if (this.moreFlag) {
					this.quaryParam.pageNum += 1
					console.log(123)
					this.getData(0)
				}
			},
			// 下拉刷新
			onRefresh() {
				if (this._freshing) return;
				this._freshing = true;
				if (!this.triggered) {
					this.triggered = true;
				}
				// this.loadStoreData();
				this.quaryParam.pageNum = 1
				this.flag = true
				this.triggered = false;
				this._freshing = false;
				this.getData(1);
			},
			// 下拉刷新复位
			onRestore() {
				this.triggered = false;
				this._freshing = false;

			},
			// 下拉刷新中止
			onAbort() {
				this.triggered = false;
				this._freshing = false;
			},
			openLoading() { //被下拉
				this.triggered = true;
			},
			// 筛选条件更改
			clickMenuItem(value) {
				this.quaryParam.timeType = value.detail
				this.quaryParam.pageNum = 1
				this.getData(1)


			},
			clickMenuItem2(value) {
				this.quaryParam.areaCode = value.detail
				this.quaryParam.pageNum = 1
				this.getData(1)

			},
			clickMenuItem3(value) {
				this.quaryParam.ticketStatus = value.detail
				this.quaryParam.pageNum = 1
				this.getData(1)

			},



		}
	}
</script>

<style lang="less">
	.no-data-box {
		background: rgba(255, 255, 255, 0.64);
		height: 343px;
		border-radius: 8px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	
		.image {
			width: 140px;
			height: 140px;
		}
	
		.col-gray-500 {
			font-size: 14px;
		}
	}
	.nav {
		width: 100%;
		overflow: hidden;
		position: relative;
		top: 0;
		left: 0;
		z-index: 10;
	}

	.nav-title {
		width: 100%;
		/* height: 45px;
		line-height: 45px; */
		text-align: center;
		position: absolute;
		bottom: 16px;
		left: 0;
		z-index: 10;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;
	}

	.van-button--plain {
		width: 80%;
	}

	.red {
		color: red;
	}

	.green {
		color: green;
	}

	.orderListBox {
		.contentpass {
			padding-left: 15px;
		}

		.van-icon-close {
			display: none;
		}

		.listBox {
			overflow: auto;
		}

		.van-card__footer {
			margin-top: -30px;
		}

		.boxer {
			border: 1px solid #eee;
			margin: 7px 12px;
			border-radius: 10px;
			overflow: hidden;

			.location {
				height: 30px;
				line-height: 30px;
				padding-left: 30px;
				background-color: #fff;
				font-size: 13px;
			}

			.van-card__desc {
				padding: 3px 0 1px;
			}
		}

		.van-card__price {
			margin-top: 5px;
		}

		.van-dropdown-menu__title {
			font-size: 13px;
		}

		.van-dropdown-menu.van-dropdown-menu--top-bottom {
			height: 30px;
			padding-bottom: 5px;
		}

		.van-card__title {
			font-size: 14px;
			margin-left: -5px;
		}

		.proList {
			padding: 5px 10px;

			.probox {
				padding: 10px 15px;
				border-radius: 5px;
				background-color: #fff;
				margin-bottom: 8px;

				.titleBox {
					overflow: hidden;

					.left {
						float: left;
					}

					.right {
						float: right;
						color: #03a9f4;
					}
				}

				.proName {
					font-size: 15px;
					padding: 5px 0;
					font-weight: 600;
				}

				.proIntro {
					margin-bottom: 5px;
					font-size: 13px;
					color: #898989;
				}

				.van-divider {
					margin: 10px 0;
				}

				.proDate {
					color: #333;
				}
			}
		}
	}
</style>