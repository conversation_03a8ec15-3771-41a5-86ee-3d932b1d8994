<template>
	<view class="orderListBox">
		<!-- 自定义导航栏 -->
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">添加旅客</text>
			</view>
		</view>

		<view class="content" :style="'overflow:auto;height:calc(100vh - ' + navH + 'px)'">
			<van-field value="旅客信息" class="tille" readonly border="true" />
			<van-cell-group>
				<van-field v-model="passenger.passengerName" readonly label="姓名" type="textarea" autosize
					border="false" />
				<van-field v-model="passenger.idNo" readonly label="证件号" type="textarea" autosize border="false" />
				<van-field v-model="passenger.phone" readonly label="联系电话" type="textarea" autosize border="false" />
				<van-field v-model="passenger.ticketNo" readonly label="票号" type="textarea" autosize border="false" />
				<van-field v-model="passenger.weight" readonly label="体重" type="textarea" autosize border="false" />
				<van-field v-model="passenger.formWhere" readonly label="来自哪" type="textarea" autosize border="false" />
				<van-field v-model="passenger.arriveTime" readonly label="几点到基地" type="textarea" autosize
					border="false" />
				<van-field v-model="passenger.remark" readonly label="备注" type="textarea" autosize border="false" />
			</van-cell-group>
			<van-field value="产品信息" class="tille" readonly border="true" />

			<van-cell-group>
				<van-field v-model="passenger.flightDate" readonly label="产品日期" type="textarea" autosize
					border="false" />
				<van-field v-model="passenger.appointmentTime" readonly label="预约日期" type="textarea" autosize
					border="false" />

				<van-field v-model="passenger.passengerProduct.depCity" readonly label="基地" type="textarea" autosize
					border="false" />
				<van-field v-model="passenger.passengerProduct.productName" readonly label="产品" type="textarea" autosize
					border="false" />
				<van-field v-model="passenger.passengerProduct.productDescription" readonly label="产品介绍" type="textarea"
					autosize border="false" />
				<van-field v-model="passenger.passengerProduct.packageName" readonly label="套餐" type="textarea" autosize
					border="false" />
				<van-field v-model="passenger.passengerProduct.packageDescription" readonly label="套餐介绍" type="textarea"
					autosize border="false" />
				<van-field v-model="passenger.passengerProduct.sortiesDepTime" readonly label="架次起飞时间" type="textarea"
					autosize border="false" />
				<van-field v-model="passenger.passengerProduct.sortiesArrTime" readonly label="架次到达时间" type="textarea"
					autosize border="false" />

				<van-field v-model="passenger.passengerProduct.packageCameraTypeName" readonly label="摄像类型"
					type="textarea" autosize border="false" />

				<van-field v-model="passenger.passengerProduct.skyHeight" readonly label="高度" type="textarea" autosize
					border="false" />

			</van-cell-group>
			<van-field value="价格信息" class="tille" readonly border="true" />

			<van-field v-model="passenger.passengerProduct.packageAdvancePaymentPrice" readonly label="定金"
				type="textarea" autosize border="false" />
			<van-field v-model="passenger.advancePaymentStatus" readonly label="定金付款状态" type="textarea" autosize
				border="false" />
			<van-field v-model="passenger.advancePaymentPayTime" readonly label="定金付款时间" type="textarea" autosize
				border="false" />

			<van-field v-model="passenger.passengerProduct.packageFinalPaymentPrice" readonly label="尾款" type="textarea"
				autosize border="false" />
			<van-field v-model="passenger.finalPaymentStatus" readonly label="尾款付款状态" type="textarea" autosize
				border="false" />
			<van-field v-model="passenger.finalPaymentPayTime" readonly label="尾款付款时间" type="textarea" autosize
				border="false" />


			<van-field v-model="passenger.passengerProduct.packageTotalPaymentPrice" readonly label="总价" type="textarea"
				autosize border="false" />

			<van-field v-model="passenger.agentSettlementPrice" readonly label="代理结算价" type="textarea" autosize
				border="false" />

			<view class="" v-if="passenger.agreement != null">
				<view class="box" style="margin-top: 20px;">
					<view class="left" style="font-size: 15px;text-align: center;">
						跳伞协议
					</view>
				</view>
				<view class="" style="padding-left: 17px;">
					<view style='margin-bottom:10px'>
						<image @click="checkImg" style="width: 100%;" :src="passenger.agreementImg" mode=""></image>
					</view>

				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const App = getApp();
	export default {
		data() {
			return {
				navH: 0,
				passenger: {},
				ticketStatusString: [{
						text: '全部',
						value: '',
						icon: ''
					},
					{
						text: '出票',
						value: 2,
						icon: ''
					},
					{
						text: '取消',
						value: 1,
						icon: ''
					},
					{
						text: '退票',
						value: 4,
						icon: ''
					},
					{
						text: '已使用',
						value: 5,
						icon: ''
					}, {
						text: '改签',
						value: 6,
						icon: ''
					},
				],
			}
		},
		onLoad(options) {
			// 同样的在onLoad 生命周期中进行接收， decodeURIComponent 为uniapp 提供的api

			this.passenger = JSON.parse(decodeURIComponent(options.passenger));
			console.log(this.passenger)
			this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
			this.passenger.advancePaymentStatus = this.transformPaymentStatus(this.passenger.advancePaymentStatus)
			this.passenger.finalPaymentStatus = this.transformPaymentStatus(this.passenger.finalPaymentStatus)

			this.passenger.advancePaymentPayTime = this.$util.getDateTimeByString(this.passenger.advancePaymentPayTime)
			this.passenger.finalPaymentPayTime = this.$util.getDateTimeByString(this.passenger.finalPaymentPayTime)
			if (this.passenger.agreement) {
				this.passenger.agreementImg = this.$util.images + this.passenger.agreement +
					'?' + Math.random()
			}

		},
		onShow() {



		},
		created() {

		},

		methods: {
			checkImg() {
				let that = this
				uni.previewImage({
					urls: [that.passenger.agreementImg]
				})
			},
			backMyPage() {
				uni.navigateBack()
			},
			transformPaymentStatus(status) {
				if (status == 1) {
					return "已支付"
				} else if (status == 0) {
					return "未支付"
				} else {
					return status
				}
			}

		}
	}
</script>

<style lang="less">
	.nav {
		width: 100%;
		overflow: hidden;
		position: relative;
		top: 0;
		left: 0;
		z-index: 10;
	}

	.nav-title {
		width: 100%;
		height: inherit;
		position: relative;
		z-index: 10;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 15px;

		.nav-title-text {
			// padding-top: 10px;
			font-family: SF Pro Text;
			font-style: normal;
			font-weight: 600;
			font-size: 16px;
			color: #000000;

			&:first-child {
				font-weight: normal;
				position: absolute;
				left: 25px;
				font-size: 22px;
			}
		}
	}

	.orderListBox {
		.listBox {
			overflow: auto;
		}

		.van-card__footer {
			margin-top: -30px;
		}

		.boxer {
			border: 1px solid #eee;
			margin: 7px 12px;
			border-radius: 10px;
			overflow: hidden;

			.location {
				height: 30px;
				line-height: 30px;
				padding-left: 30px;
				background-color: #fff;
				font-size: 13px;
			}

			.van-card__desc {
				padding: 3px 0 1px;
			}
		}

		.van-card__price {
			margin-top: 5px;
		}

		.van-dropdown-menu__title {
			font-size: 13px;
		}

		.van-dropdown-menu.van-dropdown-menu--top-bottom {
			height: 30px;
			padding-bottom: 5px;
		}

		.van-card__title {
			font-size: 14px;
			margin-left: -5px;
		}

	}
</style>