<template>
	<view class="container">
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">架次详情</text>
			</view>
		</view>

		<view class="content">
			<view class="warp">
				<view class="">

					<view class="detail-row bg-white-500 no-fold">
						<view style="width: 100%;">
							<view class="detail-block">
								<p class="fz-18 col-gray-600">编辑架次</p>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="warp">
				<view class="">

					<view class="detail-row bg-white-500 no-fold">
						<view style="width: 30%;">
							<view class="detail-block">
								<p class="fz-14 col-gray-600">状态 {{formatStatus(group.groupStatus)}}</p>
							</view>
						</view>
						<view style="width: 30%;">
							<view class="detail-block">
								<p v-if="group.departTime" class="fz-14 col-gray-600">起飞 {{group.departTime}}</p>
							</view>
						</view>
						<view style="width: 30%;">
							<view class="detail-block">
								<p v-if="group.arriveTime" class="fz-14 col-gray-600">到达 {{group.arriveTime}}</p>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="warp">
				<view class="">

					<view class="detail-row bg-white-500 no-fold " v-for="(user, index) in group.passengerGroup"
						:key="index">

						<view style="width: 5%;line-height: 40px;">
							<view class="detail-block">
								<p class="fz-14 col-gray-600">{{index+1}}</p>
							</view>
						</view>
						<view style="width: 15%;line-height: 40px">
							<view class="detail-block">
								<p class="fz-16 col-gray-600">旅客：</p>
							</view>
						</view>
						<view style="width: 40%;" class="picker-container">
							<picker mode="selector" :range="passList" :value="user.selectedIndex" range-key="name"
								@change="onNameChange($event, index)">
								<view class="picker">
									<text>{{user.passengerName}}</text>
									<image class="picker-arrow" src="/static/images/arrow_down_icon.png" />
								</view>
							</picker>
							<!-- <picker mode="selector" :range="nameList" :value="user.selectedIndex"
								@change="onNameChange($event, index)">
								<view class="picker">
									<text>{{ nameList[user.selectedIndex] }}</text>
									<image class="picker-arrow" src="/static/images/arrow_down_icon.png" />
								</view>
							</picker> -->
						</view>
						<view style="width: 20%;line-height: 40px">
							<view class="detail-block">
								<!-- <p class="fz-16 col-gray-600">{{user.weight}}</p> -->
								<input type="number" min='0' style="height: 40px !important;" class="fz-14 col-gray-600"
									v-model='user.weight' placeholder="体重" placeholder-style='font-size:12px' />
							</view>
						</view>
						<view style="line-height: 40px">
							<view class="detail-block">
								<p class="fz-14 col-gray-600" @click="remove(index)">移除 x</p>
							</view>
						</view>

					</view>
					<view style="width: 100%;line-height: 40px;z-index: 100;">
						<view class="detail-block text-center">
							<p class="fz-14 col-gray-600" @click="add()">新增旅客</p>
						</view>
					</view>


				</view>
			</view>




			<view class="warp">
				<view class="">

					<view class="detail-row bg-white-500 no-fold">
						<view style="width: 40%;">
							<view class="detail-block">
								<p class="fz-18 col-gray-600" @click="deleteGroup()">删除</p>
							</view>
						</view>
						<view style="width: 20%;">
							<view class="detail-block">
								<p class="fz-18 col-gray-600"></p>
							</view>
						</view>
						<view style="width: 40%;">
							<view class="detail-block">
								<p class="fz-18 col-gray-600" @click="submitGroup()">提交</p>
							</view>
						</view>
					</view>
				</view>
			</view>

		</view>




		<!-- 整体背景放在最后 -->
		<view class="bg-main">
			<image class="background" src="../../../static/images/background.png"></image>
		</view>





	</view>







</template>
<script>
	import {
		sortieUngroupedList,
		sortieSubmit,
		sortieDelete
	} from '../../../api/weChat.js';
	const App = getApp();
	export default {
		data() {
			return {
				group: {},
				navH: 0,
				userList: [{
						name: '张三',
						selectedIndex: 0
					},
					{
						name: '李四',
						selectedIndex: 1
					},
					{
						name: '王五',
						selectedIndex: 2
					},
					// 更多用户...
				],
				nameList: ['张三', '李四', '王五'],
				passList: []
			};
		},
		onLoad: function(options) {
			console.log(JSON.parse(options.param))
			this.group = JSON.parse(options.param)
			this.$set(this.$data, 'navH', Number(App.globalData.navHeight));

			this.getPassengerNoGroup()
		},
		onShow: function(options) {

		},

		methods: {
			async deleteGroup() {
				let that = this
				uni.showModal({
					title: '提示',
					content: '确认删除该架次吗',
					success: async function(res) {
						if (res.confirm) {
							console.log('用户点击确定');
							let param = {
								id: that.group.id
							}
							const res = await sortieDelete(param);
							console.log(res)
							if (res.response.flag) {
								uni.showToast({
									icon: 'success',
									duration: 1500,
									title: '提交成功'
								})
								setTimeout(function() {
									uni.navigateBack()
								}, 1000)
							}

						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			async submitGroup() {

				console.log(this.group)

				let param = {
					baseInfo: {
						groupDate: this.group.groupDate,
						areaCode: this.group.areaCode
					},
					groupInfos: {
						groupId: this.group.id,
						passengers: this.group.passengerGroup
					}
				}

				console.log(param)

				const res = await sortieSubmit(param);
				console.log(res)
				if (res.response.flag) {
					uni.showToast({
						icon: 'success',
						duration: 1500,
						title: '提交成功'
					})
					setTimeout(function() {
						uni.navigateBack()
					}, 1000)
				} else {
					uni.showToast({
						icon: 'error',
						duration: 1500,
						title: res.response.messages
					})
				}


			},
			remove(index) {
				this.group.passengerGroup.splice(index, 1);
			},
			add() {
				let newPass = {
					passengerName: '',
					passengerId: null,
					skydiveCoach: "无"
				}
				this.group.passengerGroup.push(newPass)

			},
			async getPassengerNoGroup(type) {
				console.log(uni.getStorageSync('userInfo'))
				let that = this
				console.log(that.arrayAirport)
				let homeParam = {
					openId: uni.getStorageSync('userInfo').openId,
					groupDate: that.group.groupDate,
					areaCode: that.group.areaCode
				};
				try {
					const res = await sortieUngroupedList(homeParam);
					console.log(res)
					that.passList = res.response.data

				} catch (e) {
					console.error(e)
				}
				console.log(this.flag)
			},
			formatStatus(e) {
				let result = ''
				if (e == 0) {
					result = '正常'
				} else if (e == 1) {
					result = '起飞'
				} else if (e == 2) {
					result = '结束'
				}
				return result;
			},
			onNameChange(e, index) {
				// 更新对应索引的selectedIndex
				// this.userList[index].selectedIndex = e.detail.value;
				console.log(e.detail.value)
				console.log(index)
				this.group.passengerGroup[index].passengerName = this.passList[e.detail.value].name
				this.group.passengerGroup[index].passengerId = this.passList[e.detail.value].passengerId
				this.group.passengerGroup[index].weight = this.passList[e.detail.value].weight
				this.group.passengerGroup[index].skydiveCoach = "无"
				this.$forceUpdate()


			},
			backMyPage() {
				uni.navigateBack()
			},



		}
	};
</script>

<style lang="scss" scoped>
	page {
		height: 100%;
	}

	.picker-container {
		margin-bottom: 10px;
		padding: 10px;
		background-color: #f0f0f0;
		border-radius: 5px;
	}

	.picker {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.picker-arrow {
		width: 20px;
		height: 20px;
	}

	.warp {
		position: relative;
		overflow: hidden;
		background-color: rgba(255, 255, 255, 0.64);
		border: 1px solid rgba(255, 255, 255, 0.72);
		border-radius: 8px;
		margin-bottom: 12px;

	}

	.detail-row {
		padding: 12px 16px;
		display: flex;
		width: 100%;
		backdrop-filter: blur(16px);
		flex-wrap: wrap;
		justify-content: space-between;
	}

	.warp_plane {
		width: 190px;
		position: absolute;
		top: 0;
		right: 0;
	}


	.warp_main {
		padding: 24rpx 0rpx 0 32rpx;
		height: 45px;
		background-color: rgba(0, 0, 0, 0);

		.header {
			// padding-right: 84rpx;
			height: 16px;

			.index {
				width: 16px;
				height: 16px;
				line-height: 16px;
				text-align: center;
				margin-right: 13px;
				border-radius: 50%;
				color: #838791;
				background-color: #EAEAEB;
				display: inline-block;
			}

			.marRight4 {
				margin-right: 8rpx;
			}

			.marRight33 {
				margin-right: 10rpx;
			}
		}

		.trip-address {
			margin-top: 16px;
			margin-bottom: 12px;

			.fz-16 {
				font-weight: 800;
			}
		}

		.trip-bar {
			width: 16px;
			height: 104px;
			margin-right: 12px;
		}

		.show-view {
			height: 330px;
			transition: .5s;
			backdrop-filter: blur(16px);
		}

		.close-view {
			height: 0;
			transition: .5s;
			overflow: hidden;
		}
	}

	.background {
		width: 100%;
		height: 100%;
		background-size: 100% 100%;
		z-index: -1;
		position: absolute;
		top: 0px;
		bottom: 0px;
	}





	.main {
		width: 100%;
		height: 100%;
	}


	.nav {
		width: 100%;
		overflow: hidden;
		position: relative;
		top: 0;
		left: 0;
		z-index: 10;
	}

	.nav-title {
		width: 100%;
		height: inherit;
		position: relative;
		z-index: 10;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 15px;

		.nav-title-text {
			// padding-top: 10px;
			font-family: SF Pro Text;
			font-style: normal;
			font-weight: 600;
			font-size: 16px;
			color: #000000;

			&:first-child {
				font-weight: normal;
				position: absolute;
				left: 25px;
				font-size: 22px;
			}
		}
	}


	.refresh {
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		top: 0;
		width: 100%;
		height: 100%;
		background-color: #eeeeee;
		color: #000000;
	}

	.content {
		padding: 0 16px;
	}

	.refresh-container {
		width: 750rpx;
		text-align: center;
		position: absolute;
		align-items: center;
		margin-bottom: 20px;
	}

	.refresh-container text {}

	.no-data-box {
		background: rgba(255, 255, 255, 0.64);
		height: 343px;
		border-radius: 8px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		.image {
			width: 140px;
			height: 140px;
		}

		.col-gray-500 {
			font-size: 14px;
		}
	}

	.spinning {
		margin-right: 4px;
		display: inline-block;
		-webkit-animation: rotate 1s linear infinite;
		animation: rotate 1s linear infinite;
	}

	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
		}
	}

	.statistics {
		margin-bottom: 12px;
		padding: 0 16px;
		background-color: #fff;
		background: rgba(255, 255, 255, 0.64);
		border: 1px solid rgba(255, 255, 255, 0.72);
		border-radius: 8px;
	}
</style>