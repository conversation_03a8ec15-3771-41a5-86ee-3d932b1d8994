<template>
	<view class="container">
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">架次管理</text>
			</view>
		</view>
		<scroll-view class="bg-gray overflow" :style="'height:calc(100vh - ' + navH + 'px)'" scroll-y
			 @refresherrefresh="onRefresh" :refresher-enabled="isRefresh"
			:refresher-triggered="triggered" @refresherrestore="onRestore" @refresherpulling="openLoading"
			refresher-default-style="none" @refresherabort="onAbort">
			<view class="refresh-container" slot="refresher">
				<view class="spinning"><text class="iconfont icon-loader-2-fill"></text></view>
				<text>{{ loadingText }}</text>
			</view>
			<view class="content">
				<view class="statistics flex-row justify-between" style="align-items: center;">
					<view>
						<p>
							<span class="col-gray-500 fz-12 margin-right-8">当前时间</span>
							<span class="col-gray-600 fz-12">{{ currentTime }}</span>
						</p>
						<!-- 	<p>
							<span class="col-gray-500 fz-12 margin-right-8">最后更新</span>
							<span class="col-gray-600 fz-12">{{ homeInfo.lastUpdateTime }}</span>
						</p> -->
					</view>
					<view class="text-center">
						<p class="col-gray-600 fz-16">{{ taskData.length }}</p>
						<p class="col-gray-500 fz-12">总计数据</p>
					</view>
				</view>
				<view class="uni-form-item uni-column" style="display: flex;height: 30px;line-height: 30px;background: #fff;
    margin-bottom: 10px;border-radius: 5px;padding: 0 15px;font-size: 12px;color: #50545E;" @click='open'>
					<view class="title" style="margin-right: 10px;">时间筛选:<text style="margin-left: 15px;">
							{{ flightDate }}</text> </view>
					<!-- <picker style="width: calc(100% - 60px);" class="time-picker" mode="date" :value="flightDate"
						@change="bindTimeChange($event, 2)">
						<text class="iconfont icon-calendar-todo-fill"></text>
						<view class="time" style="display: inline;">{{ flightDate }}</view>
					</picker> -->
				</view>
				<view class="uni-form-item uni-column" style="display: flex;height: 30px;line-height: 30px;background: #fff;
				margin-bottom: 10px;border-radius: 5px;padding: 0 15px;font-size: 12px;color: #50545E;">
					<view class="title" style="margin-right: 10px;width: 60px;">基地:</view>
					<picker @change="bindPickerChangeAirport" :value="indexAirport" :range="arrayAirport"
						range-key="name">
						<view class="uni-input colorBlue">{{arrayAirport[indexAirport].name}}</view>
					</picker>
				</view>
				<study-passenger-sortie v-for="(item, index) in taskData" v-on:listenToChildEvent='getData' :key="index"
					:taskIndex="index" :flightDate='flightDate' :taskInfo="item" :flight="1">
				</study-passenger-sortie>

				<view class="warp">
					<view>
						<view style="width: 100%;line-height: 40px;z-index: 100;">
							<view class="detail-block text-center">
								<p class="fz-14 col-gray-600" @click="addGroup()">新增架次</p>
							</view>
						</view>
					</view>
				</view>


				<view class="no-data-box" v-if="noData">
					<image class="image" src="../../../static/images/nodata.png" mode="widthFix"></image>
					<text class="col-gray-500">当前无数据</text>
				</view>


			</view>
		</scroll-view>



		<view>
			<uni-calendar ref="calendar" :insert="false" @confirm="confirm" />
		</view>
		<!-- 整体背景放在最后 -->
		<view class="bg-main">
			<image class="background" src="../../../static/images/background.png"></image>
		</view>





	</view>







</template>
<script>
	import {
		getHomeInfo,
		getFlightPlansDate,
		sortieUngroupedList,
		sortieDetailsList,
		studyOrderAirportList
	} from '../../../api/weChat.js';
	const App = getApp();
	export default {
		data() {
			return {
				arrayAirport: [],
				indexAirport: 0,
				currentTime: '',
				homeInfo: {},
				taskData: [],
				text: 'uni-app',
				navH: 0,
				isRefresh: true, // 开启下拉
				triggered: false,
				loadingText: '正在刷新',
				noData: false,
				_freshing: false,
				pageNum: 1,
				flag: true,
				flightDate: ''
			};
		},
		onLoad: function(options) {
			//自定义导航
			this._freshing = false;
			this.pageNum = 1
			this.flag = true
			this.getCurrentTime();
			// this.getArrayAirport();
			let that = this
			this.$set(this.$data, 'navH', Number(App.globalData.navHeight));



		},
		onShow: function(options) {

			this.getArrayAirport();
		},

		methods: {
			addGroup() {
				let that = this
				let param = {
					groupDate: that.flightDate,
					areaCode: that.arrayAirport[that.indexAirport].airportCode,
					passengerGroup: []
				}
				uni.navigateTo({
					url: '/pages/study/sortie/details?param=' + JSON.stringify(param)
				});
			},
			bindPickerChangeAirport(e) {
				this.indexAirport = e.detail.value
				this.getData();
			},
			async getArrayAirport() {
				let param = {
					companyCode: uni.getStorageSync('userInfo').companyCode
				};
				try {
					const res = await studyOrderAirportList(param);
					console.log(res)
					if (res.response.code == 200) {
						this.arrayAirport = res.response.data
						this.getData();
					}

				} catch (e) {
					console.error(e)
				}
			},
			open() {
				this.$refs.calendar.open();
			},
			confirm(e) {
				this.flightDate = e.fulldate
				this.pageNum = 1
				this.getData();
			},
			bindTimeChange(e) {
				console.log(e)
				this.flightDate = e.detail.value
				this.getData();
			},
			backMyPage() {
				uni.switchTab({
					url: '/pages/home/<USER>'
				})
			},
			// onBottomRefresh() {
			// 	if (this.flag) {
			// 		this.pageNum += 1
			// 		this.getData(1);
			// 	}
			// },
			getCurrentTime(type) {
				var myDate = new Date();
				var y = myDate.getFullYear()
				var m = myDate.getMonth() + 1
				m = m < 10 ? '0' + m : m
				var d = myDate.getDate()
				d = d < 10 ? ('0' + d) : d
				var week = myDate.getDay()
				var x;
				switch (week) {
					case 0:
						x = '周日';
						break;
					case 1:
						x = '周一';
						break;
					case 2:
						x = '周二';
						break;
					case 3:
						x = '周三';
						break;
					case 4:
						x = '周四';
						break;
					case 5:
						x = '周五';
						break;
					case 6:
						x = '周六';
						break;
				}
				this.currentTime = y + '/' + m + '/' + d + '  ' + x;
				if (type != 1) {
					this.flightDate = y + '-' + m + '-' + d;
				}
			},
			async getData(type) {
				console.log(uni.getStorageSync('userInfo'))
				let that = this

				console.log(that.arrayAirport)
				let homeParam = {
					openId: uni.getStorageSync('userInfo').openId,
					groupDate: that.flightDate,
					areaCode: that.arrayAirport[that.indexAirport].airportCode
				};
				try {
					const res = await sortieDetailsList(homeParam);
					console.log(res)

					if (res.response.data.length === 0) {
						this.noData = true;
						that.taskData = []
					} else {
						that.taskData = res.response.data
						this.noData = false;
					}
				} catch (e) {
					console.error(e)
				}
				console.log(this.flag)
			},
			openLoading() { //被下拉
				this.triggered = true;
			},
			// 触发下拉刷新
			onRefresh() {
				if (this._freshing) return;
				this._freshing = true;
				if (!this.triggered) {
					this.triggered = true;
				}
				// this.loadStoreData();
				this.pageNum = 1
				this.flag = true
				this.triggered = false;
				this._freshing = false;
				this.getCurrentTime(1);
				this.getData();

			},
			// 下拉刷新复位
			onRestore() {
				this.triggered = false;
				this._freshing = false;
			},
			// 下拉刷新中止
			onAbort() {
				this.triggered = false;
				this._freshing = false;
			},
		}
	};
</script>

<style lang="scss" scoped>
	page {
		height: 100%;
	}

	.warp {
		position: relative;
		overflow: hidden;
		background-color: rgba(255, 255, 255, 0.64);
		border: 1px solid rgba(255, 255, 255, 0.72);
		border-radius: 8px;
		margin-bottom: 12px;

	}

	.detail-row {
		padding: 12px 16px;
		display: flex;
		width: 100%;
		backdrop-filter: blur(16px);
		flex-wrap: wrap;
		justify-content: space-between;
	}

	.warp_plane {
		width: 190px;
		position: absolute;
		top: 0;
		right: 0;
	}


	.warp_main {
		padding: 24rpx 0rpx 0 32rpx;
		height: 45px;
		background-color: rgba(0, 0, 0, 0);

		.header {
			// padding-right: 84rpx;
			height: 16px;

			.index {
				width: 16px;
				height: 16px;
				line-height: 16px;
				text-align: center;
				margin-right: 13px;
				border-radius: 50%;
				color: #838791;
				background-color: #EAEAEB;
				display: inline-block;
			}

			.marRight4 {
				margin-right: 8rpx;
			}

			.marRight33 {
				margin-right: 10rpx;
			}
		}

		.trip-address {
			margin-top: 16px;
			margin-bottom: 12px;

			.fz-16 {
				font-weight: 800;
			}
		}

		.trip-bar {
			width: 16px;
			height: 104px;
			margin-right: 12px;
		}

		.show-view {
			height: 330px;
			transition: .5s;
			backdrop-filter: blur(16px);
		}

		.close-view {
			height: 0;
			transition: .5s;
			overflow: hidden;
		}
	}

	.background {
		width: 100%;
		height: 100%;
		background-size: 100% 100%;
		z-index: -1;
		position: absolute;
		top: 0px;
		bottom: 0px;
	}





	.main {
		width: 100%;
		height: 100%;
	}


	.nav {
		width: 100%;
		overflow: hidden;
		position: relative;
		top: 0;
		left: 0;
		z-index: 10;
	}

	.nav-title {
		width: 100%;
		height: inherit;
		position: relative;
		z-index: 10;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 15px;

		.nav-title-text {
			// padding-top: 10px;
			font-family: SF Pro Text;
			font-style: normal;
			font-weight: 600;
			font-size: 16px;
			color: #000000;

			&:first-child {
				font-weight: normal;
				position: absolute;
				left: 25px;
				font-size: 22px;
			}
		}
	}


	.refresh {
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		top: 0;
		width: 100%;
		height: 100%;
		background-color: #eeeeee;
		color: #000000;
	}

	.content {
		padding: 0 16px;
	}

	.refresh-container {
		width: 750rpx;
		text-align: center;
		position: absolute;
		align-items: center;
		margin-bottom: 20px;
	}

	.refresh-container text {}

	.no-data-box {
		background: rgba(255, 255, 255, 0.64);
		height: 343px;
		border-radius: 8px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		.image {
			width: 140px;
			height: 140px;
		}

		.col-gray-500 {
			font-size: 14px;
		}
	}

	.spinning {
		margin-right: 4px;
		display: inline-block;
		-webkit-animation: rotate 1s linear infinite;
		animation: rotate 1s linear infinite;
	}

	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
		}
	}

	.statistics {
		margin-bottom: 12px;
		padding: 0 16px;
		background-color: #fff;
		background: rgba(255, 255, 255, 0.64);
		border: 1px solid rgba(255, 255, 255, 0.72);
		border-radius: 8px;
	}
</style>