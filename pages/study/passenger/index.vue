<template>
	<view class="container">
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">旅客列表</text>
			</view>
		</view>
		<scroll-view class="bg-gray overflow" :style="'height:calc(100vh - ' + navH + 'px)'" scroll-y
			@scrolltolower="onBottomRefresh" @refresherrefresh="onRefresh" :refresher-enabled="isRefresh"
			:refresher-triggered="triggered" @refresherrestore="onRestore" @refresherpulling="openLoading"
			refresher-default-style="none" @refresherabort="onAbort">
			<view class="refresh-container" slot="refresher">
				<view class="spinning"><text class="iconfont icon-loader-2-fill"></text></view>
				<text>{{ loadingText }}</text>
			</view>
			<view class="content">
				<view class="statistics flex-row justify-between">
					<view>
						<p>
							<span class="col-gray-500 fz-12 margin-right-8">当前时间</span>
							<span class="col-gray-600 fz-12">{{ currentTime }}</span>
						</p>
						<!-- 	<p>
							<span class="col-gray-500 fz-12 margin-right-8">最后更新</span>
							<span class="col-gray-600 fz-12">{{ homeInfo.lastUpdateTime }}</span>
						</p> -->
					</view>
					<view class="text-center">
						<p class="col-gray-600 fz-16">{{ homeInfo.total }}</p>
						<p class="col-gray-500 fz-12">总计数据</p>
					</view>
				</view>


				<view class="detail-row bg-white-500 no-fold" style="padding-top: 10;">
					<view class="detail-block">
						<p class="fz-12 col-gray-500">航班号</p>
						<p class="fz-14 col-gray-600">{{param.flightNo}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">航班日期</p>
						<p class="fz-14 col-gray-600">{{param.execDate}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">条码</p>
						<p class="fz-14 col-gray-600">{{param.passCheck}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">登机</p>
						<p class="fz-14 col-gray-600" style="color: #4858af;" @click.stop='scanCodeBoarding()'>扫码登机
						</p>
					</view>
				</view>

				<view class="detail-row bg-white-500 no-fold" style="padding-top: 10;">
					<view class="detail-block">
						<p class="fz-12 col-gray-500">总人数</p>
						<p class="fz-14 col-gray-600">{{passengerCount.passCount}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">已值机</p>
						<p class="fz-14 col-gray-600">{{passengerCount.passCheckIn}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">已安检</p>
						<p class="fz-14 col-gray-600">{{passengerCount.passCheck}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">已登机</p>
						<p class="fz-14 col-gray-600">{{passengerCount.passBoarding}}</p>
					</view>
				</view>



				<study-passenger-card v-for="(item, index) in taskData" v-on:listenToChildEvent='getData' :key="index"
					:taskIndex="index" :flightDate='flightDate' :taskInfo="item" :flight="1" @custom-event="refresh">
				</study-passenger-card>
				<view class="no-data-box" v-if="noData">
					<image class="image" src="../../../static/images/nodata.png" mode="widthFix"></image>
					<text class="col-gray-500">当前无数据</text>
				</view>
			</view>
		</scroll-view>

		<!-- 整体背景放在最后 -->
		<view class="bg-main">
			<image class="background" src="../../../static/images/background.png"></image>
		</view>
	</view>
</template>
<script>
	import {
		studyPassengerList,
		studyPassengerScanCodeToBoarding,
		studyFlightPlanDetails
	} from '../../../api/weChat.js';
	const App = getApp();
	export default {
		data() {
			return {
				passengerCount: {},
				param: {},
				currentTime: '',
				homeInfo: {},
				taskData: [],
				text: 'uni-app',
				navH: 0,
				isRefresh: true, // 开启下拉
				triggered: false,
				loadingText: '正在刷新',
				noData: false,
				_freshing: false,
				flag: true,
				flightDate: ''
			};
		},
		onLoad: function(options) {

			this.param = JSON.parse(options.param)
			//自定义导航
			this._freshing = false;
			this.param.pageNum = 1
			this.param.pageSize = 10
			this.flag = true
			this.getCurrentTime();
			this.getData()
			this.refreshPassenger()
			this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
		},
		onShow: function(options) {

		},

		methods: {

			async refreshPassenger() {
				let that = this
				let param = {
					execDate: that.param.execDate,
					flightNumber: that.param.flightNo
				}
				const res = await studyFlightPlanDetails(param);
				if (res.response.code == 200) {
					that.passengerCount = res.response.data
				}else{
					that.passengerCount = that.param
				}
				console.log(res)


			},

			async boarding(e) {
				let that = this
				const res = await studyPassengerScanCodeToBoarding(e);
				console.log(res)
				if (res.response.code == 200 && res.response.data == true) {
					uni.showToast({
						title: "登机成功"
					})
					setTimeout(function() {
						that.getData()
					}, 500)

				} else {
					uni.showModal({
						content: "登机失败"
					})
				}

			},
			scanCodeBoarding() {
				let that = this
				uni.scanCode({
					onlyFromCamera: true,
					scanType: ['pdf417', 'qrCode', 'barCode', 'datamatrix'],
					success(scanRes) {
						let postJson = {
							flightNumber: that.param.flightNo,
							execDate: that.param.execDate,
							boardCard: scanRes.result,
							group: that.param.group,
							boardingType: 2
						}
						that.boarding(postJson)
					}
				})
			},
			refresh() {
				this.getData()
				this.refreshPassenger()
			},

			bindTimeChange(e) {
				console.log(e)
				this.flightDate = e.detail.value
				this.refresh();
			},
			backMyPage() {
				uni.navigateBack()
			},
			onBottomRefresh() {
				if (this.flag) {
					this.param.pageNum += 1
					this.getData(1);
					this.refreshPassenger()
				}
			},
			getCurrentTime(type) {
				var myDate = new Date();
				var y = myDate.getFullYear()
				var m = myDate.getMonth() + 1
				m = m < 10 ? '0' + m : m
				var d = myDate.getDate()
				d = d < 10 ? ('0' + d) : d
				var week = myDate.getDay()
				var x;
				switch (week) {
					case 0:
						x = '周日';
						break;
					case 1:
						x = '周一';
						break;
					case 2:
						x = '周二';
						break;
					case 3:
						x = '周三';
						break;
					case 4:
						x = '周四';
						break;
					case 5:
						x = '周五';
						break;
					case 6:
						x = '周六';
						break;
				}
				this.currentTime = y + '/' + m + '/' + d + '  ' + x;
				if (type != 1) {
					this.flightDate = y + '-' + m + '-' + d;
				}
			},
			async getData(type) {

				try {
					const res = await studyPassengerList(this.param);
					console.log(res)
					if(this.pageNum>1 && res.response.rows.length == 0){
						return 
					}
					if (res.response.rows != null && res.response.rows.length != 0) {
						this.homeInfo.total = res.response.total
						console.log(res)
						if (res.response.rows.length < 10) {
							this.flag = false
						}
						if (type == 1) {
							this.taskData = this.taskData.concat(res.response.rows);
						} else {
							this.taskData = []
							this.taskData = res.response.rows
						}
					} else {
						this.taskData = []
						this.noData = true;
					}
					if (this.taskData.length === 0 || res.response.rows == null) {
						this.noData = true;
					} else {
						this.noData = false;
					}
				} catch (e) {
					console.error(e)
				}
				console.log(this.flag)
			},
			openLoading() { //被下拉
				this.triggered = true;
			},
			// 触发下拉刷新
			onRefresh() {
				if (this._freshing) return;
				this._freshing = true;
				if (!this.triggered) {
					this.triggered = true;
				}
				// this.loadStoreData();
				this.param.pageNum = 1
				this.flag = true
				this.triggered = false;
				this._freshing = false;
				this.getCurrentTime(1);
				
				this.refresh();

			},
			// 下拉刷新复位
			onRestore() {
				this.triggered = false;
				this._freshing = false;
			},
			// 下拉刷新中止
			onAbort() {
				this.triggered = false;
				this._freshing = false;
			},
		}
	};
</script>

<style lang="scss" scoped>
	page {
		height: 100%;
	}


	.detail-row {
		padding: 12px 16px;
		display: flex;
		width: 100%;
		backdrop-filter: blur(16px);
		justify-content: space-between;
	}

	.detail-block {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: top;
		align-items: center;
		position: relative;

		.phone {
			position: absolute;
			bottom: -17px;
			font-size: 12px;
			color: #2C5DE5;
		}

		.icon-phone-fill {
			font-size: 12px !important;
		}

		.fz-14 {
			font-weight: 600;
		}
	}

	//机型机号等样式调整
	.no-fold {
		padding-bottom: 15px;
	}


	.background {
		width: 100%;
		height: 100%;
		background-size: 100% 100%;
		z-index: -1;
		position: absolute;
		top: 0px;
		bottom: 0px;
	}

	.main {
		width: 100%;
		height: 100%;
	}


	.nav {
		width: 100%;
		overflow: hidden;
		position: relative;
		top: 0;
		left: 0;
		z-index: 10;
	}

	.nav-title {
		width: 100%;
		height: inherit;
		position: relative;
		z-index: 10;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 15px;

		.nav-title-text {
			// padding-top: 10px;
			font-family: SF Pro Text;
			font-style: normal;
			font-weight: 600;
			font-size: 16px;
			color: #000000;

			&:first-child {
				font-weight: normal;
				position: absolute;
				left: 25px;
				font-size: 22px;
			}
		}
	}


	.refresh {
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		top: 0;
		width: 100%;
		height: 100%;
		background-color: #eeeeee;
		color: #000000;
	}

	.content {
		padding: 0 16px;
	}

	.refresh-container {
		width: 750rpx;
		text-align: center;
		position: absolute;
		align-items: center;
		margin-bottom: 20px;
	}

	.refresh-container text {}

	.no-data-box {
		background: rgba(255, 255, 255, 0.64);
		height: 343px;
		border-radius: 8px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		.image {
			width: 140px;
			height: 140px;
		}

		.col-gray-500 {
			font-size: 14px;
		}
	}

	.spinning {
		margin-right: 4px;
		display: inline-block;
		-webkit-animation: rotate 1s linear infinite;
		animation: rotate 1s linear infinite;
	}

	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
		}
	}

	.statistics {
		padding: 0 16px;
		background-color: #fff;
		background: rgba(255, 255, 255, 0.64);
		border: 1px solid rgba(255, 255, 255, 0.72);
		border-radius: 8px;
	}
</style>