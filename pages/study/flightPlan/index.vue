<template>
	<view class="container">
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">今日航班</text>
			</view>
		</view>
		<scroll-view class="bg-gray overflow" :style="'height:calc(100vh - ' + navH + 'px)'" scroll-y
			@scrolltolower="onBottomRefresh" @refresherrefresh="onRefresh" :refresher-enabled="isRefresh"
			:refresher-triggered="triggered" @refresherrestore="onRestore" @refresherpulling="openLoading"
			refresher-default-style="none" @refresherabort="onAbort">
			<view class="refresh-container" slot="refresher">
				<view class="spinning"><text class="iconfont icon-loader-2-fill"></text></view>
				<text>{{ loadingText }}</text>
			</view>
			<view class="content">
				<view class="statistics flex-row justify-between"  style="align-items: center;">
					<view>
						<p>
							<span class="col-gray-500 fz-12 margin-right-8">当前时间</span>
							<span class="col-gray-600 fz-12">{{ currentTime }}</span>
						</p>
						<!-- 	<p>
							<span class="col-gray-500 fz-12 margin-right-8">最后更新</span>
							<span class="col-gray-600 fz-12">{{ homeInfo.lastUpdateTime }}</span>
						</p> -->
					</view>
					<view class="text-center">
						<p class="col-gray-600 fz-16">{{ homeInfo.total }}</p>
						<p class="col-gray-500 fz-12">总计数据</p>
					</view>
				</view>
				<view class="uni-form-item uni-column" style="display: flex;height: 30px;line-height: 30px;background: #fff;
    margin-bottom: 10px;border-radius: 5px;padding: 0 15px;font-size: 12px;color: #50545E;"  @click='open'>
					<view class="title" style="margin-right: 10px;">时间筛选:<text style="margin-left: 15px;"> {{ flightDate }}</text> </view>
					<!-- <picker style="width: calc(100% - 60px);" class="time-picker" mode="date" :value="flightDate"
						@change="bindTimeChange($event, 2)">
						<text class="iconfont icon-calendar-todo-fill"></text>
						<view class="time" style="display: inline;">{{ flightDate }}</view>
					</picker> -->
				</view>
				<view class="uni-form-item uni-column" style="display: flex;height: 30px;line-height: 30px;background: #fff;
				margin-bottom: 10px;border-radius: 5px;padding: 0 15px;font-size: 12px;color: #50545E;">
					<view class="title" style="margin-right: 10px;width: 60px;">类型筛选:</view>
					<picker @change="bindPickerChangeType" :value="index" :range="arrayType">
						<view class="uni-input">{{arrayType[indexType]}}</view>
					</picker>
				</view>
				<study-flight-card v-for="(item, index) in taskData" v-on:listenToChildEvent='getData' :key="index"
					:taskIndex="index" :flightDate='flightDate' :taskInfo="item" :flight="1">
				</study-flight-card>
				<view class="no-data-box" v-if="noData">
					<image class="image" src="../../../static/images/nodata.png" mode="widthFix"></image>
					<text class="col-gray-500">当前无数据</text>
				</view>
			</view>
		</scroll-view>
		<view>
		    <uni-calendar 
		    ref="calendar"
		    :insert="false"
		    @confirm="confirm"
		     />
		</view>
		<!-- 整体背景放在最后 -->
		<view class="bg-main">
			<image class="background" src="../../../static/images/background.png"></image>
		</view>
	</view>
</template>
<script>
	import {
		getHomeInfo,
		getFlightPlansDate,
		studyFlightPlanList
	} from '../../../api/weChat.js';
	const App = getApp();
	export default {
		data() {
			return {
				arrayType: ['全部', '短途运输', '低空旅游/跳伞'],
				indexType: 0,
				currentTime: '',
				homeInfo: {},
				taskData: [],
				text: 'uni-app',
				navH: 0,
				isRefresh: true, // 开启下拉
				triggered: false,
				loadingText: '正在刷新',
				noData: false,
				_freshing: false,
				pageNum: 1,
				flag: true,
				flightDate: ''
			};
		},
		onLoad: function(options) {
			//自定义导航
			this._freshing = false;
			this.pageNum = 1
			this.flag = true
			this.getCurrentTime();
			this.getData();
			this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
		},
		onShow: function(options) {

		},

		methods: {
			open(){
				this.$refs.calendar.open();
			},
			confirm(e) {
				this.flightDate = e.fulldate
				this.pageNum = 1
				this.getData();
			},
			bindPickerChangeType: function(e) {
				this.indexType = e.detail.value
				this.getData();
			},
			bindTimeChange(e) {
				console.log(e)
				this.flightDate = e.detail.value
				this.getData();
			},
			backMyPage() {
				uni.switchTab({
					url: '/pages/home/<USER>'
				})
			},
			onBottomRefresh() {
				if (this.flag) {
					this.pageNum += 1
					this.getData(1);
				}
			},
			getCurrentTime(type) {
				var myDate = new Date();
				var y = myDate.getFullYear()
				var m = myDate.getMonth() + 1
				m = m < 10 ? '0' + m : m
				var d = myDate.getDate()
				d = d < 10 ? ('0' + d) : d
				var week = myDate.getDay()
				var x;
				switch (week) {
					case 0:
						x = '周日';
						break;
					case 1:
						x = '周一';
						break;
					case 2:
						x = '周二';
						break;
					case 3:
						x = '周三';
						break;
					case 4:
						x = '周四';
						break;
					case 5:
						x = '周五';
						break;
					case 6:
						x = '周六';
						break;
				}
				this.currentTime = y + '/' + m + '/' + d + '  ' + x;
				if (type != 1) {
					this.flightDate = y + '-' + m + '-' + d;
				}
			},
			async getData(type) {
				console.log(uni.getStorageSync('userInfo'))
				let homeParam = {
					openId: uni.getStorageSync('userInfo').openId,
					pageNum: this.pageNum,
					pageSize: 10,
					execDate: this.flightDate.replaceAll("-", ""),
					type:this.indexType
				};
				try {
					const res = await studyFlightPlanList(homeParam);
					console.log(res)
					if(this.pageNum>1 && res.response.rows.length == 0){
						return 
					}
					if (res.response.rows != null && res.response.rows.length != 0) {
						this.homeInfo.total = res.response.total
						console.log(res)
						if (res.response.rows.length < 10) {
							this.flag = false
						}
						if (type == 1) {
							this.taskData = this.taskData.concat(res.response.rows);
						} else {
							this.taskData = []
							this.taskData = res.response.rows
						}
					} else {
						this.homeInfo.total = 0
						this.taskData = []
						this.noData = true;
					}
					if (this.taskData.length === 0 || res.response.rows == null) {
						this.noData = true;
					} else {
						this.noData = false;
					}
				} catch (e) {
					console.error(e)
				}
				console.log(this.flag)
			},
			openLoading() { //被下拉
				this.triggered = true;
			},
			// 触发下拉刷新
			onRefresh() {
				if (this._freshing) return;
				this._freshing = true;
				if (!this.triggered) {
					this.triggered = true;
				}
				// this.loadStoreData();
				this.pageNum = 1
				this.flag = true
				this.triggered = false;
				this._freshing = false;
				this.getCurrentTime(1);
				this.getData();

			},
			// 下拉刷新复位
			onRestore() {
				this.triggered = false;
				this._freshing = false;
			},
			// 下拉刷新中止
			onAbort() {
				this.triggered = false;
				this._freshing = false;
			},
		}
	};
</script>

<style lang="scss" scoped>
	page {
		height: 100%;
	}

	.background {
		width: 100%;
		height: 100%;
		background-size: 100% 100%;
		z-index: -1;
		position: absolute;
		top: 0px;
		bottom: 0px;
	}

	.main {
		width: 100%;
		height: 100%;
	}


	.nav {
		width: 100%;
		overflow: hidden;
		position: relative;
		top: 0;
		left: 0;
		z-index: 10;
	}

	.nav-title {
		width: 100%;
		height: inherit;
		position: relative;
		z-index: 10;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 15px;

		.nav-title-text {
			// padding-top: 10px;
			font-family: SF Pro Text;
			font-style: normal;
			font-weight: 600;
			font-size: 16px;
			color: #000000;

			&:first-child {
				font-weight: normal;
				position: absolute;
				left: 25px;
				font-size: 22px;
			}
		}
	}


	.refresh {
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		top: 0;
		width: 100%;
		height: 100%;
		background-color: #eeeeee;
		color: #000000;
	}

	.content {
		padding: 0 16px;
	}

	.refresh-container {
		width: 750rpx;
		text-align: center;
		position: absolute;
		align-items: center;
		margin-bottom: 20px;
	}

	.refresh-container text {}

	.no-data-box {
		background: rgba(255, 255, 255, 0.64);
		height: 343px;
		border-radius: 8px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		.image {
			width: 140px;
			height: 140px;
		}

		.col-gray-500 {
			font-size: 14px;
		}
	}

	.spinning {
		margin-right: 4px;
		display: inline-block;
		-webkit-animation: rotate 1s linear infinite;
		animation: rotate 1s linear infinite;
	}

	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
		}
	}

	.statistics {
		margin-bottom: 12px;
		padding: 0 16px;
		background-color: #fff;
		background: rgba(255, 255, 255, 0.64);
		border: 1px solid rgba(255, 255, 255, 0.72);
		border-radius: 8px;
	}
</style>