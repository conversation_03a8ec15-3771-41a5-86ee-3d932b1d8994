<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">创建订单</text>
			</view>
		</view>
		<!-- 内容主体 -->
		<scroll-view class="bg-gray overflow" :style="'height:calc(100vh - ' + navH + 'px)'" scroll-y
			@scrolltolower="onBottomRefresh" @refresherrefresh="onRefresh" :refresher-enabled="isRefresh"
			:refresher-triggered="triggered" @refresherrestore="onRestore" @refresherpulling="openLoading"
			refresher-default-style="none" @refresherabort="onAbort">
			<view class="refresh-container" slot="refresher">
				<view class="spinning"><text class="iconfont icon-loader-2-fill"></text></view>
				<text>{{ loadingText }}</text>
			</view>
			<view class="content">
				<view class="box">
					<view class="leftBox">订单日期</view>
					<view class="rightBox">{{order.orderDate}}</view>
				</view>
				<view class="box">
					<view class="leftBox">订单编号</view>
					<view class="rightBox">{{orderNo}}</view>
				</view>
				<view class="box">
					<view class="leftBox">创建日期</view>
					<view class="rightBox">{{order.createTime}}</view>
				</view>
				<view class="box">
					<view class="leftBox">所属机场</view>
					<view class="rightBox">{{order.airportName}}</view>
				</view>
				<view class="box">
					<view class="leftBox">请选择代理人</view>
					<view class="rightBox">{{order.agentName}}</view>
				</view>
				<view class="box">
					<view class="leftBox">订单说明</view>
					<view class="rightBox">{{order.remarks}}</view>
				</view>



				<view class="box" style="text-align: center;">
					<image @click="openImg" style="width:200px;height:160px;" :src="order.img">
					</image>
				</view>
				<view class="box" style="text-align: center;">

					<button @click="toPassengerAdd" class="mini-btn" type="primary" size="mini"
						style="color:#ffffff;background-color:#87dfef;">
						添加旅客</button>

				</view>

				<view class="statistics flex-row">
					<view class="text-center" style="padding: 10px 0;">
						<p class="col-gray-500 fz-12">总计数据 ：{{ homeInfo.total }}</p>
					</view>
				</view>


				<study-passenger-list-card v-for="(item, index) in taskData" v-on:listenToChildEvent='getData'
					:key="index" :taskIndex="index" :flightDate='flightDate' :taskInfo="item" :flight="1"
					@custom-event="refresh">
				</study-passenger-list-card>
				<view class="no-data-box" v-if="noData">
					<image class="image" src="../../../static/images/nodata.png" mode="widthFix"></image>
					<text class="col-gray-500">当前无数据</text>
				</view>


			</view>


		</scroll-view>




		<!-- 整体背景放在最后 -->
		<view class="bg-main">
			<image class="background" src="../../../static/images/background.png"></image>
		</view>
	</view>
</template>

<script>
	import {
		studyOrderByOrderNo,
		studyOrderPassengerList,
		baseUrl
	} from '../../../api/weChat.js';

	const App = getApp();
	export default {
		data() {
			return {
				isRefresh: true, // 开启下拉
				triggered: false,
				loadingText: '正在刷新',
				noData: false,
				_freshing: false,
				taskData: [],
				pageNum: 1,
				flag: true,
				navH: 0,
				taskIndex: 1,
				homeInfo: {
					"total":0
				},
				inputValue: '',
				order: {

				},
				componentData: '',
				orderNo: '',
				orderSrc: "../../../static/images/logos.png"

			};
		},
		mounted() {

		},
		onLoad: function(options) {
			console.log(options)
			this.orderNo = options.orderNo
			this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
			this._freshing = false;
			this.pageNum = 1
			this.flag = true
			
			console.log(123)
			// this.queryPassengerList()
		},
		onShow: function(options) {
			this.pageNum = 1
			this.flag = true
			console.log(456)
			this.queryOrder()
			// uni.setStorageSync('flightSortiesId', this.flightSortiesId);
			// this.queryPassengerList()
		},
		methods: {
			refresh() {
				this.pageNum = 1
				this.flag = true
				this.queryPassengerList()
			},
			toPassengerAdd() {
				uni.navigateTo({
					url: "add?orderNo=" + this.orderNo
				})

			},
			async queryPassengerList(type) {
				let that = this
				let homeParam = {
					orderNo: this.orderNo,
					pageNum: this.pageNum,
					pageSize: 10,
				};
				try {
					const res = await studyOrderPassengerList(homeParam);
					if (res.response.code == 200) {
						this.homeInfo.total = res.response.total
						console.log(res)
						// this.taskData = res.response.data.flightplanList;
						if (res.response.rows.length < 10) {
							this.flag = false
						}
						if (type == 1) {
							this.taskData = this.taskData.concat(res.response.rows);
						} else {
							this.taskData = []
							this.taskData = res.response.rows
						}
					} else {
						this.taskData = []
						this.noData = true;
					}
					if (this.taskData.length === 0 || res.response.rows == null) {
						this.noData = true;
					} else {
						this.noData = false;
					}
					console.log("this.noData")
					console.log(this.noData)
				} catch (e) {
					console.error(e)
				}
				that.taskData.forEach(e => {
					if(!e.photo.includes(baseUrl)){
						e.photo = baseUrl + e.photo
					}
				})

				console.log(this.flag)
			},

			openImg(e) {
				let that = this
				uni.previewImage({
					urls: [that.order.img]
				})
			},

			// 返回"我的"主页面
			backMyPage() {
				uni.navigateBack()
			},

			async queryOrder() {
				let param = {
					orderNo: this.orderNo
				};
				try {
					
					const res = await studyOrderByOrderNo(param);
					this.queryPassengerList()
					console.log(res)
					if (res.response.code == 200) {
						this.order = res.response.data
					}

				} catch (e) {
					console.error(e)
				}
			},
			onBottomRefresh() {
				console.log(1111)
				if (this.flag) {
					this.pageNum += 1
					this.queryPassengerList(1);
				}
			},

			openLoading() { //被下拉
				this.triggered = true;
			},
			// 触发下拉刷新
			onRefresh() {
				if (this._freshing) return;
				this._freshing = true;
				if (!this.triggered) {
					this.triggered = true;
				}
				// this.loadStoreData();
				this.pageNum = 1
				this.flag = true
				this.triggered = false;
				this._freshing = false;
				this.queryPassengerList();

			},
			// 下拉刷新复位
			onRestore() {
				this.triggered = false;
				this._freshing = false;
			},
			// 下拉刷新中止
			onAbort() {
				this.triggered = false;
				this._freshing = false;
			},

		}
	};
</script>

<style lang="less" scoped>
	.content {
		overflow: auto;

		.box {
			font-size: 13px;
			padding: 10px 10px 10px 30px;
			background-color: #ffffff85;
			border-bottom: 1px solid #efefef;

			.leftBox {
				width: 150px;
				display: inline-block;
			}

			.rightBox {
				display: inline-block;
			}
		}
	}

	.refresh-container {
		width: 750rpx;
		text-align: center;
		position: absolute;
		align-items: center;
		margin-bottom: 20px;
	}

	.refresh-container text {}

	page {
		height: 100%;
	}

	.colorBlue {
		color: #4858af;
	}

	.background {
		width: 100%;
		height: 100%;
		// position: fixed;
		background-size: 100% 100%;
		z-index: -1;
		position: absolute;
		top: 0px;
		bottom: 0px;
		z-index: -1;
	}

	.nav {
		width: 100%;
		overflow: hidden;
		position: relative;
		top: 0;
		left: 0;
		z-index: 10;
	}

	.nav-title {
		width: 100%;
		height: inherit;
		position: relative;
		z-index: 10;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 15px;

		.nav-title-text {
			// padding-top: 10px;
			font-family: SF Pro Text;
			font-style: normal;
			font-weight: 600;
			font-size: 16px;
			color: #000000;

			&:first-child {
				font-weight: normal;
				position: absolute;
				left: 25px;
				font-size: 22px;
			}
		}
	}
	.no-data-box {
		background: rgba(255, 255, 255, 0.64);
		height: 343px;
		border-radius: 8px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	
		.image {
			width: 140px;
			height: 140px;
		}
	
		.col-gray-500 {
			font-size: 14px;
		}
	}
</style>