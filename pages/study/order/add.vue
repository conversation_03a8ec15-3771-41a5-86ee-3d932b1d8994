<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">添加旅客</text>
			</view>
		</view>
		<!-- 内容主体 -->
		<view class="content" :style="'height:calc(100vh - ' + navH + 'px)'">
			<view class="box">
				<view class="leftBox">旅客姓名</view>
				<view class="rightBox">
					<input class="fz-12 colorBlue" v-model="passenger.name" placeholder="请输入旅客姓名" />
				</view>
			</view>
			<view class="box">
				<view class="leftBox">证件类型</view>
				<view class="rightBox">
					<picker @change="bindPickerChangeIdType" :value="indexIdType" :range="arrayIdType">
						<view class="uni-input colorBlue">{{arrayIdType[indexIdType]}}</view>
					</picker>
				</view>
			</view>
			<view class="box">
				<view class="leftBox">证件号</view>
				<view class="rightBox">
					<input class="fz-12 colorBlue" v-model="passenger.idNo" placeholder="请输入证件号" />
				</view>
			</view>
			<view class="box">
				<view class="leftBox">手机号</view>
				<view class="rightBox">
					<input class="fz-12 colorBlue" v-model="passenger.phone" placeholder="请输入手机号" />
				</view>
			</view>

			<view class="box">
				<view class="leftBox">备注</view>
				<view class="rightBox">
					<input class="fz-12 colorBlue" v-model="passenger.remarks" placeholder="请输入备注信息" />
				</view>
			</view>

			<view class="box">
				<view class="leftBox">图片</view>
				<view class="rightBox">
					<image @click="selectImg" style="width: 300rpx; height: 300rpx;" :src="passenger.photoBase64">
					</image>
				</view>
			</view>

			<view class="box">
				<view class="">
					图片仅供机场安检使用。该系统已和公安系统连接，添加成功即备案。
				</view>

			</view>


			<view class="box" style="text-align: center;">
				<button @click="passengerAdd" class="mini-btn" type="primary" size="mini"
					style="color:#ffffff;background-color:#87dfef;">
					添加旅客</button>
			</view>






		</view>

		<!-- 整体背景放在最后 -->
		<view class="bg-main">
			<image class="background" src="../../../static/images/background.png"></image>
		</view>
	</view>
</template>

<script>
	import {
		studyOrderPassengerAddUrl
	} from '../../../api/weChat.js';

	const App = getApp();
	export default {
		data() {
			return {
				isRefresh: true, // 开启下拉
				triggered: false,
				loadingText: '正在刷新',
				noData: false,
				triggered: false,
				_freshing: false,
				pageNum: 1,
				flag: true,
				navH: 0,
				orderSrc: "../../../static/images/logos.png",
				arrayIdType: ['身份证', '出生证明', '护照', '其他证'],
				indexIdType: 0,
				passenger: {
					photoBase64: '../../../static/images/photo_de.png'
				},
				studyOrderPassengerAddUrl: ""

			};
		},
		mounted() {

		},
		onLoad: function(options) {
			console.log(options)
			this.passenger.orderNo = options.orderNo
			this.passenger.createUser = uni.getStorageSync('userInfo').openId
			this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
			this.studyOrderPassengerAddUrl = studyOrderPassengerAddUrl

		},
		onShow: function(options) {
			// uni.setStorageSync('flightSortiesId', this.flightSortiesId);
		},
		methods: {
			isNotBull(e) {
				if (e == null || e == "") {
					return true;
				} else {
					return false;
				}
			},
			validate() {
			
				if (this.isNotBull(this.passenger.name)) {
					uni.showToast({
						icon: "error",
						title: "请输入姓名"
					})
					return false
				}
				if (this.isNotBull(this.passenger.idNo)) {
					uni.showToast({
						icon: "error",
						title: "请输入证件号"
					})
					return false
				}
				if (this.isNotBull(this.passenger.phone)) {
					uni.showToast({
						icon: "error",
						title: "请输入手机号"
					})
					return false
				}
				if (this.isNotBull(this.passenger.photoBase64) || this.passenger.photoBase64 ==
					'../../../static/images/photo_de.png') {
					uni.showToast({
						icon: "error",
						title: "请选择图片"
					})
					return false
				}
				return true;
			},
			passengerAdd() {
				let that = this
				uni.showLoading({
				    title: '加载中',
					mask:true
				});
				this.passenger.idType = this.indexIdType

				if (!this.validate()) {
					return false;
				}
				uni.uploadFile({
					url: that.studyOrderPassengerAddUrl, // 你的后端API地址
					filePath: that.passenger.photoBase64, // 通常是用户选择的文件的临时路径
					name: 'file', // 必须和后端Controller中的MultipartFile参数名一致
					formData: that.passenger,
					success: (uploadFileRes) => {
						uni.hideLoading()
						let result = JSON.parse(uploadFileRes.data)
						if (result.code == 200 && result.data == true) {
							uni.showToast({
								icon: "success",
								title: "添加成功"
							})
							setTimeout(function() {
								uni.navigateBack()
							}, 1000)
						} else {
							uni.showToast({
								icon: "error",
								title: "添加失败"
							})
						}
						// 处理上传成功后的结果
					},
					fail: (error) => {
						uni.hideLoading()
						console.error("上传失败", error);
						// 处理上传失败的情况
					}
				});




			},
			selectImg() {
				let that = this
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success(res) {
						let tempFilePaths = res.tempFilePaths
						that.passenger.photoBase64 = tempFilePaths[0]
					}
				})
			},
			bindPickerChangeIdType(e) {
				console.log(e.detail.value)
				this.indexIdType = e.detail.value
			},

			// 返回"我的"主页面
			backMyPage() {
				uni.navigateBack()
			},

			async queryOrder() {
				let param = {
					orderNo: this.orderNo
				};
				try {
					const res = await studyOrderByOrderNo(param);
					console.log(res)
					if (res.response.code == 200) {
						this.order = res.response.data
					}

				} catch (e) {
					console.error(e)
				}
			},


		}
	};
</script>

<style lang="less" scoped>
	.content {
		overflow: auto;

		.box {
			font-size: 13px;
			padding: 10px 10px 10px 30px;
			background-color: #ffffff85;
			border-bottom: 1px solid #efefef;

			.leftBox {
				width: 150px;
				display: inline-block;
			}

			.rightBox {
				display: inline-block;
			}
		}
	}

	page {
		height: 100%;
	}

	.colorBlue {
		color: #4858af;
	}

	.background {
		width: 100%;
		height: 100%;
		// position: fixed;
		background-size: 100% 100%;
		z-index: -1;
		position: absolute;
		top: 0px;
		bottom: 0px;
		z-index: -1;
	}

	.nav {
		width: 100%;
		overflow: hidden;
		position: relative;
		top: 0;
		left: 0;
		z-index: 10;
	}

	.nav-title {
		width: 100%;
		height: inherit;
		position: relative;
		z-index: 10;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 15px;

		.nav-title-text {
			// padding-top: 10px;
			font-family: SF Pro Text;
			font-style: normal;
			font-weight: 600;
			font-size: 16px;
			color: #000000;

			&:first-child {
				font-weight: normal;
				position: absolute;
				left: 25px;
				font-size: 22px;
			}
		}
	}
</style>