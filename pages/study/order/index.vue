<template>
	<view class="container">
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">低空研学</text>
			</view>
		</view>
		<scroll-view class="bg-gray overflow" :style="'height:calc(100vh - ' + navH + 'px)'" scroll-y
			@scrolltolower="onBottomRefresh" @refresherrefresh="onRefresh" :refresher-enabled="isRefresh"
			:refresher-triggered="triggered" @refresherrestore="onRestore" @refresherpulling="openLoading"
			refresher-default-style="none" @refresherabort="onAbort">
			<view class="refresh-container" slot="refresher">
				<view class="spinning"><text class="iconfont icon-loader-2-fill"></text></view>
				<text>{{ loadingText }}</text>
			</view>
			<view class="content">
				<view class="statistics flex-row justify-between"  style="align-items: center;">
					<view>
						<p>
							<span class="col-gray-500 fz-12 margin-right-8">当前时间</span>
							<span class="col-gray-600 fz-12">{{ currentTime }}</span>
						</p>
						<!-- 	<p>
							<span class="col-gray-500 fz-12 margin-right-8">最后更新</span>
							<span class="col-gray-600 fz-12">{{ homeInfo.lastUpdateTime }}</span>
						</p> -->
					</view>
					<view class="text-center">
						<p class="col-gray-600 fz-16">{{ homeInfo.total }}</p>
						<p class="col-gray-500 fz-12">总计数据</p>
					</view>
				</view>
				<view class="uni-form-item uni-column" style="display: flex;height: 30px;line-height: 30px;background: #fff;
    margin-bottom: 10px;border-radius: 5px;padding: 0 15px;font-size: 12px;color: #50545E;">
					<view @click='open' class="title" style="margin-right: 10px;">时间筛选:<text style="margin-left: 15px;"> {{ flightDate }}</text> </view>
					<!-- <picker style="" class="time-picker" mode="date" :value="flightDate"
						@change="bindTimeChange($event, 2)">
						<text class="iconfont icon-calendar-todo-fill"></text>
						<view class="time" style="display: inline;">{{ flightDate }}</view>
					</picker> -->
					<!-- <button @click="toCreateOrder" class="mini-btn" type="primary" size="mini"
						style="color:#ffffff;background-color:#87dfef;">
						创建订单</button> -->


					<view class="arrow bg-white-500" @click.stop="changeShow" style="float: right;">
						<text v-show="showMore" class="iconfont icon-arrow-up-line"><text
								class="fz-12 col-gray-500 colorBlue">关闭创建订单</text></text>
						<text v-show="!showMore" class="iconfont icon-arrow-down-line"><text
								class="fz-12 col-gray-500 colorBlue">展开创建订单</text></text>
					</view>
				</view>


				<view class="">
					<view :class="showMore ? 'show-view':'close-view'" class="bg-white-500 fold detail-row no-fold">

						<view class="content">
							<view class="box">
								<view class="leftBox">选择日期</view>
								<view class="rightBox">
									<picker style="" class="time-picker" mode="date" :value="orderDate"
										@change="bindTimeChangeOrder($event, 2)" :start="orderStartDate">
										<text class="iconfont icon-calendar-todo-fill"></text>
										<view class="time colorBlue" style="display: inline; ">{{ orderDate }}</view>
									</picker>
								</view>
							</view>
							<view class="box">
								<view class="leftBox">请选择机场</view>
								<view class="rightBox">
									<picker @change="bindPickerChangeAirport" :value="indexAirport"
										:range="arrayAirport" range-key="name">
										<view class="uni-input colorBlue">{{arrayAirport[indexAirport].name}}</view>
									</picker>
								</view>
							</view>
							<view class="box">
								<view class="leftBox">请选择代理人</view>
								<view class="rightBox">
									<picker @change="bindPickerChangeAgent" :value="indexAgent" :range="arrayAgent"
										range-key="agentName">
										<view class="uni-input colorBlue">{{arrayAgent[indexAgent].agentName}}</view>
									</picker>
								</view>
							</view>
							<view class="box">
								<view class="leftBox">订单说明</view>
								<view class="rightBox">
									<input class="fz-12 colorBlue" v-model="remarks" placeholder="请输入" />
								</view>
							</view>

							<view class="box">
								<view class="leftBox"></view>
								<view class="rightBox">
									<button @click="createOrder" class="mini-btn" type="primary" size="mini"
										style="color:#ffffff;background-color:#87dfef;">
										创建订单并生成二维码</button>
								</view>
							</view>

							<!-- <view class="box" style="text-align: center;margin:20rpx 0rpx 20rpx 0rpx">
								<image @click="openImg" style="width:200px;height:160px;" :src="orderSrc" >
								</image>
							</view> -->

						</view>

					</view>

				</view>
				
				<study-order-card v-for="(item, index) in taskData" v-on:listenToChildEvent='getData' :key="index"
					:taskIndex="index" :flightDate='flightDate' :taskInfo="item" :flight="1">
				</study-order-card>
				<view class="no-data-box" v-if="noData">
					<image class="image" src="../../../static/images/nodata.png" mode="widthFix"></image>
					<text class="col-gray-500">当前无数据</text>
				</view>
			</view>
		</scroll-view>
		<view>
		    <uni-calendar 
		    ref="calendar"
		    :insert="false"
		    @confirm="confirm"
			@change="confirm"
		     />
		</view>
		<!-- 整体背景放在最后 -->
		<view class="bg-main">
			<image class="background" src="../../../static/images/background.png"></image>
		</view>
	</view>
</template>
<script>
	import {
		studyOrderList,
		studyOrderAirportList,
		studyOrderCreate,
		studyOrderAgentList
	} from '../../../api/weChat.js';
	const App = getApp();
	export default {
		data() {
			return {
				showMore: false,
				currentTime: '',
				homeInfo: {},
				taskData: [],
				text: 'uni-app',
				navH: 0,
				isRefresh: true, // 开启下拉
				triggered: false,
				loadingText: '正在刷新',
				noData: false,
				_freshing: false,
				pageNum: 1,
				flag: true,
				flightDate: '',
				startDate: '',
				arrayAirport: [],
				indexAirport: 0,
				indexAgent: 0,
				arrayAgent: [],
				remarks: "",
				orderSrc: "../../../static/images/logos.png",
				orderDate: '',
				orderStartDate: ''
			};
		},
		onLoad: function(options) {
			//自定义导航
			this._freshing = false;
			this.pageNum = 1
			this.flag = true
			this.getCurrentTime();
			this.getData();
			this.$set(this.$data, 'navH', Number(App.globalData.navHeight));

			this.getArrayAirport();
			this.getArrayAgent();
		},
		onShow: function(options) {

		},

		methods: {
			open(){
				this.$refs.calendar.open();
			},
			confirm(e) {
				this.flightDate = e.fulldate
				this.pageNum = 1
				this.getData();
			},
			bindPickerChangeAirport(e) {
				this.indexAirport = e.detail.value
			},
			
			bindPickerChangeAgent(e){
				this.indexAgent = e.detail.value
			},
			async getArrayAgent() {
				let param = {
					companyCode: uni.getStorageSync('userInfo').companyCode
				};
				try {
					const res = await studyOrderAgentList(param);
					console.log(res)
					if (res.response.code == 200) {
						this.arrayAgent = res.response.data
					}

				} catch (e) {
					console.error(e)
				}
			},

			async getArrayAirport() {
				let param = {
					companyCode: uni.getStorageSync('userInfo').companyCode
				};
				try {
					const res = await studyOrderAirportList(param);
					console.log(res)
					if (res.response.code == 200) {
						this.arrayAirport = res.response.data
					}

				} catch (e) {
					console.error(e)
				}
			},

			async createOrder() {
				let that = this
				let param = {
					carrierCode: uni.getStorageSync('userInfo').companyCode,
					orderDate: this.flightDate,
					createUser: uni.getStorageSync('userInfo').openId,
					agentCode: this.arrayAgent[this.indexAgent].agentCode,
					agentName: this.arrayAgent[this.indexAgent].agentName,
					airportCode: this.arrayAirport[this.indexAirport].airportCode,
					airportName: this.arrayAirport[this.indexAirport].name,
					remarks: this.remarks
				};
				try {
					console.log((param))
					const res = await studyOrderCreate(param);
					console.log(res)
					if (res.response.code == 200) {
						uni.showToast({
							title: "创建成功"
						})
						that.pageNum = 1
						that.getData();
					}

				} catch (e) {
					console.error(e)
				}

			},
			openImg(e) {
				uni.previewImage({
					urls: [this.orderSrc]
				})
			},
			changeShow() {
				this.showMore = !this.showMore;
			},
			// toCreateOrder() {
			// 	uni.navigateTo({
			// 		url: "add"
			// 	})
			// },
			bindTimeChangeOrder(e) {
				this.orderDate = e.detail.value
			},
			bindTimeChange(e) {
				console.log(e)
				this.flightDate = e.detail.value
				this.getData();
			},
			backMyPage() {
				uni.switchTab({
					url: '/pages/home/<USER>'
				})
			},
			onBottomRefresh() {
				if (this.flag) {
					this.pageNum += 1
					this.getData(1);
				}
			},
			getCurrentTime(type) {
				var myDate = new Date();
				var y = myDate.getFullYear()
				var m = myDate.getMonth() + 1
				m = m < 10 ? '0' + m : m
				var d = myDate.getDate()
				d = d < 10 ? ('0' + d) : d
				var week = myDate.getDay()
				var x;
				switch (week) {
					case 0:
						x = '周日';
						break;
					case 1:
						x = '周一';
						break;
					case 2:
						x = '周二';
						break;
					case 3:
						x = '周三';
						break;
					case 4:
						x = '周四';
						break;
					case 5:
						x = '周五';
						break;
					case 6:
						x = '周六';
						break;
				}
				this.currentTime = y + '/' + m + '/' + d + '  ' + x;
				if (type != 1) {
					this.flightDate = y + '-' + m + '-' + d;
				}
				this.orderDate = y + '-' + m + '-' + d;
				this.orderStartDate = y + '-' + m + '-' + d;
			},
			async getData(type) {
				console.log(uni.getStorageSync('userInfo'))
				let homeParam = {
					companyCode: uni.getStorageSync('userInfo').companyCode,
					pageNum: this.pageNum,
					pageSize: 10,
					orderDate: this.flightDate
				};
				try {
					const res = await studyOrderList(homeParam);
					// this.homeInfo.lastUpdateTime = res.response.data.lastUpdateTime
					if (res.response.code == 200) {
						this.homeInfo.total = res.response.total
						console.log(res)
						// this.taskData = res.response.data.flightplanList;
						if (res.response.rows.length < 10) {
							this.flag = false
						}
						if (type == 1) {
							this.taskData = this.taskData.concat(res.response.rows);
						} else {
							this.taskData = []
							this.taskData = res.response.rows
						}
					} else {
						this.taskData = []
						this.noData = true;
					}
					if (this.taskData.length === 0 || res.response.rows == null) {
						this.noData = true;
					} else {
						this.noData = false;
					}
				} catch (e) {
					console.error(e)
				}
				console.log(this.flag)
			},
			openLoading() { //被下拉
				this.triggered = true;
			},
			// 触发下拉刷新
			onRefresh() {
				if (this._freshing) return;
				this._freshing = true;
				if (!this.triggered) {
					this.triggered = true;
				}
				// this.loadStoreData();
				this.pageNum = 1
				this.flag = true
				this.triggered = false;
				this._freshing = false;
				this.getCurrentTime(1);
				this.getData();

			},
			// 下拉刷新复位
			onRestore() {
				this.triggered = false;
				this._freshing = false;
			},
			// 下拉刷新中止
			onAbort() {
				this.triggered = false;
				this._freshing = false;
			},
		}
	};
</script>

<style lang="scss" scoped>
	page {
		height: 100%;
	}

	.colorBlue {
		color: #4858af;
	}

	.background {
		width: 100%;
		height: 100%;
		background-size: 100% 100%;
		z-index: -1;
		position: absolute;
		top: 0px;
		bottom: 0px;
	}

	.main {
		width: 100%;
		height: 100%;
	}


	.nav {
		width: 100%;
		overflow: hidden;
		position: relative;
		top: 0;
		left: 0;
		z-index: 10;
	}

	.nav-title {
		width: 100%;
		height: inherit;
		position: relative;
		z-index: 10;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 15px;

		.nav-title-text {
			// padding-top: 10px;
			font-family: SF Pro Text;
			font-style: normal;
			font-weight: 600;
			font-size: 16px;
			color: #000000;

			&:first-child {
				font-weight: normal;
				position: absolute;
				left: 25px;
				font-size: 22px;
			}
		}
	}


	.refresh {
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		top: 0;
		width: 100%;
		height: 100%;
		background-color: #eeeeee;
		color: #000000;
	}

	.content {
		padding: 0 16px;

		.box {
			font-size: 13px;
			padding: 10px 10px 10px 30px;
			background-color: #ffffff85;
			border-bottom: 1px solid #efefef;

			.leftBox {
				width: 150px;
				display: inline-block;
			}

			.rightBox {
				display: inline-block;
			}
		}
	}

	.refresh-container {
		width: 750rpx;
		text-align: center;
		position: absolute;
		align-items: center;
		margin-bottom: 20px;
	}

	.refresh-container text {}

	.no-data-box {
		background: rgba(255, 255, 255, 0.64);
		height: 343px;
		border-radius: 8px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		.image {
			width: 140px;
			height: 140px;
		}

		.col-gray-500 {
			font-size: 14px;
		}
	}

	.spinning {
		margin-right: 4px;
		display: inline-block;
		-webkit-animation: rotate 1s linear infinite;
		animation: rotate 1s linear infinite;
	}

	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
		}
	}

	.statistics {
		margin-bottom: 12px;
		padding: 0 16px;
		background-color: #fff;
		background: rgba(255, 255, 255, 0.64);
		border: 1px solid rgba(255, 255, 255, 0.72);
		border-radius: 8px;
	}

	.show-view {
		// height: 330px;
		transition: .5s;
		backdrop-filter: blur(16px);
	}

	.close-view {
		height: 0;
		transition: .5s;
		overflow: hidden;
	}

	.detail-block {
		flex: 1;
		// display: flex;
		flex-direction: column;
		justify-content: top;
		align-items: center;
		position: relative;
		margin-right: 13px;

		.phone {
			position: absolute;
			bottom: -17px;
			font-size: 12px;
			color: #2C5DE5;
		}

		.icon-phone-fill {
			font-size: 12px !important;
		}

		.fz-14 {
			font-weight: 600;
		}
	}
</style>