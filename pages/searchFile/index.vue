<template>
	<view class="container alls">
		<!-- 自定义导航栏 -->
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">文件查询</text>
			</view>
		</view>
		<scroll-view class="bg-gray overflow" :style="'height:calc(100vh - ' + navH + 'px)'" scroll-y :refresher-enabled="flaggerFirst"
			 @refresherrefresh="DownRefresh" :refresher-triggered='flagger'>
			<view class="main-search">
				<text class="text iconfont icon-search-line" @click="doSearch"></text>
				<input class="input" confirm-type="search" v-model="searchKey" @confirm="doSearch"
					placeholder-style="font-size:14px;" placeholder="输入文件夹名,支持模糊查询,下拉刷新" />
				<text v-show="showCloseIcon" class="text iconfont icon-close-circle-fill" @click="clearSearchKey"></text>
			</view>
			<view class="" v-if="isReady" >
				<LyTree :tree-data="treeData" :props="props" node-key="fileDirId" :load="loadNode" lazy
					@node-click="handleNodeClick">
					</LyTree>
			</view>
			
			<view class="no-data-box" v-if="!isReady && !searchedData">
				<image class="image" src="../../static/images/nodata.png" mode="widthFix"></image>
				<text class="col-gray-500">当前无数据</text>
			</view>
			<view class="no-data-box" v-if="!isReady && searchedData">
				<image class="image" src="../../static/images/no_search_data.png" mode="widthFix"></image>
				<text class="col-gray-500">没有查询到文件，换个搜索词试试</text>
			</view>
		</scroll-view>
		<!-- 整体背景放在最后 -->
		<view class="bg-main">
			<image class="background" src="../../static/images/background.png"></image>
		</view>
	
	</view>
</template>

<script>
	var that
	import LyTree from '@/components/ly-tree/ly-tree.vue';
	import {
		getTreeList,
		getTreeNode,
		queryTree
	} from '../../api/weChat.js';
	const App = getApp();
	export default {
		components: {
			LyTree
		},
		data() {
			return {
				flagger:false,
				flaggerFirst:false,
				list: [],
				openItemId: '',
				navH: 0,
				searchKey: '',
				isReady: false,
				searchedData: false,
				defaultExpandAll:true,
				autoheight:'',
				treeData: [],
				baseUrl:'', 
				props: function() {
					return {
						label: 'fileDirName',
						children: 'children',
						isLeaf(data, node) {
							return !node.data.haveChildren;
						},
					};
				},
			};
		},
		onLoad: function(options) { 
			that = this
			this.isReady = true;
			this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
			// 获取屏幕的高度
			let self = this;
			uni.getSystemInfo({
				success: function(res) {
					self.autoheight = res.windowHeight + 'px';
					console.log( res.windowHeight)
				}
			});
			
		},
		
		computed: {
			showCloseIcon() {
				return this.searchKey.length > 0;
			}
		},
		methods: {
			backMyPage() {
				uni.switchTab({
					url: '/pages/home/<USER>'
				})
			},
			async DownRefresh(){
				
				if(this.flaggerFirst){
					this.flagger = true
					this.searchKey = ''
					const res = await getTreeList();
					if (res.response.data.length === 0) {
						this.searchedData = true;
						this.isReady = false;
					} else {
						this.isReady = true;
						this.treeData = res.response.data;
					}
					this.flagger = false
				}
			},
			handleClose(){
				this.$refs.popup.close()
			},
			clearSearchKey() {
				this.searchKey = '';
				this.doSearch();
			},
			async doSearch() {
				try {
					const param = {
						fileName: this.searchKey
					};
					if(this.searchKey != ''){
						const res = await queryTree(param);
						if (res.response.data.length === 0) {
							this.searchedData = true;
							this.isReady = false;
						} else {
							this.isReady = true;
							this.treeData = res.response.data;
						}
					}
					
				} catch (e) {
					console.error(e);
				}
			},
			loadNode(node, resolve) {
				that.flaggerFirst = false
					if (node.level === 0) {
						setTimeout(async () => {
							try {
								const res = await getTreeList();
								that.flaggerFirst = true
								if (res.response.data.length == 0) {
									that.isReady = false;
									return;
								}
								resolve(res.response.data);
							} catch (e) {
								console.error(e);
							}
						}, 1000);
					} else {
						setTimeout(async () => {
							try {
								const param = {
									parentId: node.data.fileDirId
								};
								that.flaggerFirst = true
								const res = await getTreeNode(param);
								resolve(res.response.data);
							} catch (e) {
								console.error(e);
							}
						}, 500);
					}
				
			},
			handleNodeClick(node) {
				that.flaggerFirst = true
				if (node.data.type == 2) {
					console.log(123);
					// this.$refs.popup.open('top')
					this.baseUrl = node.data.saveUrl
					uni.previewImage({
						urls:[node.data.saveUrl]
					})
					// this.baseUrl = 'https://tse1-mm.cn.bing.net/th/id/R-C.********************************?rik=MsMCKPGumufOyQ&riu=http%3a%2f%2fwww.desktx.com%2fd%2ffile%2fwallpaper%2fscenery%2f20170209%2fc2accfe637f86fb6f11949cb8651a09b.jpg&ehk=ia2TVXcow6ygWUVZ1yod5xH4aGd8565SYn6CRpxkNoo%3d&risl=&pid=ImgRaw&r=0'
					console.log(node)
					// wx.downloadFile({
					// 	// url: node.data.fileUrl,      //要预览的PDF的地址
					// 	url: 'http://cdhrss.chengdu.gov.cn/cdrsj/uploads/20180523103526wwevlckni3l.pdf',
					// 	success: function(res) {
					// 		if (res.statusCode === 200) {
					// 			//成功
					// 			var Path = res.tempFilePath; //返回的文件临时地址，用于后面打开本地预览所用
					// 			wx.openDocument({
					// 				filePath: Path, //要打开的文件路径
					// 				success: function(res) {
					// 					console.log('打开PDF成功');
					// 				}
					// 			});
					// 		}
					// 	},
					// 	fail: function(res) {
					// 		console.log(res); //失败
					// 	}
					// });
				}
			}
		}
	};
</script>

<style lang="less" >
	page{
		height: 100%;
	}
	.container {
		// padding: 0 16px;
	}

	
	.alls{
		.no-data-box {
			background: rgba(255, 255, 255, 0.64);
			height: 343px;
			border-radius: 8px;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: calc(100% - 32px);
			margin: 16px;
		
			.image {
				width: 140px;
				height: 140px;
			}
		
			.col-gray-500 {
				font-size: 14px;
			}
		}
		
		page {
			height: 100%;
		}
		
		.background {
			width: 100%;
			height: 100%;
			background-size: 100% 100%;
			z-index: -1;
			position: absolute;
			top: 0px;
			bottom: 0px;
		}
		
		.nav {
			width: 100%;
			overflow: hidden;
			position: relative;
			top: 0;
			left: 0;
			z-index: 10;
		}
		
		.nav-title {
			width: 100%;
			/* height: 45px;
			line-height: 45px; */
			text-align: center;
			position: absolute;
			bottom: 16px;
			left: 0;
			z-index: 10;
			font-family: SF Pro Text;
			font-style: normal;
			font-weight: 600;
			font-size: 16px;
			color: #000000;
			.nav-title-text {
				// padding-top: 10px;
				font-family: SF Pro Text;
				font-style: normal;
				font-weight: 600;
				font-size: 16px;
				color: #000000;
			
				&:first-child {
					font-weight: normal;
					position: absolute;
					left: 25px;
					font-size: 22px;
				}
			}
		}
		
		.main-search {
			display: flex;
			flex-direction: row;
			align-items: center;
			margin: 0 16px;
			height: 36px;
			background: rgba(255, 255, 255, 0.88);
			border-radius: 8px;
			box-sizing: border-box;
			padding: 0 5px 0 13px;
		
			.icon-close-circle-fill {
				padding: 8px;
			}
		
			.text {
				color: #bec0c5;
			}
		
			.input {
				margin-left: 9px;
				width: 100%;
			}
		}
		.imgBox{
			// height: 500px;
			width: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			// padding-top: 80px;
			box-sizing: border-box;
			overflow: auto;
			img{
				width: 100%;
			}
		}
		.ly-tree-node__content .ly-tree-node__label{
			white-space: normal;
			word-break: break-all;
			width: 80%;
		}
		.ly-tree-node__content{height: auto;}
	}
</style>
