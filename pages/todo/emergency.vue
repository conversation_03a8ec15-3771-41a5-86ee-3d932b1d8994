<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <view class="nav bg-white" :style="'height:' + navH + 'px'">
      <view class="nav-title">
        <text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
        <text class="nav-title-text">副驾驶上传任务书</text>
      </view>
    </view>
    <!-- 内容主体 -->
    <view class="content" :style="'height:calc(100vh - ' + navH + 'px)'" style="overflow: auto;">
      <form @submit="submit">

        <!-- 副驾驶上传任务书阶段 -->
        <view class="">
          <view class="uni-form-item uni-column">
            <view class="title">任务书:</view>
            <button type="default" size="mini" @click='uploadFiles(1)' style="margin-right: 5px;">上传图片</button>
            <button type="default" size="mini" @click='clearFiles(1)'>清空图片</button>
            <view class="" style="display: flex;">
              <image style="height: 80px;max-width: 52px;" v-for="(item,index) in path" :key='index'
                     @click='previews(path)'
                     v-show="path.length>0" :src="item" mode=""></image>
            </view>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">舱单:</view>
            <button type="default" size="mini" @click='uploadFiles(2)' style="margin-right: 5px;">上传图片</button>
            <button type="default" size="mini" @click='clearFiles(2)'>清空图片</button>
            <view class="" style="display: flex;">
              <image style="height: 80px;max-width: 52px;" v-for="(item,index) in path2" :key='index'
                     @click='previews(path2)'
                     v-show="path2.length>0" :src="item" mode=""></image>
            </view>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">放行单:</view>
            <button type="default" size="mini" @click='uploadFiles(3)' style="margin-right: 5px;">上传图片</button>
            <button type="default" size="mini" @click='clearFiles(3)'>清空图片</button>
            <view class="" style="display: flex;">
              <image style="height: 80px;max-width: 52px;" v-for="(item,index) in path3" :key='index'
                     @click='previews(path3)'
                     v-show="path3.length>0" :src="item" mode=""></image>
            </view>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">航行通告:</view>
            <button type="default" size="mini" @click='uploadFiles(4)' style="margin-right: 5px;">上传图片</button>
            <button type="default" size="mini" @click='clearFiles(4)'>清空图片</button>
            <view class="" style="display: flex;">
              <image style="height: 80px;max-width: 52px;" v-for="(item,index) in path4" :key='index'
                     @click='previews(path4)'
                     v-show="path4.length>0" :src="item" mode=""></image>
            </view>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">气象资料:</view>
            <button type="default" size="mini" @click='uploadFiles(5)' style="margin-right: 5px;">上传图片</button>
            <button type="default" size="mini" @click='clearFiles(5)'>清空图片</button>
            <view class="" style="display: flex;">
              <image style="height: 80px;max-width: 52px;" v-for="(item,index) in path5" :key='index'
                     @click='previews(path5)'
                     v-show="path5.length>0" :src="item" mode=""></image>
            </view>
          </view>
        </view>

        <view class="uni-btn-v">
          <button type="primary" plain="true" form-type="submit" style="margin-bottom: 10px;">提交</button>
          <!-- <button form-type="submit">Submit</button> -->
          <button type="default" form-type="reset" @click='clearPath'>清空</button>
        </view>
      </form>
    </view>
    <!-- 整体背景放在最后 -->
    <view class="bg-main">
      <image class="background" src="../../static/images/background.png"></image>
    </view>
  </view>
</template>

<script>
import api from "../../api/index.js"
import {getFlightDetailById, getVerifySure, getHomeItemData, flightSortiesSynBook} from '../../api/weChat.js';

const FormData = require('../../utils/formData/formData.js')

const App = getApp();
export default {
  data() {
    return {

      navH: 0,
      timer: '',
      taskIndex: 1,
      inputValue: '',
      taskInfo: '',
      componentData: '',
      flightSortiesId: '',
      flightPlanId: '',
      imgUrl: '',
      state: '',
      userRole: '',
      form: {
        taxiStartTime1: '',
        departureTime1: '',
        landingTime1: '',
        shutdownTime1: '',
        taxiStartTimeDate: '',
        departureTimeDate: '',
        landingTimeDate: '',
        shutdownTimeDate: '',
      },
      currentTime: '',
      path: [],
      path2: [],
      path3: [],
      path4: [],
      path5: []
    };
  },
  mounted() {
    console.log(this.currentTime)
    this.getCurrentTime()
    this.form.taxiStartTimeDate = this.currentTime
    this.form.departureTimeDate = this.currentTime
    this.form.landingTimeDate = this.currentTime
    this.form.shutdownTimeDate = this.currentTime
  },
  onLoad: function (options) {
    this.state = options.state;
    this.flightSortiesId = options.flightSortiesId;
    this.flightPlanId = options.flightplanId

    console.log(options)
    this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
    // this.userRole = uni.getStorageSync('userRole')
    this.userRole = 1


  },
  methods: {
    clearPath() {
      let that = this
      that.path = []
      that.path2 = []
      that.path3 = []
      that.path4 = []
      that.path5 = []
    },
    clearFiles(type) {
      if (type == 1) {
        this.path = []
      } else if (type == 2) {
        this.path2 = []
      } else if (type == 3) {
        this.path3 = []
      } else if (type == 4) {
        this.path4 = []
      } else if (type == 5) {
        this.path5 = []
      }
    },
    getCurrentTime() {
      var myDate = new Date();
      var y = myDate.getFullYear()
      var m = myDate.getMonth() + 1
      m = m < 10 ? '0' + m : m
      var d = myDate.getDate()
      d = d < 10 ? ('0' + d) : d
      var week = myDate.getDay()
      var x;
      switch (week) {
        case 0:
          x = '周日';
          break;
        case 1:
          x = '周一';
          break;
        case 2:
          x = '周二';
          break;
        case 3:
          x = '周三';
          break;
        case 4:
          x = '周四';
          break;
        case 5:
          x = '周五';
          break;
        case 6:
          x = '周六';
          break;
      }
      this.currentTime = y + '-' + m + '-' + d
    },
    uploadFiles(type) {
      let that = this
      console.log(123)
      uni.chooseMedia({
        sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
        mediaType: ['image'],
        sourceType: ['album', 'camera'], //从相册选择
        name: '',
        success: function (res) {
          res.tempFiles.map((i) => {
            if (type == 1) {
              if (that.path.length > 4) {
                uni.showToast({
                  title: '最多上传5张图片！'
                })
                return false
              }
              that.path.push(i.tempFilePath)
            } else if (type == 2) {
              if (that.path2.length > 4) {
                uni.showToast({
                  title: '最多上传5张图片！'
                })
                return false
              }
              that.path2.push(i.tempFilePath)
            } else if (type == 3) {
              if (that.path3.length > 4) {
                uni.showToast({
                  title: '最多上传5张图片！'
                })
                return false
              }
              that.path3.push(i.tempFilePath)
            } else if (type == 4) {
              if (that.path4.length > 4) {
                uni.showToast({
                  title: '最多上传5张图片！'
                })
                return false
              }
              that.path4.push(i.tempFilePath)
            } else if (type == 5) {
              if (that.path5.length > 4) {
                uni.showToast({
                  title: '最多上传5张图片！'
                })
                return false
              }
              that.path5.push(i.tempFilePath)
            }

          })

        }
      });
    },
    bindTimeChange(e, id) {
      console.log(e.target.value, id)
      if (id == 1) {
        this.form.taxiStartTime1 = e.target.value;
        console.log(this.form.taxiStartTime)
      }
      if (id == 2) {
        this.form.departureTime1 = e.target.value;
      }
      if (id == 3) {
        this.form.landingTime1 = e.target.value;
      }
      if (id == 4) {
        this.form.shutdownTime1 = e.target.value;
      }
      if (id == 5) {
        this.form.taxiStartTimeDate = e.target.value;
      }
      if (id == 6) {
        this.form.departureTimeDate = e.target.value;
      }
      if (id == 7) {
        this.form.landingTimeDate = e.target.value;
      }
      if (id == 8) {
        this.form.shutdownTimeDate = e.target.value;
      }
    },
    // 返回"我的"主页面
    backMyPage() {
      uni.navigateBack({
        url: '/pages/home/<USER>'
      });
    },

    async getData() {
      let param = {
        flightSortiesId: this.flightSortiesId,
      };
      try {
        const res = await getFlightDetailById(param);
        this.taskInfo = res.response.data;
      } catch (e) {
        console.error(e)
      }
    },
    previews(item) {
      uni.previewImage({
        urls: item
      })
    },
    //提交答题
    submit(type) {
      uni.showLoading({
        title: '加载中'
      });
      let that = this
      let formDatasss = new FormData()
      formDatasss.append("flightPlanId", this.flightPlanId)
      for (let i = 0; i < that.path.length; i++) {
        formDatasss.appendFile('assignments', that.path[i])
      }
      for (let i = 0; i < that.path2.length; i++) {
        formDatasss.appendFile('missionStatements', that.path2[i])
      }
      for (let i = 0; i < that.path3.length; i++) {
        formDatasss.appendFile('releasePermits', that.path3[i])
      }
      for (let i = 0; i < that.path4.length; i++) {
        formDatasss.appendFile('sailingNotices', that.path4[i])
      }
      for (let i = 0; i < that.path5.length; i++) {
        formDatasss.appendFile('weathers', that.path5[i])
      }
      // const baseUrl = 'https://ga.swcares.com.cn/trade/oc/wechat'
      formDatasss = formDatasss.getData();
      console.log(formDatasss)
      uni.request({
        url: api.baseUrl + '/wechat/dataArchive/uploadAssignment',
        data: formDatasss.buffer,
        method: 'POST',
        header: {
          'AuthCode': uni.getStorageSync('token'),
          'AuthID': uni.getStorageSync('userInfo').userId,
          'Content-Type': formDatasss.contentType,
        },
        success: (res) => {
          uni.hideLoading()
          console.log(res)
          if (res.data.code != 200) {
            uni.showToast({
              title: res.data.msg,
              icon: 'error'
            })
            return false
          }
          uni.showToast({
            title: res.data.msg,
          })
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/home/<USER>'
            });
          }, 1500)
        }
      });
      // let formData = { assignments:this.path,flightPlanId :91,missionStatements:[],
      // releasePermits :[],sailingNotices:[],weathers:[]}
      // formData = JSON.stringify(formData)
      // let formData2 = {flightPlanId :91,assignments:this.path[0]}
      // console.log(formData)
      // // const baseUrl = 'https://ga.swcares.com.cn/trade/oc/wechat'
      // uni.uploadFile({
      // 	url:baseUrl+'/wechat/dataArchive/uploadAssignment',
      // 	filePath:'',
      // 	formData:formData2,
      // 	name:'file',
      // 	header:{
      // 		AuthCode:uni.getStorageSync('token'),
      // 		AuthID:uni.getStorageSync('userInfo').userId
      // 	},
      // 	async success(res){
      // 		res = JSON.parse(res.data)
      // 		console.log(res)
      // 		if(that.path.length == 1){
      // 			uni.showToast({
      // 				title: res.msg
      // 			})
      // 			setTimeout(()=>{
      // 				uni.navigateBack({
      // 				    delta: 1,
      // 				});
      // 			},1500)
      // 		}else{
      // 			var idFile = res.data.id
      // 			that.path.map((i,o)=>{
      // 				if(o!=0){
      // 					uni.uploadFile({
      // 						url:baseUrl+'/wechat/flightDetail/importAssignmentFile1change',
      // 						filePath:i,
      // 						formData:{flightSortiesId:that.flightSortiesId,file:i},
      // 						name:'file',
      // 						header:{
      // 							AuthCode:uni.getStorageSync('token'),
      // 							AuthID:uni.getStorageSync('userInfo').userId
      // 						},
      // 						success(respond){
      // 							respond = JSON.parse(respond.data)
      // 							if(o == that.path.length-1){
      // 								uni.showToast({
      // 									title: respond.msg
      // 								})
      // 								setTimeout(()=>{
      // 									uni.navigateBack({
      // 									    delta: 1,
      // 									});
      // 								},1500)
      // 							}
      // 						},error(error){
      // 							uni.showToast({
      // 								title: '上传失败'
      // 							})
      // 						}
      // 					})
      // 				}
      // 			})
      // 		}

      // 	},error(error){
      // 		uni.showToast({
      // 			title: '上传失败'
      // 		})
      // 	}
      // })


      // uni.uploadFile({
      // 	url:'https://ga.swcares.com.cn/trade/oc/wechat/wechat/flightDetail/importAssignmentFile',
      // 	filePath:that.path,
      // 	formData:formData,
      // 	name:'file',
      // 	header:{
      // 		AuthCode:uni.getStorageSync('token'),
      // 		AuthID:uni.getStorageSync('userInfo').userId
      // 	},
      // 	async success(responce){
      // 		// param.id= JSON.parse(responce.data).data.id
      // 		const res =await flightSortiesSynBook(param)
      // 		uni.showToast({
      // 			title: res.response.msg
      // 		})
      // 		setTimeout(()=>{
      // 			uni.navigateBack({
      // 			    delta: 1,
      // 			});
      // 		},1500)
      // 	},error(error){
      // 		console.log(error)
      // 	}
      // })

    }
  }
};
</script>

<style lang="less" scoped>

page {
  height: 100%;
}

.background {
  width: 100%;
  height: 100%;
  // position: fixed;
  background-size: 100% 100%;
  z-index: -1;
  position: absolute;
  top: 0px;
  bottom: 0px;
}

.nav {
  width: 100%;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
  z-index: 10;
}

.nav-title {
  width: 100%;
  height: inherit;
  position: relative;
  z-index: 10;
  font-family: SF Pro Text;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;

  .nav-title-text {
    // padding-top: 10px;
    font-family: SF Pro Text;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #000000;

    &:first-child {
      font-weight: normal;
      position: absolute;
      left: 25px;
      font-size: 22px;
    }
  }
}

.uni-column {
  background: #ffffff86;
  line-height: 30px;
  padding: 10px 20px;
  font-size: 13px;

}

.content {
  padding: 10px;
}

textarea {
  border: 1px solid #efefef;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
}
</style>
