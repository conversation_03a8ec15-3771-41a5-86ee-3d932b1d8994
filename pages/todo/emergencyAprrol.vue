<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">特殊情况</text>
			</view>
		</view>
		<!-- 内容主体 -->
		<view class="content" :style="'height:calc(100vh - ' + navH + 'px)'" style="overflow: auto;">
			<form @submit="submit" >
				<!-- 预先准备 -->
				<!-- 机长直接准备 -->
				<view class="" >
					<view class="uni-form-item uni-column">
						<view class="title">特殊情况:</view>
						<view class="uni-textarea">
							<textarea :disabled='true' maxlength="800" v-model='exceptionalCase'  placeholder="请填写特殊情况" placeholder-style='font-size:12px'/>
						</view>
					</view>
				</view>
				
				<view class="uni-btn-v">
					<button type="primary" plain="true" @click="submit(1)" style="margin-bottom: 10px;">已知悉</button>
					<!-- <button form-type="submit">Submit</button> -->
				</view>
			</form>
		</view>
		<!-- 整体背景放在最后 -->
		<view class="bg-main"><image class="background" src="../../static/images/background.png"></image></view>
	</view>
</template>

<script>
	import { captainDirectPreparation,emergency,emergencyList} from '../../api/weChat.js';

const App = getApp();
export default {
	data() {
		return {
			navH: 0,
			taskInfo: '',
			flightSortiesId:'',
			exceptionalCase:'',
			type:''
		};
	},
	mounted() {
		this.getData()
	},
	onLoad: function(options) {
		this.flightSortiesId = options.flightSortiesId;
		this.type = options.type;
		this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
		// this.userRole = uni.getStorageSync('userRole')
	},
	methods: {
		
		// 返回"我的"主页面
		backMyPage() {
			uni.navigateBack({
				 url: '/pages/home/<USER>'
			});
		},
		async getData() {
			let param = {
				flightSortiesId: this.flightSortiesId,
			};
			try {
				const res = await emergencyList(param);
				console.log(res.response.data)
				this.exceptionalCase = res.response.data.exceptionalCase;
			} catch (e) {
				console.error(e)
			}
		},
		
		//提交答题
		async submit(type) {
			
			let param = {
				flightSortiesId:this.flightSortiesId,
			};
			var types = this.type
			param[types] = 1
			try {
				const res = await emergency(param);
				uni.showToast({
					title: res.response.msg
				})
				setTimeout(()=>{
					uni.navigateBack({
					    delta: 1,
					});
				},1500)
			} catch (e) {
				console.error(e)
			}
		}
	}
};
</script>

<style lang="less" scoped>

page {
	height: 100%;
}

.background {
	width: 100%;
	height: 100%;
	// position: fixed;
	background-size: 100% 100%;
	z-index: -1;
	position: absolute;
	top: 0px;
	bottom: 0px;
}

.nav {
	width: 100%;
	overflow: hidden;
	position: relative;
	top: 0;
	left: 0;
	z-index: 10;
}

.nav-title {
	width: 100%;
	height: inherit;
	position: relative;
	z-index: 10;
	font-family: SF Pro Text;
	font-style: normal;
	font-weight: 600;
	font-size: 16px;
	color: #000000;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 15px;

	.nav-title-text {
		// padding-top: 10px;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;

		&:first-child {
			font-weight: normal;
			position: absolute;
			left: 25px;
			font-size: 22px;
		}
	}
}

.uni-column{
	    background: #ffffff86;
		line-height: 30px;
		padding: 10px 20px;
		font-size: 13px;

}
.content{
	padding: 10px;
}
textarea{
	    border: 1px solid #efefef;
	    width: 100%;
	    padding: 10px;
	    box-sizing: border-box;
}
</style>
