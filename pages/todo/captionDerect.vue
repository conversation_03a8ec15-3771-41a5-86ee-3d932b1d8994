<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <view class="nav bg-white" :style="'height:' + navH + 'px'">
      <view class="nav-title">
        <text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
        <text class="nav-title-text">机长直接准备</text>
      </view>
    </view>
    <!-- 内容主体 -->
    <view class="content" :style="'height:calc(100vh - ' + navH + 'px)'" style="overflow: auto;">
      <form @submit="submit">
        <task-card v-for="(item, index) in taskData" :key="index" :taskIndex="index"
                   :taskInfo="item" :flight="1" :show-option="false">
        </task-card>
        <view class="uni-form-item uni-column" @click="flyFit('signIn')">
          <view class="title" style="overflow: hidden;">
            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.signIn" :value="form.signIn"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">签到</view>
          </view>
        </view>
        <view class="uni-form-item uni-column" @click="flyFit('physicalCondition')">
          <view class="title" style="overflow: hidden;">
            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;margin-top: 5px;" :checked="form.physicalCondition"
                            :value="form.physicalCondition"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">身体健康状况满足运行要求</view>

          </view>
        </view>
        <view class="uni-form-item uni-column" @click="flyFit('carryCertificate')">
          <view class="title" style="overflow: hidden;">
            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.carryCertificate" :value="form.carryCertificate"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">已携带飞行任务书、空勤登机证</view>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title" style="overflow: hidden;" @click="flyFit('dressAppropriately')">

            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.dressAppropriately" :value="form.dressAppropriately"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">着装符合要求，携带反光背心</view>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title" style="overflow: hidden;" @click="flyFit('certificateEffective')">

            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.certificateEffective"
                            :value="form.certificateEffective"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">
              执照、近期经历、体检合格证、熟练检查、危险品培训在有效期内
            </view>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title" style="overflow: hidden;" @click="flyFit('availableFlashlight')">

            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.availableFlashlight" :value="form.availableFlashlight"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">手电筒可用</view>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title" style="overflow: hidden;" @click="flyFit('eyeglass')">

            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.eyeglass" :value="form.eyeglass"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">矫正视力眼镜两副(如适用)</view>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title" style="overflow: hidden;" @click="flyFit('obtainCompleteSailingData')">

            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.obtainCompleteSailingData"
                            :value="form.obtainCompleteSailingData"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">已获取有效、完整航行资料</view>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title" style="overflow: hidden;" @click="flyFit('completeOnboardData')">

            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.completeOnboardData" :value="form.completeOnboardData"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">机载资料齐全</view>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title" style="overflow: hidden;" @click="flyFit('completedMeteorologicalAnalysis')">

            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.completedMeteorologicalAnalysis"
                            :value="form.completedMeteorologicalAnalysis"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">已完成机场、航路气象分析</view>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title" style="overflow: hidden;" @click="flyFit('completedFlightCharacteristicsAndPrecautions')">

            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.completedFlightCharacteristicsAndPrecautions"
                            :value="form.completedFlightCharacteristicsAndPrecautions"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">已完成机场、航路飞行特点、注意事项
            </view>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title" style="overflow: hidden;" @click="flyFit('confirmedAircraftCondition')">

            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.confirmedAircraftCondition"
                            :value="form.confirmedAircraftCondition"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">已确认飞机状况(MEL)</view>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title" style="overflow: hidden;" @click="flyFit('confirmedTakeoffFuel')">

            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.confirmedTakeoffFuel"
                            :value="form.confirmedTakeoffFuel"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">已确认起飞油量</view>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title" style="overflow: hidden;" @click="flyFit('confirmedAircraftTechnicalStandard')">

            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.confirmedAircraftTechnicalStandard"
                            :value="form.confirmedAircraftTechnicalStandard"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">
              已确认本机型技术标准、飞行经历时间简述
            </view>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title" style="overflow: hidden;" @click="flyFit('confirmedCrewDivideWork')">

            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.confirmedCrewDivideWork"
                            :value="form.confirmedCrewDivideWork"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">已简述确认机组分工</view>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title" style="overflow: hidden;" @click="flyFit('confirmedAircraftNoDeliberateDamage')">

            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.confirmedAircraftNoDeliberateDamage"
                            :value="form.confirmedAircraftNoDeliberateDamage"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">
              已确认航空器无蓄意损坏、驾驶舱/客舱/吊舱无不明物品(B300机型需确认工具箱和起落架舱)
            </view>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title" style="overflow: hidden;" @click="flyFit('confirmJoinCrewAndVipInformation')">

            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.confirmJoinCrewAndVipInformation"
                            :value="form.confirmJoinCrewAndVipInformation"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">确认加入机组人员、VIP信息</view>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title" style="overflow: hidden;" @click="flyFit('confirmedExplanationRouteWeather')">

            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.confirmedExplanationRouteWeather"
                            :value="form.confirmedExplanationRouteWeather"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">
              确认已讲解航路天气、颠簸区域、持续时间/强度
            </view>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title" style="overflow: hidden;" @click="flyFit('confirmedEmergencies')">

            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.confirmedEmergencies"
                            :value="form.confirmedEmergencies"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">
              已确认紧急情况机组成员的工作职责和处置程序
            </view>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title" style="overflow: hidden;" @click="flyFit('confirmedAirDefenseMeasures')">

            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.confirmedAirDefenseMeasures"
                            :value="form.confirmedAirDefenseMeasures"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">已确认空防措施及应急预案</view>
          </view>
        </view>
        <view class="uni-btn-v">
          <button type="primary" plain="true" form-type="submit" style="margin-bottom: 10px;">提交</button>
          <!-- <button form-type="submit">Submit</button> -->
          <button type="default" @click="resetForm">清空</button>
        </view>
      </form>
    </view>
    <!-- 整体背景放在最后 -->
    <view class="bg-main">
      <image class="background" src="../../static/images/background.png"></image>
    </view>
  </view>
</template>

<script>
import {log} from 'util';
import {
  getFlightPlanSingle,
  prepareForFlightplanUpdates,
  getFlightDetailById,
  getVerifySure,
  getHomeItemData
} from '../../api/weChat.js';

const App = getApp();
export default {
  data() {
    return {
      navH: 0,
      taskInfo: '',
      flightplanId: '',
      flightSortiesId: '',
      form: {
        signIn: 0,
        availableFlashlight: 0,
        carryCertificate: 0,
        certificateEffective: 0,
        completeOnboardData: 0,
        completedFlightCharacteristicsAndPrecautions: 0,
        completedMeteorologicalAnalysis: 0,
        confirmJoinCrewAndVipInformation: 0,
        confirmedAirDefenseMeasures: 0,
        confirmedAircraftCondition: 0,
        confirmedAircraftNoDeliberateDamage: 0,
        confirmedAircraftTechnicalStandard: 0,
        confirmedCrewDivideWork: 0,
        confirmedExplanationRouteWeather: 0,
        confirmedTakeoffFuel: 0,
        dressAppropriately: 0,
        eyeglass: 0,
        confirmedEmergencies: 0,
        obtainCompleteSailingData: 0,
        physicalCondition: 0,
      },
      taskData: [],
      roleType: 1,
      inds: ''
    };
  },
  mounted() {

  },
  created() {
    this.userRole = uni.getStorageSync('userRole');
  },
  onLoad: function (options) {
    this.flightplanId = options.flightplanId;
    this.flightSortiesId = options.flightSortiesId;

    this.roleType = options.roleType;
    // this.roleType = 2;
    this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
    this.getData()
  },
  methods: {
    resetForm() {
      this.form = {}
      this.form.signIn = 0
      this.form.availableFlashlight = 0
      this.form.carryCertificate = 0
      this.form.certificateEffective = 0
      this.form.completeOnboardData = 0
      this.form.completedFlightCharacteristicsAndPrecautions = 0
      this.form.completedMeteorologicalAnalysis = 0
      this.form.confirmJoinCrewAndVipInformation = 0
      this.form.confirmedAirDefenseMeasures = 0
      this.form.confirmedAircraftCondition = 0
      this.form.confirmedAircraftNoDeliberateDamage = 0
      this.form.confirmedAircraftTechnicalStandard = 0
      this.form.confirmedCrewDivideWork = 0
      this.form.confirmedExplanationRouteWeather = 0
      this.form.confirmedTakeoffFuel = 0
      this.form.dressAppropriately = 0
      this.form.eyeglass = 0
      this.form.confirmedEmergencies = 0
      this.form.obtainCompleteSailingData = 0
      this.form.physicalCondition = 0
    },
    flyFit(param) {
      this.form[param] = this.form[param] == 0 ? 1 : 0
      this.$forceUpdate()
    },
    // 返回"我的"主页面
    backMyPage() {
      uni.navigateBack({
        url: '/pages/home/<USER>'
      });
    },
    async getData(type) {
      let homeParam = {
        flightPlanId: this.flightplanId
      };
      try {
        const res = await getFlightPlanSingle(homeParam);
        // this.taskData = res.response.data.flightplanList;
        this.taskData = [res.response.data]

      } catch (e) {
        console.error(e)
      }
      console.log(this.flag)
    },

    async submit(type) {
      console.log(111)
      let param = this.form;
      param.flightPlanId = this.flightplanId
      // param.roleType = this.roleType
      try {
        const res = await prepareForFlightplanUpdates(param);
        uni.showToast({
          title: res.response.msg
        })
        setTimeout(() => {
          uni.navigateTo({
            url: '/pages/home/<USER>'
          });
        }, 1500)
      } catch (e) {
        console.error(e)
      }
    }
  }
};
</script>

<style lang="less" scoped>

page {
  height: 100%;
}

.background {
  width: 100%;
  height: 100%;
  // position: fixed;
  background-size: 100% 100%;
  z-index: -1;
  position: absolute;
  top: 0px;
  bottom: 0px;
}

.nav {
  width: 100%;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
  z-index: 10;
}

.nav-title {
  width: 100%;
  height: inherit;
  position: relative;
  z-index: 10;
  font-family: SF Pro Text;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;

  .nav-title-text {
    // padding-top: 10px;
    font-family: SF Pro Text;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #000000;

    &:first-child {
      font-weight: normal;
      position: absolute;
      left: 25px;
      font-size: 22px;
    }
  }
}

.uni-column {
  background: #ffffff86;
  line-height: 30px;
  padding: 10px 20px;
  font-size: 13px;

}

.content {
  padding: 10px;
}

textarea {
  border: 1px solid #efefef;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
}
</style>
