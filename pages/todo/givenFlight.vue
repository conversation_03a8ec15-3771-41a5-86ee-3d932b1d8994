<template>
  <view class="container">
    <view class="nav bg-white" :style="'height:' + navH + 'px'">
      <view class="nav-title">
        <text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
        <text class="nav-title-text">确认飞行时间</text>
      </view>
    </view>
    <scroll-view class="bg-gray overflow" :style="'height:calc(100vh - ' + navH + 'px)'" scroll-y
                 @refresherrefresh="onRefresh" :refresher-enabled="isRefresh" :refresher-triggered="triggered"
                 @refresherrestore="onRestore" @refresherpulling="openLoading" refresher-default-style="none"
                 @refresherabort="onAbort">
      <view class="refresh-container" slot="refresher">
        <view class="spinning">
          <text class="iconfont icon-loader-2-fill"></text>
        </view>
        <text>{{ loadingText }}</text>
      </view>
      <view class="content">
        <!-- <view class="statistics flex-row justify-between">
          <view style="padding: 0 16px 5px;">
            <p>
              <span class="col-gray-500 fz-12 margin-right-8">当前时间</span>
              <span class="col-gray-600 fz-12">{{ currentTime }}</span>
            </p>
            <p>
              <span class="col-gray-500 fz-12 margin-right-8">最后更新</span>
              <span class="col-gray-600 fz-12">{{ homeInfo.lastUpdateTime }}</span>
            </p>
          </view>

        </view> -->
        <task-card v-for="(item, index) in taskData" :key="index" :taskIndex="index"
                   :taskInfo="item" :types='1' :flight='1' :show-option="false">
        </task-card>
        <view class="no-data-box" v-if="noData">
          <image class="image" src="../../static/images/nodata.png" mode="widthFix"></image>
          <text class="col-gray-500">当前无数据</text>
        </view>
        <view class="" style="border-radius: 8px;overflow: hidden;">
          <view class="box">
            <view class="leftBox">飞行架次</view>
            <view class="rightBox">{{ form.flightFrequency }}</view>
          </view>
          <view v-show='routeType' class=""
                style="text-align: center;font-size: 15px;font-weight: 700;padding: 13px 0;background: #ffffff85;">
            去程信息
          </view>
          <view class="box">
            <view class="leftBox">滑出时刻</view>
            <view class="rightBox">{{ form.slideStartTime }}</view>
          </view>
          <view class="box">
            <view class="leftBox">起飞时刻</view>
            <view class="rightBox">{{ form.flyStartTime }}</view>
          </view>
          <view class="box">
            <view class="leftBox">着陆时刻</view>
            <view class="rightBox">{{ form.flyEndTime }}</view>
          </view>
          <view class="box">
            <view class="leftBox">滑入时刻</view>
            <view class="rightBox">{{ form.slideEndTime }}</view>
          </view>
          <view class="box">
            <view class="leftBox">夜航时间</view>
            <view class="rightBox">{{ form.nightFlyTime }}</view>
          </view>
          <view class="box">
            <view class="leftBox">剩余燃油</view>
            <view class="rightBox">{{ form.fuelExpend == null ? '' : form.fuelExpend }}L</view>
          </view>
          <view class="box">
            <view class="leftBox">旅客人数</view>
            <view class="rightBox">{{ form.passengerNumber }}</view>
          </view>
          <view class="title" v-show="userRole.includes('operationControl')" @click="flyFit(1)"
                style="background-color: #ffffff85;padding: 10px 10px 10px 30px;overflow: hidden;">
            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="flightDelay" :value="flightDelay"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">航班延误</view>
          </view>
          <view v-show="flightDelay == 1&&userRole.includes('operationControl')" class="uni-form-item uni-column"
                style="background-color: #ffffff85;overflow: hidden;padding: 10px 10px 10px 30px;">
            <view class="leftBox" style="float: left;">选择延误原因：</view>
            <view class="rightBox" style="float: left;">
              <view class="uni-list-cell">
                <picker mode="selector" :value="index" @change="bindPickerChange" :range="arraysas">
                  {{ arraysas[index] }}
                </picker>
              </view>
            </view>

          </view>
          <!-- 返程 -->
          <view v-show='routeType' class=""
                style="text-align: center;font-size: 15px;font-weight: 700;padding: 13px 0;background: #ffffff85;">
            返程信息
          </view>
          <view class="" v-show='routeType'>
            <view class="box">
              <view class="leftBox">滑出时刻</view>
              <view class="rightBox">{{ form.secondSlideStartTime }}</view>
            </view>
            <view class="box">
              <view class="leftBox">起飞时刻</view>
              <view class="rightBox">{{ form.secondFlyStartTime }}</view>
            </view>
            <view class="box">
              <view class="leftBox">着陆时刻</view>
              <view class="rightBox">{{ form.secondFlyEndTime }}</view>
            </view>
            <view class="box">
              <view class="leftBox">滑入时刻</view>
              <view class="rightBox">{{ form.secondSlideEndTime }}</view>
            </view>
            <view class="box">
              <view class="leftBox">夜航时间</view>
              <view class="rightBox">{{ form.secondNightFlyTime }}</view>
            </view>
            <view class="box">
              <view class="leftBox">剩余燃油</view>
              <view class="rightBox">{{ form.secondFuelExpend == null ? '' : form.secondFuelExpend }}L</view>
            </view>
            <view class="box">
              <view class="leftBox">旅客人数</view>
              <view class="rightBox">{{ form.secondPassengerNumber }}</view>
            </view>
          </view>
          <view class="title" v-show="userRole.includes('operationControl')&&routeType" @click="flyFit(2)"
                style="background-color: #ffffff85;padding: 10px 10px 10px 30px;overflow: hidden;">
            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="flightDelay1" :value="flightDelay1"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">航班延误</view>
          </view>
          <view v-show="flightDelay1 == 1&&userRole.includes('operationControl')&&routeType"
                class="uni-form-item uni-column"
                style="background-color: #ffffff85;overflow: hidden;padding: 10px 10px 10px 30px;">
            <view class="leftBox" style="float: left;">选择延误原因：</view>
            <view class="rightBox" style="float: left;">
              <view class="uni-list-cell">
                <picker mode="selector" :value="index2" @change="bindPickerChange2" :range="arraysas">
                  {{ arraysas[index2] }}
                </picker>
              </view>
            </view>

          </view>
        </view>

        <view class="uni-btn-v" v-show='flightSortiesId != null' style="margin-top: 10px;">
          <button type="primary" plain="true" @click="submit(1)" style="margin-bottom: 10px;">确认</button>
          <button type="warn" @click="submit(3)">退回</button>
        </view>
      </view>
    </scroll-view>

    <!-- 整体背景放在最后 -->
    <view class="bg-main">
      <image class="background" src="../../static/images/background.png"></image>
    </view>
  </view>
</template>
<script>
import {
  getHomeInfo, getFlightPlanSingle, flightSortiesUpdateSyn, dataArchive, confirmFlyMoment
} from '../../api/weChat.js';

const App = getApp();
export default {
  data() {
    return {
      routeType: false,
      currentTime: '',
      homeInfo: {},
      taskData: [],
      text: 'uni-app',
      navH: 0,
      isRefresh: true, // 开启下拉
      triggered: false,
      loadingText: '正在刷新',
      noData: false,
      _freshing: false,
      pageNum: 1,
      flag: true,
      flightSortiesId: '',
      form: {},
      arraysas: ['天气原因', '空管原因', '军事活动', '飞机故障', '航班计划调整', '航务保障', '机组原因', '机场原因', '油料原因', '旅客原因'],
      index: 0,
      index2: 0,
      userRole: '',
      flightDelay: 0,
      flightDelay1: 0,
    };
  },
  onLoad: function (options) {
    //自定义导航
    console.log(options)
    this._freshing = false;
    this.flightSortiesId = options.flightSortiesId || null;
    console.log(this.flightSortiesId + 1111111111)
    this.flightplanId = options.flightplanId;
    this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
    uni.setStorageSync('flightSortiesId', this.flightSortiesId);
    this.pageNum = 1
    this.flag = true
    this.getCurrentTime();
    this.getData();
  },
  onShow: function (options) {

  },
  created() {
    this.userRole = uni.getStorageSync('userRole');
  },
  methods: {
    flyFit(type) {
      if (type == 1) {
        this.flightDelay = this.flightDelay == 0 ? 1 : 0
      } else {
        this.flightDelay1 = this.flightDelay1 == 0 ? 1 : 0
      }
      this.$forceUpdate()
    },
    bindPickerChange(e) {
      console.log(e)
      this.index = e.detail.value
    },
    bindPickerChange2(e) {
      console.log(e)
      this.index2 = e.detail.value
    },
    check(type) {

      if (type == 1) {
        uni.navigateTo({
          url: '/pages/todo/captionDerectCheck?flightSortiesId=' + this.flightSortiesId
        });
      } else if (type == 2) {
        uni.navigateTo({
          url: '/pages/todo/copolitDerectCheck?flightSortiesId=' + this.flightSortiesId
        });
      } else if (type == 3) {
        uni.navigateTo({
          url: '/pages/todo/maintenanceDerectCheck?flightSortiesId=' + this.flightSortiesId
        });
      } else if (type == 4) {
        uni.navigateTo({
          url: '/pages/todo/deliveryDerectCheck?flightSortiesId=' + this.flightSortiesId
        });
      }
    },
    submit(type) {
      let param = {};
      if (type == 1) {
        param = {
          flightPlanId: this.flightplanId,
          captainConfirmStatus: 1,
          delayReason: this.arraysas[this.index],
          flightDelay: this.flightDelay,
          ocConfirmStatus: 1,
          secondDelayReason: this.arraysas[this.index2],
          secondFlightDelay: this.flightDelay1,
        }
        // arraysas[index]
      } else if (type == 3) {
        param = {
          flightPlanId: this.flightplanId,
          captainConfirmStatus: 2,
          delayReason: this.arraysas[this.index],
          flightDelay: this.flightDelay,
          ocConfirmStatus: 2,
          secondDelayReason: this.arraysas[this.index2],
          secondFlightDelay: this.flightDelay1,
        }
      }
      console.log(param)
      uni.showModal({
        title: '确认操作？',
        content: '',
        success: async function (resp) {
          if (resp.confirm) {
            try {
              const res = await confirmFlyMoment(param);     //班次取消
              uni.showToast({
                title: res.response.msg
              })
              setTimeout(() => {
                uni.navigateBack({
                  delta: 1,
                });
              }, 1500)
            } catch (e) {
              console.error(e)
            }
          } else if (resp.cancel) {
            console.log('用户点击取消');
          }
        }
      });


    },
    backMyPage() {
      uni.navigateBack({
        delta: 1
      });
    },
    onBottomRefresh() {
      if (this.flag) {
        this.pageNum += 1
        this.getData(1);
      }
    },
    getCurrentTime() {
      var myDate = new Date();
      var y = myDate.getFullYear()
      var m = myDate.getMonth() + 1
      m = m < 10 ? '0' + m : m
      var d = myDate.getDate()
      d = d < 10 ? ('0' + d) : d
      var week = myDate.getDay()
      var x;
      switch (week) {
        case 0:
          x = '周日';
          break;
        case 1:
          x = '周一';
          break;
        case 2:
          x = '周二';
          break;
        case 3:
          x = '周三';
          break;
        case 4:
          x = '周四';
          break;
        case 5:
          x = '周五';
          break;
        case 6:
          x = '周六';
          break;
      }
      this.currentTime = y + '/' + m + '/' + d + '  ' + x;
    },
    async getData(type) {
      let homeParam = {
        flightPlanId: this.flightplanId
      };
      try {
        const res = await getFlightPlanSingle(homeParam);
        this.homeInfo.lastUpdateTime = res.response.data.lastUpdateTime
        this.homeInfo.total = res.response.data.total
        console.log(res)
        // this.taskData = res.response.data.flightplanList;
        this.taskData = [res.response.data]
        this.routeType = res.response.data.routeType

      } catch (e) {
        console.error(e)
      }

      let homeParam2 = {
        flightPlanId: this.flightplanId
      };
      try {
        const res = await dataArchive(homeParam2);
        this.form = res.response.data
      } catch (e) {
        console.error(e)
      }

    },
    openLoading() { //被下拉
      this.triggered = true;
    },
    // 触发下拉刷新
    onRefresh() {
      if (this._freshing) return;
      this._freshing = true;
      if (!this.triggered) {
        this.triggered = true;
      }
      // this.loadStoreData();
      this.pageNum = 1
      this.flag = true
      this.triggered = false;
      this._freshing = false;
      this.getCurrentTime();
      this.getData();

    },
    // 下拉刷新复位
    onRestore() {
      this.triggered = false;
      this._freshing = false;
    },
    // 下拉刷新中止
    onAbort() {
      this.triggered = false;
      this._freshing = false;
    },
  }
};
</script>

<style lang="scss" scoped>
page {
  height: 100%;
}

.background {
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  z-index: -1;
  position: absolute;
  top: 0px;
  bottom: 0px;
}

.main {
  width: 100%;
  height: 100%;
}


.nav {
  width: 100%;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
  z-index: 10;
}

.nav-title {
  width: 100%;
  height: inherit;
  position: relative;
  z-index: 10;
  font-family: SF Pro Text;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;

  .nav-title-text {
    // padding-top: 10px;
    font-family: SF Pro Text;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #000000;

    &:first-child {
      font-weight: normal;
      position: absolute;
      left: 25px;
      font-size: 22px;
    }
  }
}


.refresh {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #eeeeee;
  color: #000000;
}

.content {
  padding: 0 16px;
}

.refresh-container {
  width: 750rpx;
  text-align: center;
  position: absolute;
  align-items: center;
  margin-bottom: 20px;
}

.refresh-container text {
}

.no-data-box {
  background: rgba(255, 255, 255, 0.64);
  height: 343px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .image {
    width: 140px;
    height: 140px;
  }

  .col-gray-500 {
    font-size: 14px;
  }
}

.spinning {
  margin-right: 4px;
  display: inline-block;
  -webkit-animation: rotate 1s linear infinite;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.statistics {
  margin-bottom: 12px;
  padding: 0 16px;
  background-color: #fff;
  background: rgba(255, 255, 255, 0.64);
  border: 1px solid rgba(255, 255, 255, 0.72);
  border-radius: 8px;
}

.content {
  overflow: auto;

  .box {
    font-size: 15px;
    padding: 10px 10px 10px 30px;
    background-color: #ffffff85;
    border-bottom: 1px solid #efefef;

    .leftBox {
      width: 150px;
      display: inline-block;
    }

    .rightBox {
      display: inline-block;
    }
  }
}
</style>
