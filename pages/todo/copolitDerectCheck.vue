<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <view class="nav bg-white" :style="'height:' + navH + 'px'">
      <view class="nav-title">
        <text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
        <text class="nav-title-text">副驾驶直接准备</text>
      </view>
    </view>
    <!-- 内容主体 -->
    <view class="content" :style="'height:calc(100vh - ' + navH + 'px)'" style="overflow: auto;">
      <task-card v-for="(item, index) in taskData" :key="index" :taskIndex="index"
                 :taskInfo="item" :flight="1" :show-option="false">
      </task-card>
      <!-- 机长直接准备 -->
      <view class="" v-for="(item,index) in meslist" :key='index' style="margin-bottom:10px">
        <view class="uni-form-item uni-column">
          <view style="padding-bottom: 10px;">
            <text>姓名:{{ item.fillInUserName }}</text>
          </view>
          <view style="overflow: hidden;">
            <icon type="success" style="float: left;margin-right: 8px;margin-top: 7px;" size="16"/>
            <text style="float: left;width:calc(100% - 25px)">签到</text>
          </view>
          <view style="overflow: hidden;">
            <icon type="success" style="float: left;margin-right: 8px;margin-top: 7px;" size="16"/>
            <text style="float: left;width:calc(100% - 25px)">身体健康状况满足运行要求</text>
          </view>
          <view style="overflow: hidden;">
            <icon type="success" style="float: left;margin-right: 8px;margin-top: 7px;" size="16"/>
            <text style="float: left;width:calc(100% - 25px)">已携带飞行任务书、空勤登机证</text>
          </view>
          <view style="overflow: hidden;">
            <icon type="success" style="float: left;margin-right: 8px;margin-top: 7px;" size="16"/>
            <text style="float: left;width:calc(100% - 25px)">着装符合要求，携带反光背心</text>
          </view>
          <view style="overflow: hidden;">
            <icon type="success" style="float: left;margin-right: 8px;margin-top: 7px;" size="16"/>
            <text style="float: left;width:calc(100% - 25px)">执照、近期经历、体检合格证、熟练检查、危险品培训在有效期内
            </text>
          </view>
          <view style="overflow: hidden;">
            <icon type="success" style="float: left;margin-right: 8px;margin-top: 7px;" size="16"/>
            <text style="float: left;width:calc(100% - 25px)">手电筒可用</text>
          </view>
          <view style="overflow: hidden;">
            <icon type="success" style="float: left;margin-right: 8px;margin-top: 7px;" size="16"/>
            <text style="float: left;width:calc(100% - 25px)">矫正视力眼镜两副(如适用)</text>
          </view>

          <!-- <view class="title">用户名:{{item.fillInUserName}}</view>
          <view class="title">机长直接准备内容:</view>
          <view class="uni-textarea">
            <textarea :disabled='true' maxlength="800" v-model='item.preparationContent'  />
          </view> -->
        </view>
        <view class="uni-form-item uni-column">
          <view class="title">填写时间:{{ item.fillingTime }}</view>
        </view>
      </view>


      <view class="no-data-box" v-if="noData">
        <image class="image" src="../../static/images/nodata.png" mode="widthFix"></image>
        <text class="col-gray-500">当前无数据</text>
      </view>
    </view>

    <!-- 整体背景放在最后 -->
    <view class="bg-main">
      <image class="background" src="../../static/images/background.png"></image>
    </view>
  </view>
</template>

<script>
import {getFlightPlanSingle, captainDirectPreparation, copilotDirectPreparationOnly} from '../../api/weChat.js';

const App = getApp();
export default {
  data() {
    return {
      navH: 0,
      taskInfo: '',
      flightSortiesId: '',
      preparationContent: '',
      meslist: [],
      noData: false,
      taskData: []

    };
  },
  mounted() {
    this.getData()
    this.getData2()
  },
  created() {
    // this.flightSortiesId = uni.getStorageSync('flightSortiesId');
    this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
  },
  onLoad: function (options) {
    this.flightSortiesId = options.flightSortiesId
    // this.userRole = uni.getStorageSync('userRole')
  },
  methods: {

    async getData() {
      let param = {
        flightPlanId: this.flightSortiesId,
      };
      try {
        const res = await copilotDirectPreparationOnly(param);
        this.meslist = res.response.data;
        if (this.meslist.length == 0) {
          this.noData = true
        } else {
          this.noData = false;
        }
      } catch (e) {
        console.error(e)
      }
    },
    async getData2(type) {
      let homeParam = {
        flightPlanId: this.flightSortiesId
      };
      try {
        const res = await getFlightPlanSingle(homeParam);
        // this.taskData = res.response.data.flightplanList;
        this.taskData = [res.response.data]

      } catch (e) {
        console.error(e)
      }
    },
    // 返回"我的"主页面
    backMyPage() {
      uni.navigateBack({
        url: '/pages/home/<USER>'
      });
    },

  }
};
</script>

<style lang="less" scoped>

page {
  height: 100%;
}

.background {
  width: 100%;
  height: 100%;
  // position: fixed;
  background-size: 100% 100%;
  z-index: -1;
  position: absolute;
  top: 0px;
  bottom: 0px;
}

.no-data-box {
  background: rgba(255, 255, 255, 0.64);
  height: 343px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .image {
    width: 140px;
    height: 140px;
  }

  .col-gray-500 {
    font-size: 14px;
  }
}

.scroll-view {
  height: calc(100vh - 135px) !important;
}

.nav {
  width: 100%;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
  z-index: 10;
}

.nav-title {
  width: 100%;
  height: inherit;
  position: relative;
  z-index: 10;
  font-family: SF Pro Text;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;

  .nav-title-text {
    // padding-top: 10px;
    font-family: SF Pro Text;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #000000;

    &:first-child {
      font-weight: normal;
      position: absolute;
      left: 25px;
      font-size: 22px;
    }
  }
}

.uni-column {
  background: #ffffff86;
  line-height: 30px;
  padding: 10px 20px;
  font-size: 13px;

}

.content {
  padding: 10px;
}

textarea {
  border: 1px solid #efefef;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
}
</style>
