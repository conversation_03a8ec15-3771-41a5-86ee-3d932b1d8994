<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <view class="nav bg-white" :style="'height:' + navH + 'px'">
      <view class="nav-title">
        <text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
        <text class="nav-title-text">预先准备</text>
      </view>
    </view>
    <!-- 内容主体 -->
    <view class="content" :style="'height:calc(100vh - ' + navH + 'px)'" style="overflow: auto;">
      <task-card v-for="(item, index) in taskData" :key="index" :taskIndex="index"
                 :taskInfo="item" :flight="1" :show-option="false">
      </task-card>
      <view v-for="(item,index) in list" style="border-top:5px solid #eee">
        <view class="uni-form-item uni-column">
          <view class="title">姓名:{{ item.fillInUserName }}</view>
        </view>
        <!-- <view class="uni-form-item uni-column">
          <view class="title">着装、反光背心、矫正视力眼镜等是否准备充分:</view>
          <view class="">
            {{item.readyStatus == 1 ? '是' : '否'}}
          </view>
        </view> -->
        <view class="uni-form-item uni-column" v-if='type !=2'>
          <view class="title">起降机场、备降机场（天气、程序、特点）:</view>
          <view class="uni-textarea">
            <textarea :disabled='true' maxlength="800" v-model='item.alternateAirport'
                      placeholder-style='font-size:12px'/>
          </view>
        </view>
        <view class="uni-form-item uni-column" v-if='type !=2'>
          <view class="title">航线、机场（飞行特点、注意事项）:</view>
          <view class="uni-textarea">
            <textarea :disabled='true' maxlength="800" v-model='item.routeAndAirport'
                      placeholder-style='font-size:12px'/>
          </view>
        </view>
        <view class="uni-form-item uni-column" v-if='type !=2'>
          <view class="title">特情处置准备:</view>
          <view class="uni-textarea">
            <textarea :disabled='true' maxlength="800" v-model='item.specialDisposition'
                      placeholder-style='font-size:12px'/>
          </view>
        </view>
        <view class="uni-form-item uni-column" v-show='type==2'>
          <view class="title" style="overflow: hidden;">
            <view style="float: left;margin-right: 20px;">飞行适航状态:</view>
            <view style="float: left;">
              {{ item.airworthinessStatus == 0 ? '否' : '是' }}
            </view>
          </view>
        </view>
        <view class="uni-form-item uni-column" v-show='type==2&&item.airworthinessStatus==0'>
          <view class="title">故障说明:</view>
          <view class="uni-textarea">
            <textarea maxlength="800" v-model='form.faultRemark' placeholder="请填写故障说明"
                      placeholder-style='font-size:12px'/>
          </view>
        </view>
      </view>
      <view class="no-data-box" v-if="noData">
        <image class="image" src="../../static/images/nodata.png" mode="widthFix"></image>
        <text class="col-gray-500">当前无数据</text>
      </view>
    </view>
    <!-- 整体背景放在最后 -->
    <view class="bg-main">
      <image class="background" src="../../static/images/background.png"></image>
    </view>
  </view>
</template>

<script>
import {
  prepareForFlightplanAll,
  getFlightPlanSingleCheck,
  getFlightPlanSingle,
  prepareForFlightplan,
  getFlightDetailById,
  getVerifySure,
  getHomeItemData
} from '../../api/weChat.js';

const App = getApp();
export default {
  data() {
    return {
      navH: 0,
      taskInfo: '',
      flightSortiesId: '',
      form: {
        readyStatus: '1'
      },
      taskData: [],
      type: null,
      list: [],
      noData: false,
      showFlag: false
    };
  },
  mounted() {

  },
  created() {
    this.userRole = uni.getStorageSync('userRole');
    var flagDD = false
    this.userRole.map((i) => {
      if (i == 'pilot') {
        flagDD = true
      }
    })
    if (flagDD) {
      this.showFlag = true
    }
  },
  onLoad: function (options) {

    this.type = options.type;
    this.flightplanId = options.flightplanId;
    this.flightSortiesId = options.flightSortiesId;
    console.log(options)
    this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
    this.getData()
  },
  methods: {

    changeState(e) {
      this.form.readyStatus = e.detail.value
    },
    // 返回"我的"主页面
    backMyPage() {
      uni.navigateBack({
        url: '/pages/home/<USER>'
      });
    },
    async getData() {
      let param = {
        flightPlanId: this.flightSortiesId,
        roleType: this.type
      };
      try {
        const res = await prepareForFlightplanAll(param);
        this.list = res.response.data;
        if (this.list.length == 0) {
          this.noData = true
        } else {
          this.noData = false;
        }
      } catch (e) {
        console.error(e)
      }

      let homeParam = {
        flightPlanId: this.flightSortiesId
      };
      try {
        const res = await getFlightPlanSingle(homeParam);
        // this.taskData = res.response.data.flightplanList;
        this.taskData = [res.response.data]

      } catch (e) {
        console.error(e)
      }
    },
    //提交答题
    async submit(type) {
      console.log(111)
      let param = this.form;
      param.flightplanId = this.flightplanId
      console.log(param)
      // return false

      try {
        const res = await prepareForFlightplan(param);
        uni.showToast({
          title: res.response.msg
        })
        // setTimeout(()=>{
        // 	uni.navigateBack({
        // 	    delta: 1,
        // 	});
        // },1500)
      } catch (e) {
        console.error(e)
      }
    }
  }
};
</script>

<style lang="less" scoped>

page {
  height: 100%;
}

.background {
  width: 100%;
  height: 100%;
  // position: fixed;
  background-size: 100% 100%;
  z-index: -1;
  position: absolute;
  top: 0px;
  bottom: 0px;
}

.nav {
  width: 100%;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
  z-index: 10;
}

.nav-title {
  width: 100%;
  height: inherit;
  position: relative;
  z-index: 10;
  font-family: SF Pro Text;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;

  .nav-title-text {
    // padding-top: 10px;
    font-family: SF Pro Text;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #000000;

    &:first-child {
      font-weight: normal;
      position: absolute;
      left: 25px;
      font-size: 22px;
    }
  }
}

.uni-column {
  background: #ffffff86;
  line-height: 30px;
  padding: 10px 20px;
  font-size: 13px;

}

.content {
  padding: 10px;
}

textarea {
  border: 1px solid #efefef;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
}

.no-data-box {
  background: rgba(255, 255, 255, 0.64);
  height: 343px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .image {
    width: 140px;
    height: 140px;
  }

  .col-gray-500 {
    font-size: 14px;
  }
}
</style>
