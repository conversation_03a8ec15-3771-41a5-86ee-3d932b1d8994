<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <view class="nav bg-white" :style="'height:' + navH + 'px'">
      <view class="nav-title">
        <text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
        <text class="nav-title-text">机长飞行讲评</text>
      </view>
    </view>
    <!-- 内容主体 -->
    <view class="content" :style="'height:calc(100vh - ' + navH + 'px)'" style="overflow: auto;">
      <form @submit="submit">
        <task-card v-for="(item, index) in taskData" :key="index" :taskIndex="index"
                   :taskInfo="item" :flight="1" :show-option="false">
        </task-card>
        <view class="uni-form-item uni-column" @click="flyFit('carefulInspectionDuringFlight')">
          <view class="title" style="overflow: hidden;">
            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.carefulInspectionDuringFlight"
                            :value="form.carefulInspectionDuringFlight"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">
              飞行过程中执行检查单时是否做到“心到、口到、眼到、手到”
            </view>
          </view>
        </view>
        <view class="uni-form-item uni-column" @click="flyFit('confirmedInspectionAroundAircraft')">
          <view class="title" style="overflow: hidden;">
            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;margin-top: 5px;" :checked="form.confirmedInspectionAroundAircraft"
                            :value="form.confirmedInspectionAroundAircraft"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">飞行前绕机检查是否按要求完成</view>

          </view>
        </view>
        <view class="uni-form-item uni-column" @click="flyFit('aircraftVariousSystemsProperly')">
          <view class="title" style="overflow: hidden;">
            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.aircraftVariousSystemsProperly"
                            :value="form.aircraftVariousSystemsProperly"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">
              飞机在运行过程中，仪表系统、刹车系统、转弯机构是否工作正常
            </view>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title" style="overflow: hidden;" @click="flyFit('unsafeIncidentsNoOccurred')">

            <view style="float: left;">
              <checkbox-group style="transform:scale(0.7);margin-top: 4px;">
                <label>
                  <checkbox style="float: left;" :checked="form.unsafeIncidentsNoOccurred"
                            :value="form.unsafeIncidentsNoOccurred"/>
                </label>
              </checkbox-group>
            </view>
            <view style="float: left;margin-right: 20px;width: calc(100% - 50px);">运行过程中是否有不安全事件的发生
            </view>
          </view>
        </view>
        <view class="uni-form-item uni-column" v-show='form.unsafeIncidentsNoOccurred==1'>
          <view class="title">不安全事件描述:</view>
          <view class="uni-textarea">
            <textarea maxlength="800" v-model='form.unsafeIncidentsRemark' placeholder="请填写运行过程中不安全事件描述"
                      placeholder-style='font-size:12px'/>
          </view>
        </view>

        <view class="uni-form-item uni-column">
          <view class="title">飞行预先准备、直接准备的情况讲评:</view>
          <view class="uni-textarea">
            <textarea maxlength="800" v-model='form.commentaryOnFlightPreparation'
                      placeholder="请填写飞行预先准备、直接准备的情况讲评" placeholder-style='font-size:12px'/>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title">飞机起飞、爬升、巡航、进近以及着陆的操纵情况讲评:</view>
          <view class="uni-textarea">
            <textarea maxlength="800" v-model='form.commentaryOnAircraftOperations'
                      placeholder="请填写飞机起飞、爬升、巡航、进近以及着陆的操纵情况讲评"
                      placeholder-style='font-size:12px'/>
          </view>
        </view>
        <view class="uni-form-item uni-column">
          <view class="title">机组可以提出有效改进措施，不断完善公司运行制度:</view>
          <view class="uni-textarea">
            <textarea maxlength="800" v-model='form.improvementMeasures'
                      placeholder="请填写机组可以提出有效改进措施，不断完善公司运行制度"
                      placeholder-style='font-size:12px'/>
          </view>
        </view>

        <view class="uni-btn-v">
          <button type="primary" plain="true" form-type="submit" style="margin-bottom: 10px;">提交</button>
          <!-- <button form-type="submit">Submit</button> -->
          <button type="default" @click="resetForm">清空</button>
        </view>
      </form>
    </view>
    <!-- 整体背景放在最后 -->
    <view class="bg-main">
      <image class="background" src="../../static/images/background.png"></image>
    </view>
  </view>
</template>

<script>
import {log} from 'util';
import {
  getFlightPlanSingle,
  flightCommentary,
  maintenanceDirectPreparation,
  getFlightDetailById,
  getVerifySure,
  getHomeItemData
} from '../../api/weChat.js';

const App = getApp();
export default {
  data() {
    return {
      navH: 0,
      taskInfo: '',
      flightplanId: '',
      flightSortiesId: '',
      form: {
        carefulInspectionDuringFlight: 0,
        confirmedInspectionAroundAircraft: 0,
        aircraftVariousSystemsProperly: 0,
        improvementMeasures: '',
        commentaryOnAircraftOperations: '',
        unsafeIncidentsNoOccurred: '',
        commentaryOnFlightPreparation: '',
      },
      taskData: [],
      roleType: 1,
    };
  },
  mounted() {

  },
  created() {
    this.userRole = uni.getStorageSync('userRole');
  },
  onLoad: function (options) {
    this.flightplanId = options.flightplanId;
    this.flightSortiesId = options.flightSortiesId;

    this.roleType = options.roleType;
    // this.roleType = 2;
    this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
    this.getData()
  },
  methods: {
    resetForm() {
      this.form = {}
      this.form.signIn = 0
      this.form.completedCheckedAndRecordedFuel = 0
      this.form.verifiedRelatedParameters = 0
      this.form.airworthinessStatus = 0
    },
    flyFit(param) {
      this.form[param] = this.form[param] == 0 ? 1 : 0
      this.$forceUpdate()
    },
    // 返回"我的"主页面
    backMyPage() {
      uni.navigateBack({
        url: '/pages/home/<USER>'
      });
    },
    async getData(type) {
      let homeParam = {
        flightPlanId: this.flightplanId
      };
      try {
        const res = await getFlightPlanSingle(homeParam);
        // this.taskData = res.response.data.flightplanList;
        this.taskData = [res.response.data]

      } catch (e) {
        console.error(e)
      }
      console.log(this.flag)
    },

    async submit(type) {
      console.log(111)
      let param = JSON.parse(JSON.stringify(this.form));
      param.unsafeIncidentsNoOccurred = param.unsafeIncidentsNoOccurred == 1 ? param.unsafeIncidentsNoOccurred + '' : '0'
      param.flightPlanId = this.flightplanId
      // param.roleType = this.roleType
      try {
        const res = await flightCommentary(param);
        uni.showToast({
          title: res.response.msg
        })
        setTimeout(() => {
          uni.navigateTo({
            url: '/pages/home/<USER>'
          });
        }, 1500)
      } catch (e) {
        console.error(e)
      }
    }
  }
};
</script>

<style lang="less" scoped>

page {
  height: 100%;
}

.background {
  width: 100%;
  height: 100%;
  // position: fixed;
  background-size: 100% 100%;
  z-index: -1;
  position: absolute;
  top: 0px;
  bottom: 0px;
}

.nav {
  width: 100%;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
  z-index: 10;
}

.nav-title {
  width: 100%;
  height: inherit;
  position: relative;
  z-index: 10;
  font-family: SF Pro Text;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;

  .nav-title-text {
    // padding-top: 10px;
    font-family: SF Pro Text;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #000000;

    &:first-child {
      font-weight: normal;
      position: absolute;
      left: 25px;
      font-size: 22px;
    }
  }
}

.uni-column {
  background: #ffffff86;
  line-height: 30px;
  padding: 10px 20px;
  font-size: 13px;

}

.content {
  padding: 10px;
}

textarea {
  border: 1px solid #efefef;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
}
</style>
