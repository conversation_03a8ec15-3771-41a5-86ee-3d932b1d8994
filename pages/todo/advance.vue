<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <view class="nav bg-white" :style="'height:' + navH + 'px'">
      <view class="nav-title">
        <text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
        <text class="nav-title-text">预先准备</text>
      </view>
    </view>
    <!-- 内容主体 -->
    <view class="content" :style="'height:calc(100vh - ' + navH + 'px)'" style="overflow: auto;">
      <form @submit="submit">
        <!--        				<task-cardstor v-for="(item, index) in taskData" :key="index" :taskIndex="index" -->
        <!--        					:taskInfo="item" :types='1' :flight='1'>-->
        <!--        				</task-cardstor>-->
        <task-card v-for="(item, index) in taskData" :key="index" :taskIndex="index"
                   :taskInfo="item" :flight="1" :show-option="false">
        </task-card>
        <view class="uni-form-item uni-column" v-show='roleType!=2'>
          <view class="title">起降机场、备降机场（天气、程序、特点）:</view>
          <view class="uni-textarea">
            <textarea maxlength="800" v-model='form.alternateAirport'
                      placeholder="请填写起降机场、备降机场（天气、程序、特点）" placeholder-style='font-size:12px'/>
          </view>
        </view>

        <view class="uni-form-item uni-column" v-show='roleType!=2'>
          <view class="title">航线、机场（飞行特点、注意事项）:</view>
          <view class="uni-textarea">
            <textarea maxlength="800" v-model='form.routeAndAirport' placeholder="请填写航线、机场（飞行特点、注意事项）"
                      placeholder-style='font-size:12px'/>
          </view>
        </view>
        <view class="uni-form-item uni-column" v-show='roleType!=2'>
          <view class="title">特情处置准备:</view>
          <view class="uni-textarea">
            <textarea maxlength="800" v-model='form.specialDisposition' placeholder="请填写备降机场（天气、程序、特点）"
                      placeholder-style='font-size:12px'/>
          </view>
        </view>
        <view class="uni-form-item uni-column" v-show='roleType==2'>
          <view class="title" style="overflow: hidden;">
            <view style="float: left;margin-right: 20px;">飞行适航状态:</view>
            <view style="float: left;">
              <checkbox-group @change="flyFit">
                <label>
                  <checkbox value="1"/>
                  是
                </label>
              </checkbox-group>
            </view>
          </view>
        </view>
        <view class="uni-form-item uni-column" v-show='roleType==2&&form.airworthinessStatus==0'>
          <view class="title" style="overflow: hidden;">
            <view style="float: left;margin-right: 20px;">故障符合IMEL规范:</view>
            <view style="float: left;">
              <checkbox-group @change="flyFit2">
                <label>
                  <checkbox value="1"/>
                  是
                </label>
              </checkbox-group>
            </view>
          </view>
        </view>
        <view class="uni-form-item uni-column" v-show='roleType==2&&form.airworthinessStatus==0'>
          <view class="title">故障说明:</view>
          <view class="uni-textarea">
            <textarea maxlength="800" v-model='form.faultRemark' placeholder="请填写故障说明"
                      placeholder-style='font-size:12px'/>
          </view>
        </view>
        <view class="uni-btn-v">
          <button type="primary" plain="true" form-type="submit" style="margin-bottom: 10px;">提交</button>
          <!-- <button form-type="submit">Submit</button> -->
          <button type="default" @click="resetForm">清空</button>
        </view>
      </form>
    </view>
    <!-- 整体背景放在最后 -->
    <view class="bg-main">
      <image class="background" src="../../static/images/background.png"></image>
    </view>
  </view>
</template>

<script>
import {
  getFlightPlanSingle,
  prepareForFlightplan,
  addMaintenancePrepare,
} from '../../api/weChat.js';

const App = getApp();
export default {
  data() {
    return {
      navH: 0,
      taskInfo: '',
      flightplanId: '',
      flightSortiesId: '',
      form: {
        alternateAirport: '',
        routeAndAirport: '',
        specialDisposition: '',
        faultRemark: '',
        airworthinessStatus: 0,
        airworthinessStatus1: 0,
      },
      taskData: [],
      showFlag: false,
      roleType: 1,
    };
  },
  mounted() {

  },
  created() {
    this.userRole = uni.getStorageSync('userRole');
    var flagDD = false
    this.userRole.map((i) => {
      if (i == 'pilot') {
        flagDD = true
      }
    })
    if (flagDD) {
      this.showFlag = true
    }
  },
  onLoad: function (options) {
    this.flightplanId = options.flightplanId;
    this.flightSortiesId = options.flightSortiesId;

    this.roleType = options.roleType;
    // this.roleType = 2;
    console.log(options)
    this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
    this.getData()
  },
  methods: {
    resetForm() {
      this.form = {}
      this.form.airworthinessStatus = 0
    },
    flyFit(e) {
      this.form.airworthinessStatus = e.target.value.length == 0 ? 0 : 1
      this.$forceUpdate()
    },
    flyFit2(e) {
      this.form.airworthinessStatus1 = e.target.value.length == 0 ? 0 : 1
      this.$forceUpdate()
    },
    goGiven() {
      uni.navigateTo({
        url: '/pages/todo/givenFlight?flightplanId=' + this.flightplanId
      });
    },
    changeState(e) {
      this.form.readyStatus = e.detail.value
    },
    // 返回"我的"主页面
    backMyPage() {
      uni.navigateBack({
        url: '/pages/home/<USER>'
      });
    },
    async getData(type) {
      let homeParam = {
        flightPlanId: this.flightplanId
      };
      try {
        const res = await getFlightPlanSingle(homeParam);
        // this.taskData = res.response.data.flightplanList;
        this.taskData = [res.response.data]

      } catch (e) {
        console.error(e)
      }
      console.log(this.flag)
    },

    async submit(type) {
      let that = this
      let param = that.form;
      param.flightPlanId = that.flightplanId
      param.roleType = that.roleType
      if (param.airworthinessStatus != 1) { //不适航
        param.airworthinessStatus = param.airworthinessStatus1 == 0 ? 0 : 2
      }

      try {
        const res = that.roleType == 2 ? await addMaintenancePrepare(param) : await prepareForFlightplan(param)
        uni.showToast({
          title: res.response.msg
        })
        setTimeout(() => {
          uni.navigateTo({
            url: '/pages/home/<USER>'
          });
        }, 1500)
      } catch (e) {
        console.error(e)
      }
    }
  }
};
</script>

<style lang="less" scoped>

page {
  height: 100%;
}

.background {
  width: 100%;
  height: 100%;
  // position: fixed;
  background-size: 100% 100%;
  z-index: -1;
  position: absolute;
  top: 0px;
  bottom: 0px;
}

.nav {
  width: 100%;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
  z-index: 10;
}

.nav-title {
  width: 100%;
  height: inherit;
  position: relative;
  z-index: 10;
  font-family: SF Pro Text;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;

  .nav-title-text {
    // padding-top: 10px;
    font-family: SF Pro Text;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #000000;

    &:first-child {
      font-weight: normal;
      position: absolute;
      left: 25px;
      font-size: 22px;
    }
  }
}

.uni-column {
  background: #ffffff86;
  line-height: 30px;
  padding: 10px 20px;
  font-size: 13px;

}

.content {
  padding: 10px;
}

textarea {
  border: 1px solid #efefef;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
}
</style>
