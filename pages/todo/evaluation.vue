<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <view class="nav bg-white" :style="'height:' + navH + 'px'">
      <view class="nav-title">
        <text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
        <text class="nav-title-text">副驾驶上传任务书</text>
      </view>
    </view>
    <!-- 内容主体 -->
    <view class="content" :style="'height:calc(100vh - ' + navH + 'px)'" style="overflow: auto;">
      <form @submit="submit">

        <!-- 副驾驶上传任务书阶段 -->
        <view class="">
          <view class="uni-form-item uni-column">
            <view class="title">上传任务书:</view>
            <button type="default" size="mini" @click='uploadFiles' style="margin-right: 5px;">上传图片</button>
            <button type="default" size="mini" @click='clearFiles'>清空图片</button>
            <view class="" style="display: flex;">
              <image style="height: 100px;" v-for="(item,index) in path" :key='index' @click='previews(path)'
                     v-show="path.length>0" :src="item" mode=""></image>
            </view>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">滑行开始日期:</view>
            <picker class="time-picker" mode="date" :value="form.taxiStartTimeDate" :start="currentTime"
                    @change="bindTimeChange($event, 5)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.taxiStartTimeDate }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">滑行开始时间:</view>
            <picker class="time-picker" mode="time" :value="form.taxiStartTime1" start="09:00" end="08:59"
                    @change="bindTimeChange($event, 1)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.taxiStartTime1 }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">起飞日期:</view>
            <picker class="time-picker" mode="date" :value="form.departureTimeDate" :start="currentTime"
                    @change="bindTimeChange($event, 6)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.departureTimeDate }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">起飞时间:</view>
            <picker class="time-picker" mode="time" :value="form.departureTime1" start="09:00" end="08:59"
                    @change="bindTimeChange($event, 2)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.departureTime1 }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">降落日期:</view>
            <picker class="time-picker" mode="date" :value="form.landingTimeDate" :start="currentTime"
                    @change="bindTimeChange($event, 7)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.landingTimeDate }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">降落时间:</view>
            <picker class="time-picker" mode="time" :value="form.landingTime1" start="09:00" end="08:59"
                    @change="bindTimeChange($event, 3)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.landingTime1 }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">关车日期:</view>
            <picker class="time-picker" mode="date" :value="form.shutdownTimeDate" :start="currentTime"
                    @change="bindTimeChange($event, 8)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.shutdownTimeDate }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">关车时间:</view>
            <picker class="time-picker" mode="time" :value="form.shutdownTime1" start="09:00" end="08:59"
                    @change="bindTimeChange($event, 4)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.shutdownTime1 }}</view>
            </picker>
          </view>
        </view>

        <view class="uni-btn-v">
          <button type="primary" plain="true" form-type="submit" style="margin-bottom: 10px;">提交</button>
          <!-- <button form-type="submit">Submit</button> -->
          <button type="default" form-type="reset" @click='path=[]'>清空</button>
        </view>
      </form>
    </view>
    <!-- 整体背景放在最后 -->
    <view class="bg-main">
      <image class="background" src="../../static/images/background.png"></image>
    </view>
  </view>
</template>

<script>
import api from "../../api/index.js"
import {getFlightDetailById, getVerifySure, getHomeItemData, flightSortiesSynBook} from '../../api/weChat.js';

const App = getApp();
export default {
  data() {
    return {

      navH: 0,
      timer: '',
      taskIndex: 1,
      inputValue: '',
      taskInfo: '',
      componentData: '',
      flightSortiesId: '',
      imgUrl: '',
      state: '',
      userRole: '',
      form: {
        taxiStartTime1: '',
        departureTime1: '',
        landingTime1: '',
        shutdownTime1: '',
        taxiStartTimeDate: '',
        departureTimeDate: '',
        landingTimeDate: '',
        shutdownTimeDate: '',
      },
      currentTime: '',
      path: []
    };
  },
  mounted() {
    console.log(this.currentTime)
    this.getCurrentTime()
    this.form.taxiStartTimeDate = this.currentTime
    this.form.departureTimeDate = this.currentTime
    this.form.landingTimeDate = this.currentTime
    this.form.shutdownTimeDate = this.currentTime
  },
  onLoad: function (options) {
    this.state = options.state;
    this.flightSortiesId = options.flightSortiesId;

    console.log(options)
    this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
    // this.userRole = uni.getStorageSync('userRole')
    this.userRole = 1


  },
  methods: {
    clearFiles() {
      this.path = []
    },
    getCurrentTime() {
      var myDate = new Date();
      var y = myDate.getFullYear()
      var m = myDate.getMonth() + 1
      m = m < 10 ? '0' + m : m
      var d = myDate.getDate()
      d = d < 10 ? ('0' + d) : d
      var week = myDate.getDay()
      var x;
      switch (week) {
        case 0:
          x = '周日';
          break;
        case 1:
          x = '周一';
          break;
        case 2:
          x = '周二';
          break;
        case 3:
          x = '周三';
          break;
        case 4:
          x = '周四';
          break;
        case 5:
          x = '周五';
          break;
        case 6:
          x = '周六';
          break;
      }
      this.currentTime = y + '-' + m + '-' + d
    },
    uploadFiles() {
      let that = this
      uni.chooseImage({
        count: 9, //默认9
        sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
        // sourceType: ['album'], //从相册选择
        name: '',
        success: function (res) {
          console.log(res.tempFilePaths)
          res.tempFilePaths.map((i) => {
            that.path.push(i)
          })

        }
      });
    },
    bindTimeChange(e, id) {
      console.log(e.target.value, id)
      if (id == 1) {
        this.form.taxiStartTime1 = e.target.value;
        console.log(this.form.taxiStartTime)
      }
      if (id == 2) {
        this.form.departureTime1 = e.target.value;
      }
      if (id == 3) {
        this.form.landingTime1 = e.target.value;
      }
      if (id == 4) {
        this.form.shutdownTime1 = e.target.value;
      }
      if (id == 5) {
        this.form.taxiStartTimeDate = e.target.value;
      }
      if (id == 6) {
        this.form.departureTimeDate = e.target.value;
      }
      if (id == 7) {
        this.form.landingTimeDate = e.target.value;
      }
      if (id == 8) {
        this.form.shutdownTimeDate = e.target.value;
      }
    },
    // 返回"我的"主页面
    backMyPage() {
      uni.navigateBack({
        url: '/pages/home/<USER>'
      });
    },

    async getData() {
      let param = {
        flightSortiesId: this.flightSortiesId,
      };
      try {
        const res = await getFlightDetailById(param);
        this.taskInfo = res.response.data;
      } catch (e) {
        console.error(e)
      }
    },
    previews(item) {
      uni.previewImage({
        urls: item
      })
    },
    //提交答题
    submit(type) {
      let that = this
      let param = {}
      param.flightSortiesId = this.flightSortiesId
      param.taxiStartTime = this.form.taxiStartTimeDate + ' ' + this.form.taxiStartTime1
      param.departureTime = this.form.departureTimeDate + ' ' + this.form.departureTime1
      param.landingTime = this.form.landingTimeDate + ' ' + this.form.landingTime1
      param.shutdownTime = this.form.shutdownTimeDate + ' ' + this.form.shutdownTime1
      let formData = {'file': this.path, 'flightSortiesId': this.flightSortiesId}
      console.log(formData)
      if (that.path.length == []) {
        uni.showToast({
          title: '请上传任务书！'
        })
      }
      uni.uploadFile({
        url: api.baseUrl + '/wechat/flightDetail/importAssignmentFile1',
        filePath: that.path[0],
        formData: param,
        name: 'file',
        header: {
          AuthCode: uni.getStorageSync('token'),
          AuthID: uni.getStorageSync('userInfo').userId
        },
        async success(res) {
          res = JSON.parse(res.data)
          console.log(res)
          if (that.path.length == 1) {
            uni.showToast({
              title: res.msg
            })
            setTimeout(() => {
              uni.navigateBack({
                delta: 1,
              });
            }, 1500)
          } else {
            var idFile = res.data.id
            that.path.map((i, o) => {
              if (o != 0) {
                uni.uploadFile({
                  url: baseUrl + '/wechat/flightDetail/importAssignmentFile1change',
                  filePath: i,
                  formData: {flightSortiesId: that.flightSortiesId, file: i},
                  name: 'file',
                  header: {
                    AuthCode: uni.getStorageSync('token'),
                    AuthID: uni.getStorageSync('userInfo').userId
                  },
                  success(respond) {
                    respond = JSON.parse(respond.data)
                    if (o == that.path.length - 1) {
                      uni.showToast({
                        title: respond.msg
                      })
                      setTimeout(() => {
                        uni.navigateBack({
                          delta: 1,
                        });
                      }, 1500)
                    }
                  }, error(error) {
                    uni.showToast({
                      title: '上传失败'
                    })
                  }
                })
              }
            })
          }

        }, error(error) {
          uni.showToast({
            title: '上传失败'
          })
        }
      })


      // uni.uploadFile({
      // 	url:'https://ga.swcares.com.cn/trade/oc/wechat/wechat/flightDetail/importAssignmentFile',
      // 	filePath:that.path,
      // 	formData:formData,
      // 	name:'file',
      // 	header:{
      // 		AuthCode:uni.getStorageSync('token'),
      // 		AuthID:uni.getStorageSync('userInfo').userId
      // 	},
      // 	async success(responce){
      // 		// param.id= JSON.parse(responce.data).data.id
      // 		const res =await flightSortiesSynBook(param)
      // 		uni.showToast({
      // 			title: res.response.msg
      // 		})
      // 		setTimeout(()=>{
      // 			uni.navigateBack({
      // 			    delta: 1,
      // 			});
      // 		},1500)
      // 	},error(error){
      // 		console.log(error)
      // 	}
      // })

    }
  }
};
</script>

<style lang="less" scoped>

page {
  height: 100%;
}

.background {
  width: 100%;
  height: 100%;
  // position: fixed;
  background-size: 100% 100%;
  z-index: -1;
  position: absolute;
  top: 0px;
  bottom: 0px;
}

.nav {
  width: 100%;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
  z-index: 10;
}

.nav-title {
  width: 100%;
  height: inherit;
  position: relative;
  z-index: 10;
  font-family: SF Pro Text;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;

  .nav-title-text {
    // padding-top: 10px;
    font-family: SF Pro Text;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #000000;

    &:first-child {
      font-weight: normal;
      position: absolute;
      left: 25px;
      font-size: 22px;
    }
  }
}

.uni-column {
  background: #ffffff86;
  line-height: 30px;
  padding: 10px 20px;
  font-size: 13px;

}

.content {
  padding: 10px;
}

textarea {
  border: 1px solid #efefef;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
}
</style>
