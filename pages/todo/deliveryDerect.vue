<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <view class="nav bg-white" :style="'height:' + navH + 'px'">
      <view class="nav-title">
        <text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
        <text class="nav-title-text">责任运控直接准备</text>
      </view>
    </view>
    <!-- 内容主体 -->
    <view class="content" :style="'height:calc(100vh - ' + navH + 'px)'" style="overflow: auto;">
      <form @submit="submit">
        <task-card v-for="(item, index) in taskData" :key="index" :taskIndex="index" :types='1'
                   :taskInfo="item" :show-option="false">
        </task-card>
        <!-- 运控直接准备阶段 -->
        <view class="">
          <view class="uni-form-item uni-column">
            <view class="title">QNH:</view>
            <view class="uni-textarea">
              <textarea maxlength="800" v-model='form.qnh' placeholder="请填写QNH" placeholder-style='font-size:12px'/>
            </view>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">天气资料:</view>
            <view class="uni-textarea">
              <textarea maxlength="800" v-model='form.weatherData' placeholder="请填写天气资料"
                        placeholder-style='font-size:12px'/>
            </view>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">航路航线:</view>
            <view class="uni-textarea">
              <textarea maxlength="800" v-model='form.airRoute' placeholder="请填写航路航线"
                        placeholder-style='font-size:12px'/>
            </view>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">放行提示:</view>
            <view class="uni-textarea">
              <textarea maxlength="800" v-model='form.releasePrompt' placeholder="请填写放行提示"
                        placeholder-style='font-size:12px'/>
            </view>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">运行提示内容:</view>
            <view class="uni-textarea">
              <textarea maxlength="800" v-model='form.operationPromptContent' placeholder="请填写运行提示内容"
                        placeholder-style='font-size:12px'/>
            </view>
          </view>

          <view class="uni-form-item uni-column">
            <view class="title">上传舱单:</view>
            <button type="default" size="mini" @click='uploadFiles' style="margin-right: 5px;">上传图片</button>
            <button type="default" size="mini" @click='clearFiles'>清空图片</button>
            <view class="" style="display: flex;">
              <image style="height: 100px;" v-for="(item,index) in path" :key='index' @click='previews(path)'
                     v-show="path.length>0" :src="item" mode=""></image>
            </view>
          </view>
        </view>


        <view class="uni-btn-v">
          <button type="primary" plain="true" form-type="submit" style="margin-bottom: 10px;">提交</button>
          <!-- <button form-type="submit">Submit</button> -->
          <button type="default" form-type="reset">清空</button>
        </view>
      </form>
    </view>
    <!-- 整体背景放在最后 -->
    <view class="bg-main">
      <image class="background" src="../../static/images/background.png"></image>
    </view>
  </view>
</template>

<script>
import api from "../../api/index.js"
import {getFlight, ocDirectPreparation} from '../../api/weChat.js';

const App = getApp();
export default {
  data() {
    return {
      time1: '1213',
      time2: '',
      navH: 0,
      timer: '',
      taskIndex: 1,
      inputValue: '',
      taskInfo: '',
      componentData: '',
      flightSortiesId: '',
      imgUrl: '',
      state: '',
      userRole: '',
      form: {},
      taskData: [],
      path: []
    };
  },
  mounted() {

  },
  onLoad: function (options) {
    this.flightSortiesId = options.flightSortiesId;
    console.log(options)
    this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
    // this.userRole = uni.getStorageSync('userRole')
    this.userRole = 1
    this.getData()
  },
  methods: {
    clearFiles() {
      this.path = []
    },
    previews(item) {
      uni.previewImage({
        urls: item
      })
    },
    uploadFiles() {
      let that = this
      uni.chooseImage({
        count: 9, //默认9
        sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
        // sourceType: ['album'], //从相册选择
        name: '',
        success: function (res) {
          res.tempFilePaths.map((i) => {
            that.path.push(i)
          })

        }
      });
    },
    bindTimeChange(e, id) {
      if (id === 1) {
        this.time1 = e.target.value;
      }
      if (id === 2) {
        this.time2 = e.target.value;
      }
      if (id === 3) {
        this.time3 = e.target.value;
      }
      if (id === 4) {
        this.time4 = e.target.value;
      }
    },
    // 返回"我的"主页面
    backMyPage() {
      uni.navigateBack({
        url: '/pages/home/<USER>'
      });
    },

    async getData(type) {
      let homeParam = {
        flightSortiesId: this.flightSortiesId
      };
      try {
        const res = await getFlight(homeParam);
        // this.taskData = res.response.data.flightplanList;
        this.taskData = [res.response.data]

      } catch (e) {
        console.error(e)
      }
    },

    //提交答题
    async submit(type) {
      let that = this
      let param = this.form;
      param.flightSortiesId = this.flightSortiesId
      if (that.path.length == 0) {
        param.file = ''
        uni.request({
          url: api.baseUrl + '/wechat/ocDirectPreparation/importOcDirectPreparation2',
          method: 'POST',
          header: {
            'content-type': 'application/x-www-form-urlencoded;charset=UTF-8',
            'AuthCode': uni.getStorageSync('token'),
            'AuthID': uni.getStorageSync('userInfo').userId
          },
          data: param,
          success: (res) => {
            uni.showToast({
              title: res.data.msg
            })
            setTimeout(() => {
              uni.navigateBack({
                delta: 1,
              });
            }, 1500)
          }, error: (error) => {
            uni.showToast({
              title: '上传失败，请联系管理员！'
            })
          }
        });
      } else {
        param.file = that.path
        if (that.path.length == 1) {
          uni.uploadFile({
            url: baseUrl + '/wechat/ocDirectPreparation/importOcDirectPreparation1',
            filePath: that.path[0],
            formData: param,
            name: 'file',
            header: {
              AuthCode: uni.getStorageSync('token'),
              AuthID: uni.getStorageSync('userInfo').userId
            },
            async success(res) {
              res = JSON.parse(res.data)
              console.log(res)
              uni.showToast({
                title: res.msg
              })
              setTimeout(() => {
                uni.navigateBack({
                  delta: 1,
                });
              }, 1500)

            }, error(error) {
              uni.showToast({
                title: '上传失败'
              })
            }
          })
        } else {
          uni.uploadFile({
            url: baseUrl + '/wechat/ocDirectPreparation/importOcDirectPreparation1',
            filePath: that.path[0],
            formData: param,
            name: 'file',
            header: {
              AuthCode: uni.getStorageSync('token'),
              AuthID: uni.getStorageSync('userInfo').userId
            },
            async success(res) {
              res = JSON.parse(res.data)
              console.log(res)
              var idFile = res.data.id
              that.path.map((i, o) => {
                if (o != 0) {
                  console.log({id: idFile, file: i})
                  uni.uploadFile({
                    url: baseUrl + '/wechat/ocDirectPreparation/importOcDirectPreparation1change',
                    filePath: i,
                    formData: {id: idFile, file: i},
                    name: 'file',
                    header: {
                      AuthCode: uni.getStorageSync('token'),
                      AuthID: uni.getStorageSync('userInfo').userId
                    },
                    success(respond) {
                      respond = JSON.parse(respond.data)
                      if (o == that.path.length - 1) {
                        uni.showToast({
                          title: respond.msg
                        })
                        setTimeout(() => {
                          uni.navigateBack({
                            delta: 1,
                          });
                        }, 1500)
                      }
                    }, error(error) {
                      uni.showToast({
                        title: '上传失败'
                      })
                    }
                  })
                }
              })

            }, error(error) {
              uni.showToast({
                title: '上传失败'
              })
            }
          })
        }


      }


    }
  }
};
</script>

<style lang="less" scoped>

page {
  height: 100%;
}

.background {
  width: 100%;
  height: 100%;
  // position: fixed;
  background-size: 100% 100%;
  z-index: -1;
  position: absolute;
  top: 0px;
  bottom: 0px;
}

.nav {
  width: 100%;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
  z-index: 10;
}

.nav-title {
  width: 100%;
  height: inherit;
  position: relative;
  z-index: 10;
  font-family: SF Pro Text;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;

  .nav-title-text {
    // padding-top: 10px;
    font-family: SF Pro Text;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #000000;

    &:first-child {
      font-weight: normal;
      position: absolute;
      left: 25px;
      font-size: 22px;
    }
  }
}

.uni-column {
  background: #ffffff86;
  line-height: 30px;
  padding: 10px 20px;
  font-size: 13px;

}

.content {
  padding: 10px;
}

textarea {
  border: 1px solid #efefef;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
}
</style>
