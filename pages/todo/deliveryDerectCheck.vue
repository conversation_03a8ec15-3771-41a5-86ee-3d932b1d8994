<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <view class="nav bg-white" :style="'height:' + navH + 'px'">
      <view class="nav-title">
        <text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
        <text class="nav-title-text">责任运控直接准备</text>
      </view>
    </view>
    <!-- 内容主体 -->
    <view class="content" :style="'height:calc(100vh - ' + navH + 'px)'" style="overflow: auto;">
      <task-card v-for="(item, index) in taskData" :key="index" :taskIndex="index"
                 :taskInfo="item" :types="1" :show-option="false">
      </task-card>
      <form @submit="submit">


        <!-- 运控直接准备阶段 -->
        <view class="" v-for="(item,index) in meslist" :key='index' style="margin-bottom:10px">

          <view class="uni-form-item uni-column">
            <view class="title">用户名:{{ item.fillInUserName }}</view>
            <view class="title">QNH:</view>
            <view class="uni-textarea">
              <textarea :disabled='true' maxlength="800" v-model='item.qnh' placeholder="请填写QNH"
                        placeholder-style='font-size:12px'/>
            </view>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">天气资料:</view>
            <view class="uni-textarea">
              <textarea :disabled='true' maxlength="800" v-model='item.weatherData' placeholder="请填写天气资料"
                        placeholder-style='font-size:12px'/>
            </view>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">航路航线:</view>
            <view class="uni-textarea">
              <textarea :disabled='true' maxlength="800" v-model='item.airRoute' placeholder="请填写航路航线"
                        placeholder-style='font-size:12px'/>
            </view>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">放行提示:</view>
            <view class="uni-textarea">
              <textarea :disabled='true' maxlength="800" v-model='item.releasePrompt' placeholder="请填写运行提示内容"
                        placeholder-style='font-size:12px'/>
            </view>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">运行提示内容:</view>
            <view class="uni-textarea">
              <textarea :disabled='true' maxlength="800" v-model='item.operationPromptContent'
                        placeholder="请填写运行提示内容" placeholder-style='font-size:12px'/>
            </view>
          </view>
          <view class="uni-form-item uni-column" v-if="item.manifestUploadFileUrl!=null">
            <view class="title">舱单:</view>
            <view class="" style="display: flex;">
              <image style="height: 100px;" v-for="(itemx,indexx) in item.manifestUploadFileUrl" :key='indexx'
                     @click='previews(item.manifestUploadFileUrl)'
                     :src="itemx" mode=""></image>
            </view>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">填写时间:{{ item.fillingTime }}</view>
          </view>

        </view>

      </form>
      <view class="no-data-box" v-if="noData">
        <image class="image" src="../../static/images/nodata.png" mode="widthFix"></image>
        <text class="col-gray-500">当前无数据</text>
      </view>
    </view>

    <!-- 整体背景放在最后 -->
    <view class="bg-main">
      <image class="background" src="../../static/images/background.png"></image>
    </view>
  </view>
</template>

<script>
import {getFlight, ocDirectPreparation, ocDirectPreparationOnly} from '../../api/weChat.js';

const App = getApp();
export default {
  data() {
    return {
      time1: '1213',
      time2: '',
      navH: 0,
      timer: '',
      taskIndex: 1,
      inputValue: '',
      taskInfo: '',
      componentData: '',
      flightSortiesId: '',
      imgUrl: '',
      state: '',
      userRole: '',
      form: {},
      meslist: [],
      noData: false,
      taskData: []
    };
  },
  mounted() {
    this.getData()
    this.getData2()
  },
  created() {
    // this.flightSortiesId = uni.getStorageSync('flightSortiesId');
    this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
  },
  onLoad: function (options) {
    this.flightSortiesId = options.flightSortiesId
    // this.userRole = uni.getStorageSync('userRole')
    this.userRole = 1
  },
  methods: {
    previews(item) {
      uni.previewImage({
        urls: item
      })
    },
    bindTimeChange(e, id) {
      if (id === 1) {
        this.time1 = e.target.value;
      }
      if (id === 2) {
        this.time2 = e.target.value;
      }
      if (id === 3) {
        this.time3 = e.target.value;
      }
      if (id === 4) {
        this.time4 = e.target.value;
      }
    },
    // 返回"我的"主页面
    backMyPage() {
      uni.navigateBack({
        url: '/pages/home/<USER>'
      });
    },
    async getData2(type) {
      let homeParam = {
        flightSortiesId: this.flightSortiesId
      };
      try {
        const res = await getFlight(homeParam);
        // this.taskData = res.response.data.flightplanList;
        this.taskData = [res.response.data]

      } catch (e) {
        console.error(e)
      }
      console.log(this.flag)
    },
    async getData() {
      let param = {
        flightSortiesId: this.flightSortiesId,
      };
      try {
        const res = await ocDirectPreparationOnly(param);
        this.meslist = res.response.data;
        if (this.meslist.length == 0) {
          this.noData = true
        } else {
          this.noData = false;
        }
      } catch (e) {
        console.error(e)
      }
    },

    clearRmk() {
      this.inputValue = '';
    },
    //提交答题
    async submit(type) {

      let param = this.form;
      param.flightSortiesId = this.flightSortiesId

      try {
        const res = await ocDirectPreparation(param);
        uni.showToast({
          title: res.response.msg
        })
        setTimeout(() => {
          uni.navigateBack({
            delta: 1,
          });
        }, 1500)
      } catch (e) {
        console.error(e)
      }
    }
  }
};
</script>

<style lang="less" scoped>

page {
  height: 100%;
}

.background {
  width: 100%;
  height: 100%;
  // position: fixed;
  background-size: 100% 100%;
  z-index: -1;
  position: absolute;
  top: 0px;
  bottom: 0px;
}

.no-data-box {
  background: rgba(255, 255, 255, 0.64);
  height: 343px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .image {
    width: 140px;
    height: 140px;
  }

  .col-gray-500 {
    font-size: 14px;
  }
}

.scroll-view {
  height: calc(100vh - 135px) !important;
}

.nav {
  width: 100%;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
  z-index: 10;
}

.nav-title {
  width: 100%;
  height: inherit;
  position: relative;
  z-index: 10;
  font-family: SF Pro Text;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;

  .nav-title-text {
    // padding-top: 10px;
    font-family: SF Pro Text;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #000000;

    &:first-child {
      font-weight: normal;
      position: absolute;
      left: 25px;
      font-size: 22px;
    }
  }
}

.uni-column {
  background: #ffffff86;
  line-height: 30px;
  padding: 10px 20px;
  font-size: 13px;

}

.content {
  padding: 10px;
}

textarea {
  border: 1px solid #efefef;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
}
</style>
