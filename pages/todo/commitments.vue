<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <view class="nav bg-white" :style="'height:' + navH + 'px'">
      <view class="nav-title">
        <text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
        <text class="nav-title-text">副驾驶填写飞行时间</text>
      </view>
    </view>
    <!-- 内容主体 -->
    <view class="content" :style="'height:calc(100vh - ' + navH + 'px)'" style="overflow: auto;">
      <form @submit="submit">

        <!-- 副驾驶上传任务书阶段 -->
        <view class="">
          <!-- <view class="uni-form-item uni-column">
            <view class="title">上传任务书:</view>
            <button type="default" size="mini" @click='uploadFiles' style="margin-right: 5px;">上传图片</button>
            <button type="default" size="mini" @click='clearFiles'>清空图片</button>
            <view class="" style="display: flex;">
              <image style="height: 100px;" v-for="(item,index) in path" :key='index' @click='previews(path)'
                v-show="path.length>0" :src="item" mode=""></image>
            </view>
          </view> -->
          <view class="">
            <view class="uni-form-item uni-column">
              <view class="title">飞行架次</view>
              <view class="uni-textarea">
                <input class="inputNum-input" type="number" v-model='form.flightFrequency' placeholder="请填写飞行架次"
                       placeholder-style='font-size:12px'/>
              </view>
            </view>
          </view>
          <view class="" v-show='form.routeType==1'
                style="text-align: center;font-size: 15px;font-weight: 700;margin: 20px 0;">
            去程信息
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">滑行开始日期</view>
            <picker class="time-picker" mode="date" :value="form.taxiStartTimeDate" :start="currentTime"
                    @change="bindTimeChange($event, 5)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.taxiStartTimeDate }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">滑行开始时间</view>
            <picker class="time-picker" mode="time" :value="form.taxiStartTime1" start="09:00" end="08:59"
                    @change="bindTimeChange($event, 1)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.taxiStartTime1 }}</view>
            </picker>
          </view>

          <view class="uni-form-item uni-column">
            <view class="title">起飞日期</view>
            <picker class="time-picker" mode="date" :value="form.departureTimeDate" :start="currentTime"
                    @change="bindTimeChange($event, 6)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.departureTimeDate }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">起飞时间</view>
            <picker class="time-picker" mode="time" :value="form.departureTime1" start="09:00" end="08:59"
                    @change="bindTimeChange($event, 2)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.departureTime1 }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">着陆日期</view>
            <picker class="time-picker" mode="date" :value="form.landingTimeDate" :start="currentTime"
                    @change="bindTimeChange($event, 7)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.landingTimeDate }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">着陆时间</view>
            <picker class="time-picker" mode="time" :value="form.landingTime1" start="09:00" end="08:59"
                    @change="bindTimeChange($event, 3)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.landingTime1 }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">滑行结束日期</view>
            <picker class="time-picker" mode="date" :value="form.shutdownTimeDate" :start="currentTime"
                    @change="bindTimeChange($event, 8)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.shutdownTimeDate }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">滑行结束时间</view>
            <picker class="time-picker" mode="time" :value="form.shutdownTime1" start="09:00" end="08:59"
                    @change="bindTimeChange($event, 4)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.shutdownTime1 }}</view>
            </picker>
          </view>
          <!-- <view class="uni-form-item uni-column">
            <view class="title">关车日期:</view>
            <picker class="time-picker" mode="date" :value="form.shutdownTimeDate" :start="currentTime"
             @change="bindTimeChange($event, 8)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.shutdownTimeDate }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">关车时间:</view>
            <picker class="time-picker" mode="time" :value="form.shutdownTime1" start="09:00" end="08:59" @change="bindTimeChange($event, 4)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.shutdownTime1 }}</view>
            </picker>
          </view> -->
          <view class="uni-form-item uni-column">
            <view class="title">夜航时间</view>
            <picker class="time-picker" mode="time" :value="form.nightFlyTime" start="09:00" end="08:59"
                    @change="bindTimeChange($event, 13)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.nightFlyTime }}</view>
            </picker>
          </view>
          <view class="">
            <view class="uni-form-item uni-column">
              <view class="title">剩余燃油</view>
              <view class="uni-textarea">

                <input class="inputNum-input" type="number" v-model='form.fuelExpend' placeholder="请填写剩余燃油"
                       placeholder-style='font-size:12px'/>
              </view>
            </view>
          </view>
          <view class="" v-if="form.flightPurpose == '短途运输'">
            <view class="uni-form-item uni-column">
              <view class="title">旅客人数</view>
              <view class="uni-textarea">
                <input class="inputNum-input" type="number" v-model='form.passengerNumber' placeholder="请填写旅客人数"
                       placeholder-style='font-size:12px'/>
              </view>
            </view>
          </view>
        </view>

        <view class="" v-show="form.routeType==1">
          <view class="" style="text-align: center;font-size: 15px;font-weight: 700;margin: 20px 0;">
            返程信息
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">返程滑行开始日期</view>
            <picker class="time-picker" mode="date" :value="form.secondSlideStartTime2" :start="currentTime"
                    @change="bindTimeChange($event, 55)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.secondSlideStartTime2 }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">返程滑行开始时间</view>
            <picker class="time-picker" mode="time" :value="form.secondSlideStartTime1" start="09:00" end="08:59"
                    @change="bindTimeChange($event, 11)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.secondSlideStartTime1 }}</view>
            </picker>
          </view>

          <view class="uni-form-item uni-column">
            <view class="title">返程起飞日期</view>
            <picker class="time-picker" mode="date" :value="form.secondFlyStartTime1" :start="currentTime"
                    @change="bindTimeChange($event, 66)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.secondFlyStartTime1 }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">返程起飞时间</view>
            <picker class="time-picker" mode="time" :value="form.secondFlyStartTime2" start="09:00" end="08:59"
                    @change="bindTimeChange($event, 22)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.secondFlyStartTime2 }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">返程着陆日期</view>
            <picker class="time-picker" mode="date" :value="form.secondFlyEndTime1" :start="currentTime"
                    @change="bindTimeChange($event, 77)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.secondFlyEndTime1 }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">返程着陆时间</view>
            <picker class="time-picker" mode="time" :value="form.secondFlyEndTime2" start="09:00" end="08:59"
                    @change="bindTimeChange($event, 33)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.secondFlyEndTime2 }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">返程滑行结束日期</view>
            <picker class="time-picker" mode="date" :value="form.secondSlideEndTime1" :start="currentTime"
                    @change="bindTimeChange($event, 88)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.secondSlideEndTime1 }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">返程滑行结束时间</view>
            <picker class="time-picker" mode="time" :value="form.secondSlideEndTime2" start="09:00" end="08:59"
                    @change="bindTimeChange($event, 44)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.secondSlideEndTime2 }}</view>
            </picker>
          </view>
          <!-- <view class="uni-form-item uni-column">
            <view class="title">关车日期:</view>
            <picker class="time-picker" mode="date" :value="form.shutdownTimeDate" :start="currentTime"
             @change="bindTimeChange($event, 8)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.shutdownTimeDate }}</view>
            </picker>
          </view>
          <view class="uni-form-item uni-column">
            <view class="title">关车时间:</view>
            <picker class="time-picker" mode="time" :value="form.shutdownTime1" start="09:00" end="08:59" @change="bindTimeChange($event, 4)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.shutdownTime1 }}</view>
            </picker>
          </view> -->
          <view class="uni-form-item uni-column">
            <view class="title">返程夜航时间</view>
            <picker class="time-picker" mode="time" :value="form.secondNightFlyTime" start="09:00" end="08:59"
                    @change="bindTimeChange($event, 133)">
              <text class="iconfont icon-calendar-todo-fill"></text>
              <view class="time" style="display: inline;">{{ form.secondNightFlyTime }}</view>
            </picker>
          </view>
          <view class="">
            <view class="uni-form-item uni-column">
              <view class="title">返程剩余燃油</view>
              <view class="uni-textarea">

                <input class="inputNum-input" type="number" v-model='form.secondFuelExpend' placeholder="请填写剩余燃油"
                       placeholder-style='font-size:12px'/>
              </view>
            </view>
          </view>
          <view class="" v-if="form.flightPurpose == '短途运输'">
            <view class="uni-form-item uni-column">
              <view class="title">返程旅客人数</view>
              <view class="uni-textarea">
                <input class="inputNum-input" type="number" v-model='form.secondPassengerNumber'
                       placeholder="请填写旅客人数" placeholder-style='font-size:12px'/>
              </view>
            </view>
          </view>
        </view>
        <view class="uni-btn-v">
          <button type="primary" plain="true" form-type="submit" style="margin-bottom: 10px;">提交</button>
          <!-- <button form-type="submit">Submit</button> -->
          <button type="default" @click='resetForm'>清空</button>
        </view>
      </form>
    </view>
    <!-- 整体背景放在最后 -->
    <view class="bg-main">
      <image class="background" src="../../static/images/background.png"></image>
    </view>
  </view>
</template>

<script>
import {
  getFlightPlanSingle,
  getFlightDetailById,
  addFlyMoment,
  getVerifySure,
  getHomeItemData,
  flightSortiesSynBook
} from '../../api/weChat.js';

const App = getApp();
export default {
  data() {
    return {

      navH: 0,
      timer: '',
      taskIndex: 1,
      inputValue: '',
      taskInfo: '',
      componentData: '',
      flightSortiesId: '',
      imgUrl: '',
      state: '',
      userRole: '',
      form: {
        taxiStartTime1: '',
        departureTime1: '',
        landingTime1: '',
        shutdownTime1: '',
        taxiStartTimeDate: '',
        departureTimeDate: '',
        landingTimeDate: '',
        shutdownTimeDate: '',
        nightFlyTime: '00:00',
        secondSlideStartTime1: '',
        secondSlideStartTime2: '',
        secondFlyStartTime1: '',
        secondFlyStartTime2: '',
        secondFlyEndTime1: '',
        secondFlyEndTime2: '',
        secondSlideEndTime1: '',
        secondSlideEndTime2: '',
        secondNightFlyTime: '00:00',
        routeType: 0,
      },
      currentTime: '',
      path: [],
      flightplanId: ''
    };
  },
  mounted() {
    console.log(this.currentTime)
    this.getCurrentTime()
    this.getData()
    this.form.taxiStartTimeDate = this.currentTime
    this.form.departureTimeDate = this.currentTime
    this.form.landingTimeDate = this.currentTime
    this.form.shutdownTimeDate = this.currentTime
    this.form.secondFlyStartTime1 = this.currentTime
    this.form.secondFlyEndTime1 = this.currentTime
    this.form.secondSlideEndTime1 = this.currentTime
    this.form.secondSlideStartTime2 = this.currentTime

  },
  onLoad: function (options) {
    this.state = options.state;
    console.log(options)
    this.flightplanId = options.flightplanId;
    this.flightSortiesId = options.flightplanId;

    console.log(options)
    this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
    // this.userRole = uni.getStorageSync('userRole')
    this.userRole = 1


  },
  methods: {
    flyFit(param) {
      this.form[param] = this.form[param] == 0 ? 1 : 0
      this.$forceUpdate()
    },
    resetForm() {
      this.form = {
        taxiStartTime1: '',
        departureTime1: '',
        landingTime1: '',
        shutdownTime1: '',
        taxiStartTimeDate: '',
        departureTimeDate: '',
        landingTimeDate: '',
        shutdownTimeDate: '',
        nightFlyTime: '',
        secondSlideStartTime1: '',
        secondSlideStartTime2: '',
        secondFlyStartTime1: '',
        secondFlyStartTime2: '',
        secondFlyEndTime1: '',
        secondFlyEndTime2: '',
        secondSlideEndTime1: '',
        secondSlideEndTime2: '',
        secondNightFlyTime: '',
        routeType: 0,


      }
    },
    clearFiles() {
      this.path = []
    },
    getCurrentTime() {
      var myDate = new Date();
      var y = myDate.getFullYear()
      var m = myDate.getMonth() + 1
      m = m < 10 ? '0' + m : m
      var d = myDate.getDate()
      d = d < 10 ? ('0' + d) : d
      var week = myDate.getDay()
      var x;
      switch (week) {
        case 0:
          x = '周日';
          break;
        case 1:
          x = '周一';
          break;
        case 2:
          x = '周二';
          break;
        case 3:
          x = '周三';
          break;
        case 4:
          x = '周四';
          break;
        case 5:
          x = '周五';
          break;
        case 6:
          x = '周六';
          break;
      }
      this.currentTime = y + '-' + m + '-' + d
    },
    uploadFiles() {
      let that = this
      uni.chooseImage({
        count: 9, //默认9
        sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
        // sourceType: ['album'], //从相册选择
        name: '',
        success: function (res) {
          console.log(res.tempFilePaths)
          res.tempFilePaths.map((i) => {
            that.path.push(i)
          })

        }
      });
    },
    bindTimeChange(e, id) {
      console.log(e.target.value, id)
      if (id == 1) {
        this.form.taxiStartTime1 = e.target.value;
        console.log(this.form.taxiStartTime)
      }
      if (id == 2) {
        this.form.departureTime1 = e.target.value;
      }
      if (id == 3) {
        this.form.landingTime1 = e.target.value;
      }
      if (id == 4) {
        this.form.shutdownTime1 = e.target.value;
      }
      if (id == 5) {
        this.form.taxiStartTimeDate = e.target.value;
      }
      if (id == 6) {
        this.form.departureTimeDate = e.target.value;
      }
      if (id == 7) {
        this.form.landingTimeDate = e.target.value;
      }
      if (id == 8) {
        this.form.shutdownTimeDate = e.target.value;
      }
      if (id == 13) {
        this.form.nightFlyTime = e.target.value;
      }

      if (id == 11) {
        this.form.secondSlideStartTime1 = e.target.value;
      }
      if (id == 55) {
        this.form.secondSlideStartTime2 = e.target.value;
      }
      if (id == 66) {
        this.form.secondFlyStartTime1 = e.target.value;
      }
      if (id == 22) {
        this.form.secondFlyStartTime2 = e.target.value;
      }
      if (id == 77) {
        this.form.secondFlyEndTime1 = e.target.value;
      }
      if (id == 33) {
        this.form.secondFlyEndTime2 = e.target.value;
      }
      if (id == 88) {
        this.form.secondSlideEndTime1 = e.target.value;
      }
      if (id == 44) {
        this.form.secondSlideEndTime2 = e.target.value;
      }
      if (id == 133) {
        this.form.secondNightFlyTime = e.target.value;
      }


    },
    // 返回"我的"主页面
    backMyPage() {
      uni.navigateBack({
        url: '/pages/home/<USER>'
      });
    },

    async getData() {
      let homeParam = {
        flightPlanId: this.flightSortiesId
      };
      try {
        const res = await getFlightPlanSingle(homeParam);
        this.form.routeType = res.response.data.routeType
        this.form.flightPurpose = res.response.data.flightPurpose

      } catch (e) {
        console.error(e)
      }
    },
    previews(item) {
      uni.previewImage({
        urls: item
      })
    },
    //提交答题
    async submit(type) {
      let that = this
      let param = {}
      param = this.form
      param.flightPlanId = this.flightplanId
      param.slideStartTime = this.form.taxiStartTimeDate + ' ' + this.form.taxiStartTime1
      param.slideEndTime = this.form.shutdownTimeDate + ' ' + this.form.shutdownTime1
      param.flyStartTime = this.form.departureTimeDate + ' ' + this.form.departureTime1
      param.flyEndTime = this.form.landingTimeDate + ' ' + this.form.landingTime1

      param.secondSlideStartTime1 = param.secondSlideStartTime1 == '' ? '00:00' : param.secondSlideStartTime1
      param.secondFlyStartTime2 = param.secondFlyStartTime2 == '' ? '00:00' : param.secondFlyStartTime2
      param.secondFlyEndTime2 = param.secondFlyEndTime2 == '' ? '00:00' : param.secondFlyEndTime2
      param.secondSlideEndTime2 = param.secondSlideEndTime2 == '' ? '00:00' : param.secondSlideEndTime2

      param.secondSlideStartTime = param.secondSlideStartTime2 + ' ' + param.secondSlideStartTime1
      param.secondFlyStartTime = param.secondFlyStartTime1 + ' ' + param.secondFlyStartTime2
      param.secondFlyEndTime = param.secondFlyEndTime1 + ' ' + param.secondFlyEndTime2
      param.secondSlideEndTime = param.secondSlideEndTime1 + ' ' + param.secondSlideEndTime2


      try {
        const res = await addFlyMoment(param);
        uni.showToast({
          title: res.response.msg
        })
        setTimeout(() => {
          uni.navigateTo({
            url: '/pages/home/<USER>'
          });
        }, 1500)
      } catch (e) {
        console.error(e)
      }

      // let formData = { 'file':this.path,'flightSortiesId':this.flightSortiesId}
      // console.log(formData)
      // if(that.path.length == []){
      // 	uni.showToast({
      // 		title: '请上传任务书！'
      // 	})
      // }
      // const baseUrl = 'https://ga.swcares.com.cn/trade/oc/wechat'
      // uni.uploadFile({
      // 	url:baseUrl+'/wechat/flightDetail/importAssignmentFile1',
      // 	filePath:that.path[0],
      // 	formData:param,
      // 	name:'file',
      // 	header:{
      // 		AuthCode:uni.getStorageSync('token'),
      // 		AuthID:uni.getStorageSync('userInfo').userId
      // 	},
      // 	async success(res){
      // 		res = JSON.parse(res.data)
      // 		console.log(res)
      // 		if(that.path.length == 1){
      // 			uni.showToast({
      // 				title: res.msg
      // 			})
      // 			setTimeout(()=>{
      // 				uni.navigateBack({
      // 				    delta: 1,
      // 				});
      // 			},1500)
      // 		}else{
      // 			var idFile = res.data.id
      // 			that.path.map((i,o)=>{
      // 				if(o!=0){
      // 					uni.uploadFile({
      // 						url:baseUrl+'/wechat/flightDetail/importAssignmentFile1change',
      // 						filePath:i,
      // 						formData:{flightSortiesId:that.flightSortiesId,file:i},
      // 						name:'file',
      // 						header:{
      // 							AuthCode:uni.getStorageSync('token'),
      // 							AuthID:uni.getStorageSync('userInfo').userId
      // 						},
      // 						success(respond){
      // 							respond = JSON.parse(respond.data)
      // 							if(o == that.path.length-1){
      // 								uni.showToast({
      // 									title: respond.msg
      // 								})
      // 								setTimeout(()=>{
      // 									uni.navigateBack({
      // 									    delta: 1,
      // 									});
      // 								},1500)
      // 							}
      // 						},error(error){
      // 							uni.showToast({
      // 								title: '上传失败'
      // 							})
      // 						}
      // 					})
      // 				}
      // 			})
      // 		}

      // 	},error(error){
      // 		uni.showToast({
      // 			title: '上传失败'
      // 		})
      // 	}
      // })


      // uni.uploadFile({
      // 	url:'https://ga.swcares.com.cn/trade/oc/wechat/wechat/flightDetail/importAssignmentFile',
      // 	filePath:that.path,
      // 	formData:formData,
      // 	name:'file',
      // 	header:{
      // 		AuthCode:uni.getStorageSync('token'),
      // 		AuthID:uni.getStorageSync('userInfo').userId
      // 	},
      // 	async success(responce){
      // 		// param.id= JSON.parse(responce.data).data.id
      // 		const res =await flightSortiesSynBook(param)
      // 		uni.showToast({
      // 			title: res.response.msg
      // 		})
      // 		setTimeout(()=>{
      // 			uni.navigateBack({
      // 			    delta: 1,
      // 			});
      // 		},1500)
      // 	},error(error){
      // 		console.log(error)
      // 	}
      // })

    }
  }
};
</script>

<style lang="less" scoped>

page {
  height: 100%;
}

.background {
  width: 100%;
  height: 100%;
  // position: fixed;
  background-size: 100% 100%;
  z-index: -1;
  position: absolute;
  top: 0px;
  bottom: 0px;
}

.nav {
  width: 100%;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
  z-index: 10;
}

.nav-title {
  width: 100%;
  height: inherit;
  position: relative;
  z-index: 10;
  font-family: SF Pro Text;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;

  .nav-title-text {
    // padding-top: 10px;
    font-family: SF Pro Text;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #000000;

    &:first-child {
      font-weight: normal;
      position: absolute;
      left: 25px;
      font-size: 22px;
    }
  }
}

.uni-column {
  background: #ffffff86;
  line-height: 30px;
  padding: 10px 20px;
  font-size: 13px;

}

.content {
  padding: 10px;
}

textarea {
  border: 1px solid #efefef;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
}
</style>
