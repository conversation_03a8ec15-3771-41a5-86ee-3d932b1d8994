<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="backMyPage"></text>
				<text class="nav-title-text">飞行部审批</text>
			</view>
		</view>
		<!-- 内容主体 -->
		<view class="content" :style="'height:calc(100vh - ' + navH + 'px)'" style="overflow: auto;">
			<task-card v-for="(item, index) in taskData" :key="index" :taskIndex="index" :taskInfo="item" :types='1' :flight='1'>
			</task-card> 
			<message-list v-for="(item, index) in dateList" :key="index" :taskIndex="index"
				:taskInfo="item" >
			</message-list>
			
			<view v-for="(item,index) in list" style="border-top:5px solid #eee">
				<view class="uni-form-item uni-column">
					<view class="title">姓名:{{item.fillInUserName}}</view>
					
				</view>
				
				<view class="uni-form-item uni-column">
					<view class="title">着装、反光背心、矫正视力眼镜等是否准备充分:</view>
					<view class="">
						{{item.readyStatus == 1 ? '是' : '否'}}
					</view>
				</view>
				<view class="uni-form-item uni-column">
					<view class="title">身体状况:</view>
					<view class="uni-textarea">
						<textarea :disabled='true' maxlength="800" v-model='item.prePhysicalCondition'  placeholder="请填写身体状况" placeholder-style='font-size:12px'/>
					</view>
				</view>
				<view class="uni-form-item uni-column" v-if="item.roleType==0">
					<view class="title">备降机场（天气、程序、特点）:</view>
					<view class="uni-textarea">
						<textarea :disabled='true' maxlength="800"  v-model='item.alternateAirport' placeholder="请填写备降机场（天气、程序、特点）" placeholder-style='font-size:12px'/>
					</view>
				</view>
				<view class="uni-form-item uni-column" v-if="item.roleType==0">
					<view class="title">航线、机场（飞行特点、注意事项）:</view>
					<view class="uni-textarea">
						<textarea :disabled='true' maxlength="800" v-model='item.routeAndAirport' placeholder="请填写航线、机场（飞行特点、注意事项）" placeholder-style='font-size:12px'/>
					</view>
				</view>
				<view class="uni-form-item uni-column" v-if="item.roleType==0">
					<view class="title">特情处置:</view>
					<view class="uni-textarea">
						<textarea :disabled='true' maxlength="800" v-model='item.specialDisposition' placeholder="请填写特情处置" placeholder-style='font-size:12px'/>
					</view>
				</view>
			</view>
			<view class="">
				
			</view>
			<view class="uni-btn-v" v-if="type==111">
				<button type="primary" plain="true" @click="submit(1)" style="margin-bottom: 10px;">通过</button>
				<!-- <button form-type="submit">Submit</button> -->
				<button type="default" @click="submit(2)">拒绝</button>
			</view>
			
		</view>
		<!-- 整体背景放在最后 -->
		<view class="bg-main"><image class="background" src="../../static/images/background.png"></image></view>
	</view>
</template>

<script>
	import { getUserAprrol,getFlightPlanSingle,updateFlightPlan,emergencyList,prepareForFlightplanAll} from '../../api/weChat.js';

const App = getApp();
export default {
	data() {
		return {
			navH: 0,
			timer: '',
			taskIndex: 1,
			inputValue: '',
			taskInfo: '',
			componentData: '',
			flightSortiesId:'',
			imgUrl:'',
			form:{},
			list:[],
			taskData:[],
			dateList:[],
			type:111
		};
	},
	mounted() {
		this.getData()
		this.getData2()
		this.getData3()
	},
	onLoad: function(options) {
		this.flightplanId = options.flightplanId;
		this.flightSortiesId = options.flightSortiesId;
		
		this.type = options.type;
		
		this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
	},
	methods: {
		backStatus(type){
			if(type == 0){
				return '未审批'
			}else if(type==1){
				return '同意'
			}else if(type ==2){
				return '拒绝'
			}
		},
		async getData3(type) {
		    let homeParam = {
		        flightSortiesId : this.flightSortiesId,
		    };
			console.log(homeParam)
		    try {
		        const res = await getUserAprrol(homeParam);
		        // this.taskData = res.response.data.flightplanList;
		        this.dateList = res.response.data
		        
		    } catch (e) {
		        console.error(e)
		    }
		},
		goGiven(){
			uni.navigateTo({
				url: '/pages/todo/givenFlight?flightplanId='+this.flightplanId
			});
		},
		// 返回"我的"主页面
		backMyPage() {
			uni.navigateBack({
				 url: '/pages/home/<USER>'
			});
		},
		async getData2(type) {
			let homeParam = {
				flightplanId :this.flightplanId,
				flightSortiesId : this.flightSortiesId,
				
			};
			try {
				const res = await getFlightPlanSingle(homeParam);
				// this.taskData = res.response.data.flightplanList;
				this.taskData = [res.response.data]
				
			} catch (e) {
				console.error(e)
			}
			console.log(this.flag)
		},
		async getData() {
			let param = {
				flightSortiesId: this.flightSortiesId,
			};
			try {
				const res = await prepareForFlightplanAll(param);
				console.log(res.response.data)
				this.list = res.response.data;
			} catch (e) {
				console.error(e)
			}
		},
		
		//提交答题
		async submit(type) {
			console.log(111)
			let param = {
				preApprovalStatus:type,
				flightSortiesId:this.flightSortiesId
			};
			uni.showModal({
				title: '确认操作？',
				content: '',
				 success:async function(resp) {
					if (resp.confirm) {
						try {
							const res = await updateFlightPlan(param);
							uni.showToast({
								title: res.response.msg
							})
							setTimeout(()=>{
								uni.navigateBack({
								    delta: 1,
								});
							},1500)
						} catch (e) {
							console.error(e)
						}
					} else if (resp.cancel) {
						console.log('用户点击取消');
					}
				}
			});
			
		}
	}
};
</script>

<style lang="less" scoped>

page {
	height: 100%;
}

.background {
	width: 100%;
	height: 100%;
	// position: fixed;
	background-size: 100% 100%;
	z-index: -1;
	position: absolute;
	top: 0px;
	bottom: 0px;
}

.nav {
	width: 100%;
	overflow: hidden;
	position: relative;
	top: 0;
	left: 0;
	z-index: 10;
}

.nav-title {
	width: 100%;
	height: inherit;
	position: relative;
	z-index: 10;
	font-family: SF Pro Text;
	font-style: normal;
	font-weight: 600;
	font-size: 16px;
	color: #000000;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 15px;

	.nav-title-text {
		// padding-top: 10px;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		color: #000000;

		&:first-child {
			font-weight: normal;
			position: absolute;
			left: 25px;
			font-size: 22px;
		}
	}
}

.uni-column{
	    background: #ffffff86;
		line-height: 30px;
		padding: 10px 20px;
		font-size: 13px;

}
.content{
	padding: 10px;
}
textarea{
	    border: 1px solid #efefef;
	    width: 100%;
	    padding: 10px;
	    box-sizing: border-box;
}
</style>
