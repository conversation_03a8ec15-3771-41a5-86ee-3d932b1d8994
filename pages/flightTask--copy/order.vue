<template>
  <view>
    <!-- 自定义导航栏 -->
    <CustomerNav title="承运订单" custom-go-back @onBack="onBack" />
    <view class="order-container">
      <view class="filter">
        <view class="section-title">筛选</view>
        <FormItem label="计划时间">
          <view>
            <view class="btn-groups">
              <view
                class="custom-tag"
                v-for="item in dateOptionList"
                :class="selectedDateType === item.value ? 'active' : ''"
                :key="item.value"
                @click="selectDate(item)"
              >
                {{ item.text }}
              </view>
            </view>

            <view class="input-box-line" v-if="selectedDateType === '其他日期'">
              <input
                v-model="selectedDate"
                placeholder="请选择"
                disabled
                @click="datePickerShow = true"
              />
              <van-icon name="arrow-down" class="right-icon" />
            </view>
          </view>
        </FormItem>
        <FormItem label="起飞基地" v-if="tabActive === '空中游览'">
          <view class="flex-row">
            <input
              class="input-box"
              v-model="departure.text"
              placeholder="请选择"
              disabled
              @click="openPicker('起飞基地', baseOptions)"
            />
          </view>
        </FormItem>
        <view class="search-box">
          <view class="search-bth" @click="handleQuery"> 查询</view>
        </view>
      </view>
      <!-- 预约日期选择 -->

      <!-- 订单列表 -->
      <view class="order-list">
        <view class="section-title">订单列表</view>
        <van-tabs
          :active="tabActive"
          @change="tabChange"
          color="#2c5de5"
          type="card"
        >
          <van-tab title="空中游览" name="空中游览" style="margin: 0">
            <van-checkbox-group
              :value="checkedOrderIds"
              @change="onOrderChange"
              v-if="orderList.length > 0"
            >
              <view
                class="order-card"
                v-for="(item, index) in orderList"
                :key="index"
              >
                <!-- 订单列表标题 -->
                <view class="order-status">
                  <text>空中游览</text>
                  <van-checkbox
                    :value="index"
                    :name="index"
                    shape="square"
                    icon-size="14"
                  />
                </view>
                <!-- 产品信息 -->
                <view class="product-info">
                  <view class="info-row">
                    <text class="label">产品名称：</text>
                    <text class="value">{{ item.productName || '' }}</text>
                  </view>
                  <view class="info-row">
                    <text class="label">套餐名称：</text>
                    <text class="value">{{ item.packageName || '' }}</text>
                  </view>
                  <view class="info-row">
                    <text class="label">旅客：</text>
                    <text class="value">{{ item.passengers || '' }}</text>
                  </view>
                </view>
              </view>
            </van-checkbox-group>
            <Empty v-else />
          </van-tab>
          <van-tab title="空中交通" name="空中交通">
            <van-radio-group
              :value="checkedOrderIds"
              @change="onOrderChange"
              v-if="orderList.length > 0"
            >
              <view
                class="order-card"
                v-for="(item, index) in orderList"
                :key="index"
              >
                <!-- 订单列表标题 -->
                <view class="order-status">
                  <text>空中交通</text>
                  <van-radio :value="index" :name="index" icon-size="14" />
                </view>
                <!-- 产品信息 -->
                <view class="product-info">
                  <view class="info-row">
                    <text class="label">起飞基地：</text>
                    <text class="value">{{ item.depCity || '' }}</text>
                  </view>
                  <view class="info-row">
                    <text class="label">到达基地：</text>
                    <text class="value">{{ item.arrCity || '' }}</text>
                  </view>

                  <view class="info-row">
                    <text class="label">乘客：</text>
                    <text class="value">{{ item.passengerName || '' }}</text>
                  </view>
                  <view class="info-row">
                    <text class="label">航班日期：</text>
                    <text class="value">{{ item.flightDate || '' }}</text>
                  </view>
                  <view class="info-row">
                    <text class="label">计划时刻：</text>
                    <text class="value"
                      >{{ item.depTime || '' }} - {{ item.arrTime }}
                    </text>
                  </view>
                </view>
              </view>
            </van-radio-group>
            <Empty v-else />
          </van-tab>
        </van-tabs>
      </view>

      <!-- 发布需求按钮 -->
      <view class="publish-section">
        <view class="publish-btn" @click="publishOrder"> 发布需求</view>
      </view>
    </view>
    <Background />
    <!-- 日期选择器 -->
    <van-calendar
      :show="datePickerShow"
      @close="datePickerShow = false"
      @confirm="onDateConfirm"
      color="#2C5DE5"
    />
    <van-popup :show="pickerData.show" position="bottom">
      <van-picker
        :columns="pickerData.list"
        @confirm="onPickerConfirm"
        @cancel="closePicker"
        show-toolbar
        :title="pickerData.title"
      />
    </van-popup>
  </view>
</template>

<script>
import CustomerNav from '../../components/CutomerNav/index.vue'
import Empty from '../../components/Empty/index.vue'
import dayjs from 'dayjs'
import { DATE_FORMAT, SUCCESS_CODE } from '../../utils/constant'
import {
  getAirportAll,
  getTravelBookOrder,
  getTravelBookOrder2,
} from '../../api/flightTask'
import Background from '../../components/Background/index.vue'
import FormItem from './compoents/FormItem.vue'

export default {
  name: 'order',
  components: { FormItem, Background, CustomerNav, Empty },
  data() {
    return {
      tabActive: '空中游览',
      selectedDateType: '今天', // 当前选中的日期类型
      selectedDate: dayjs().format(DATE_FORMAT), // 选中的具体日期
      dateOptionList: [
        { text: '今天', value: '今天' },
        { text: '明天', value: '明天' },
        { text: '后天', value: '后天' },
        { text: '其他日期', value: '其他日期' },
      ],
      datePickerShow: false,
      orderList: [],
      checkedOrderIds: null,
      departure: { text: '', value: '' }, //起飞基地
      baseOptions: [], // 起飞基地选项
      // 下拉选择数据
      pickerData: {
        show: false,
        title: '',
        list: [],
      },
    }
  },
  mounted() {
    if (this.tabActive === '空中游览') {
      this.getAirportList().then(() => {
        this.getOrderList(this.tabActive)
      })
    } else {
      this.getOrderList(this.tabActive)
    }
  },
  methods: {
    onBack() {
      uni.switchTab({
        url: '/pages/home/<USER>',
      })
    },
    // 获取基地数据
    async getAirportList() {
      const res = await getAirportAll()
      if (res.response.code === SUCCESS_CODE) {
        this.baseOptions = res.response.data.map((item) => {
          return {
            text: item.name || '',
            value: item.threeAirportCode || '',
          }
        })
        this.departure.text = this.baseOptions[0].text
        this.departure.value = this.baseOptions[0].value
      }
    },

    // 选择日期
    selectDate(type) {
      this.selectedDateType = type.value
      switch (type) {
        case '今天':
          this.selectedDate = dayjs().format(DATE_FORMAT)
          break
        case '明天':
          this.selectedDate = dayjs().add(1, 'day').format(DATE_FORMAT)
          break
        case '后天':
          this.selectedDate = dayjs().add(2, 'day').format(DATE_FORMAT)
          break
        case '其他日期':
          console.log(1111)
          this.datePickerShow = true
          break
      }
    },

    onDateConfirm(ev) {
      this.selectedDate = dayjs(ev.detail).format(DATE_FORMAT)
      this.datePickerShow = false
    },
    openPicker(title, list) {
      this.pickerData = {
        show: true,
        title: title,
        list: list,
      }
    },
    closePicker() {
      this.pickerData = {
        show: false,
        title: '',
        list: [],
      }
    },
    onPickerConfirm(ev) {
      this.departure = ev.detail.value
      this.closePicker()
    },

    onOrderChange(ev) {
      this.checkedOrderIds = ev.detail
    },
    tabChange(ev) {
      this.tabActive = ev.detail.name
      this.getOrderList(ev.detail.name)
    },
    handleQuery() {
      this.getOrderList(this.tabActive)
    },
    //获取订单列表
    async getOrderList(type) {
      if (type === '空中游览') {
        const res = await getTravelBookOrder2({
          flightDate: this.selectedDate,
          areaCode: this.departure.value,
        })
        if (res.response.code === SUCCESS_CODE) {
          this.orderList = res.response.data || []
        }
      } else {
        const res = await getTravelBookOrder({ flightDate: this.selectedDate })
        if (res.response.code === SUCCESS_CODE) {
          this.orderList = res.response.data || []
        }
      }
    },
    // 发布订单
    publishOrder() {
      if (this.checkedOrderIds == null || this.checkedOrderIds.length === 0) {
        uni.showToast({
          title: '请选择订单',
          icon: 'none',
        })
        return
      }
      if (this.tabActive === '空中交通') {
        const params = JSON.stringify(this.orderList[this.checkedOrderIds])
        uni.navigateTo({
          url: `/pages/flightTask/create?pageType=${this.tabActive}&orderInfo=${params}`,
        })
      } else {
        const params = []
        this.checkedOrderIds.map((item) => {
          params.push(this.orderList[item])
        })
        uni.navigateTo({
          url: `/pages/flightTask/create?pageType=${this.tabActive}&orderInfo=${JSON.stringify(params)}`,
        })
      }
    },
  },
}
</script>

<style scoped lang="scss">
@import '../../assets/css/common.less';

.order-container {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  box-sizing: border-box;
  width: calc(100% - 32px);
  margin: 16px auto;
}

.filter {
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
}

.btn-groups {
  width: 100%;
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.custom-tag {
  padding: 0 4px;
  height: 24px;
  line-height: 24px;
  border: 1px solid #2c5de5;
  color: #2c5de5;
  background: transparent;
  font-size: 12px;
  border-radius: 4px;

  &.active {
    background: #2c5de5;
    color: #fff;
  }
}

/* 订单列表 */
.order-list {
  margin-bottom: 16px;
}

.order-card {
  margin-top: 10px;
  background-color: #fff;
  border-radius: 8px;
  //margin-bottom: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;

  .order-status {
    width: 100%;
    //background-color: rgba(44, 93, 229, 1);
    //color: #fff;
    border: 1px solid #f0f0f0;
    font-size: 14px;
    font-weight: bold;
    padding: 6px 16px;
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .product-info {
    padding: 16px;
  }
}

.input-box-line {
  padding: 4px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 8px;
  gap: 8px;
  color: #666;
  font-size: 14px;

  .right-icon {
    font-size: 12px;
  }
}

.info-row {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  min-width: 80px;
}

.value {
  color: #333;
  flex: 1;
}

/* 发布按钮 */
.publish-section {
  padding: 16px 0;
}

.publish-btn {
  width: 100%;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background-color: #fff;
  color: #2c5de5;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  border: 1px solid #2c5de5;
}

.publish-btn:active {
  background-color: rgba(27, 65, 191, 0.5);
  color: #fff;
  border: 1px solid rgba(27, 65, 191, 0.5);
}

.search-box {
  display: flex;
  justify-content: flex-end;
}

.search-bth {
  margin: 12px 0;
  display: inline-block;
  padding: 4px 8px;
  text-align: center;
  background-color: #fff;
  color: #2c5de5;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #2c5de5;

  &:active {
    background-color: rgba(27, 65, 191, 0.5);
    color: #fff;
    border: 1px solid rgba(27, 65, 191, 0.5);
  }
}
</style>
<style lang="scss">
.order-list {
  .van-tabs--card .van-tabs__scroll--card {
    margin: 0;
  }
}
</style>
