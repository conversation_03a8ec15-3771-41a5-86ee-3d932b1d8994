<template>
  <view class="taskItem-card" @click="handleTaskClick(taskItem)">
    <view class="taskItem-header">
      <view class="title-box"> {{ taskItem.taskType }}</view>
      <view class="option-box">
        <text>去确认</text>
        <van-icon name="arrow" />
      </view>
    </view>
    <view class="taskItem-content">
      <view class="info-row">
        <view class="info-item">
          <text class="label">注册号：</text>
          <text class="value">{{ taskItem.registrationNumber }}</text>
        </view>
        <view class="info-item">
          <text class="label">机型：</text>
          <text class="value">{{ taskItem.aircraftType }}</text>
        </view>
      </view>

      <view class="info-row" v-if="taskItem.departure">
        <view class="info-item">
          <text class="label">起飞基地：</text>
          <text class="value">{{ taskItem.departure }}</text>
        </view>
      </view>
      <view class="info-row" v-if="taskItem.routeOrAirspaceName">
        <view class="info-item full-width">
          <text class="label">航线/空域：</text>
          <text class="value">{{ taskItem.routeOrAirspaceName }}</text>
        </view>
      </view>
      <view class="info-row">
        <view class="info-item full-width">
          <text class="label">飞行日期：</text>
          <text class="value"
            >{{ taskItem.flightDate || '' }}({{
              getRelativeDateText(taskItem.flightDate)
            }})
          </text>
        </view>
      </view>

      <view class="info-row">
        <view class="info-item">
          <text class="label">预估架次：</text>
          <text class="value">{{ taskItem.flightFrequency }}</text>
        </view>
      </view>

      <view class="info-row">
        <view class="info-item full-width">
          <text class="label">发布时间：</text>
          <text class="value">{{ taskItem.createTime }}</text>
        </view>
      </view>
      <view class="split-line"></view>
      <view class="info-row">
        <view class="info-item full-width">
          <text class="label">已确认部门：</text>
          <view class="value process-box">
            <view
              v-for="item in processList"
              :key="item.name"
              class="process-item"
              :class="taskItem[item.statusName] === 1 ? 'active' : ''"
            >
              <view class="circle"></view>
              <text class="name">{{ item.name }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getRelativeDateText } from '../../../utils'

export default {
  name: 'TaskItem',
  props: {
    taskItem: {
      type: Object,
    },
    handleClick: {
      type: Function,
    },
  },
  data() {
    return {
      processList: [
        { name: '市场', statusName: 'marketConfirm' },
        { name: '飞行', statusName: 'flightConfirm' },
        { name: '机务', statusName: 'maintenanceConfirm' },
        { name: '运控', statusName: 'ocConfirm' },
      ],
    }
  },
  methods: {
    getRelativeDateText,
    handleTaskClick(val) {
      this.$emit('handleClick', val)
    },
  },
}
</script>

<style scoped></style>
