<template>
  <view class="receive-detail-page">
    <!-- 自定义导航栏 -->
    <CustomerNav title="飞行任务确认" />

    <!-- 内容主体 -->
    <view class="content">
      <!-- 表单区域 -->
      <view class="form-box">
        <van-row>
          <van-col span="12">
            <FormItem label="注册号：" class="text-row" label-width="60px">
              <text>{{ formData.registrationNumber }}</text>
            </FormItem>
          </van-col>
          <van-col span="12">
            <FormItem label="机型：" class="text-row" label-width="50px">
              <text>{{ formData.aircraftType }}</text>
            </FormItem>
          </van-col>
        </van-row>

        <FormItem label="任务性质：" class="text-row">
          <text>{{ formData.taskType }}</text>
        </FormItem>

        <FormItem label="计划时间：" class="text-row">
          <text
            >{{ formData.flightDate }}({{
              getRelativeDateText(formData.flightDate)
            }})
          </text>
        </FormItem>
        <view
          v-if="formData.taskType && formData.taskType.indexOf('空中游览') > -1"
        >
          <FormItem label="产品名称：" class="text-row">
            <text>{{ formData.productName }}</text>
          </FormItem>
          <FormItem label="套餐名称：" class="text-row">
            <text>{{ formData.packageName }}</text>
          </FormItem>
        </view>
        <view v-else>
          <FormItem
            label="起飞基地："
            class="text-row"
            v-if="!!formData.departure"
          >
            <text>{{ formData.departure }}</text>
          </FormItem>
          <FormItem
            label="降落基地："
            class="text-row"
            v-if="!!formData.arrive"
          >
            <text>{{ formData.arrive }}</text>
          </FormItem>
        </view>

        <FormItem label="预估架次：" class="text-row">
          <text>{{ formData.flightFrequency }}</text>
        </FormItem>

        <FormItem label="市场备注：" class="text-row">
          <text>{{ formData.remark }}</text>
        </FormItem>

        <FormItem label="计划起降时间" multi-line>
          <view>
            <FormItem
              :label="'架次' + (index + 1)"
              label-width="74px"
              is-child
              v-for="(item, index) in editData.takeOffAndLanding"
              :key="index"
              class="text-row"
            >
              <view class="flex-row">
                <input
                  class="input-box"
                  v-model="item.planDepartTime"
                  placeholder="预计起飞时间"
                  disabled
                  @click="openTimePicker(index, 'planDepartTime')"
                />
                <input
                  class="input-box"
                  v-model="item.planArriveTime"
                  placeholder="预计降落时间"
                  disabled
                  @click="openTimePicker(index, 'planArriveTime')"
                />
              </view>
            </FormItem>
          </view>
        </FormItem>

        <view>
          <FormItem label="航线类型">
            <view class="btn-groups">
              <view
                class="custom-tag"
                v-for="item in routeTypeList"
                :class="editData.flightType === item.value ? 'active' : ''"
                :key="item.value"
                @click="onRouteTypeChange(item)"
              >
                {{ item.text }}
              </view>
            </view>
          </FormItem>
          <view v-if="editData.flightType === 1">
            <FormItem
              label="航线选择"
              show-icon
              clearable
              @clear="onRouteClear"
            >
              <input
                class="input-box"
                v-model="editData.routeOrAirspaceName.text"
                placeholder="请选择航线"
                disabled
                @click="openPicker('航线', routeOptions, 'routeOrAirspaceName')"
              />
            </FormItem>
            <FormItem label="航线手动输入" multi-line>
              <view>
                <FormItem label="起飞基地" is-child label-width="74px">
                  <view class="flex-row">
                    <input
                      class="input-box"
                      v-model="editData.departure.text"
                      placeholder="请选择"
                      disabled
                      @click="openPicker('起飞基地', baseOptions, 'departure')"
                    />
                  </view>
                </FormItem>
                <FormItem label="经停点" is-child label-width="74px">
                  <view class="flex-row">
                    <input
                      class="input-box"
                      v-model="editData.alternate.text"
                      placeholder="请选择"
                      disabled
                      @click="openPicker('经停点', baseOptions, 'alternate')"
                    />
                  </view>
                </FormItem>
                <FormItem label="降落基地" is-child label-width="74px">
                  <view class="flex-row">
                    <input
                      class="input-box"
                      v-model="editData.arrive.text"
                      placeholder="请选择"
                      disabled
                      @click="openPicker('降落基地', baseOptions, 'arrive')"
                    />
                  </view>
                </FormItem>
              </view>
            </FormItem>
          </view>
          <view v-else>
            <FormItem label="空域选择" show-icon>
              <input
                class="input-box"
                v-model="editData.routeOrAirspaceName.text"
                placeholder="请选择空域"
                disabled
                @click="
                  openPicker('空域', airspaceOptions, 'routeOrAirspaceName')
                "
              />
            </FormItem>
            <FormItem label="航线手动输入" multi-line>
              <view>
                <FormItem label="起飞基地" is-child label-width="74px">
                  <view class="flex-row">
                    <input
                      class="input-box"
                      v-model="editData.departure.text"
                      placeholder="请选择"
                      disabled
                      @click="openPicker('起飞基地', baseOptions, 'departure')"
                    />
                  </view>
                </FormItem>
              </view>
            </FormItem>
          </view>

          <FormItem label="高度">
            <input
              class="input-box"
              v-model="editData.altitude"
              placeholder="请输入高度"
            />
          </FormItem>
          <FormItem label="备降场" show-icon>
            <view class="flex-row">
              <input
                class="input-box"
                :adjust-position="true"
                v-model="editData.emergencyLanding"
                placeholder="请选择"
                @click="openPicker('备降场', baseOptions, 'emergencyLanding')"
              />
            </view>
          </FormItem>
          <FormItem label="飞行规则">
            <input
              class="input-box"
              v-model="editData.flightRule"
              placeholder="请输入飞行规则"
            />
          </FormItem>
          <FormItem label="机长气象标准">
            <input
              class="input-box"
              v-model="editData.meteorologyStandard"
              placeholder="请输入机长气象标准"
            />
          </FormItem>
          <FormItem label="应答机">
            <input
              class="input-box"
              v-model="editData.transponderCode"
              placeholder="请输入应答机"
            />
          </FormItem>
          <FormItem label="燃油续航">
            <input
              class="input-box"
              v-model="editData.fuelEndurance"
              placeholder="请输入燃油续航"
            />
          </FormItem>
          <FormItem label="巡航速度">
            <input
              class="input-box"
              v-model="editData.circuitSpeed"
              placeholder="请输入巡航速度"
            />
          </FormItem>
        </view>
        <FormItem label="备注" class="text-row">
          <textarea
            v-model="editData.ocRemark"
            placeholder="请输入备注"
            :maxlength="500"
            class="textarea-box"
          />
        </FormItem>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-section">
        <van-button type="info" block @click="submitForm"
          >确定保障计划
        </van-button>
      </view>
      <Background />
      <!-- pop弹窗-->
      <van-popup :show="pickerData.show" position="bottom">
        <van-picker
          :columns="pickerData.list"
          @confirm="onPickerConfirm"
          @cancel="closePicker"
          show-toolbar
          :title="pickerData.title"
        />
      </van-popup>
      <!-- 时间选择器   -->
      <van-popup :show="timePicker.show" position="bottom">
        <van-datetime-picker
          type="time"
          :value="timePicker.value"
          @confirm="onTimeConfirm"
          @cancel="closeTimePicker"
        />
      </van-popup>
    </view>
  </view>
</template>

<script>
import CustomerNav from '../../components/CutomerNav/index.vue'
import FormItem from './compoents/FormItem.vue'
import {
  getAirportAll,
  getAirspaceAll,
  getRouteAll,
  planConfirmFlightTask,
  queryFlightTaskConfigDetail,
} from '../../api/flightTask'
import { SUCCESS_CODE } from '../../utils/constant'
import { getRelativeDateText } from '../../utils'
import Background from '../../components/Background/index.vue'

export default {
  name: 'sureDetail',
  components: { Background, CustomerNav, FormItem },
  data() {
    return {
      taskId: '',
      formData: {},
      editData: {
        routeOrAirspaceName: { text: '', value: '' }, //航线选择
        departure: { text: '', value: '' }, //起飞基地
        alternate: { text: '', value: '' }, //经停点
        arrive: { text: '', value: '' }, //降落基地
      }, // 编辑数据
      //下拉选择数据
      pickerData: {
        show: false,
        title: '',
        list: [],
        formKey: '',
      },
      //航线类型
      routeTypeList: [
        { text: '航线', value: 1 },
        { text: '空域', value: 2 },
      ],
      routeOptions: [], //航线
      airspaceOptions: [], //空域
      baseOptions: [], //起飞/降落/经停点基地
      timePicker: { show: false, index: 0, formKey: '', value: '12:00' },
    }
  },
  mounted() {
    this.getData()
  },
  onLoad: function (option) {
    this.taskId = option.id
  },
  methods: {
    getRelativeDateText,
    // 初始化数据
    async getData() {
      const res = await queryFlightTaskConfigDetail({
        flightTaskConfigId: Number(this.taskId),
      })
      if (res.response.code === 200) {
        const data = res.response.data
        this.formData = data || {}
        this.editData = {
          ...data,
          routeOrAirspaceName: {
            text: data.routeOrAirspaceName,
            value: data.routeOrAirspaceId,
          }, //航线选择
          departure: { text: data.departure, value: data.departureCode }, //起飞基地
          alternate: { text: data.alternate, value: data.alternateCode }, //经停点
          arrive: { text: data.arrive, value: data.arriveCode }, //降落基地
        }
        this.getPickerListData()
      }
    },
    //获取下拉数据
    async getPickerListData() {
      //航线
      if (this.editData.flightType === 1) {
        const res3 = await getRouteAll()
        if (res3.response.code === SUCCESS_CODE) {
          this.routeOptions = res3.response.data.map((item) => {
            return {
              text: item.routeCode || '',
              value: item.id || '',
            }
          })
        }
        const res5 = await getAirportAll()
        if (res5.response.code === SUCCESS_CODE) {
          this.baseOptions = res5.response.data.map((item) => {
            return {
              text: item.name || '',
              value: item.threeAirportCode || '',
            }
          })
        }
      }
      //空域
      if (this.editData.flightType === 2) {
        const res4 = await getAirspaceAll()
        if (res4.response.code === SUCCESS_CODE) {
          this.airspaceOptions = res4.response.data.map((item) => {
            return {
              text: item.airspaceName || '',
              value: item.id || '',
            }
          })
        }
      }
    },
    //航线清除
    onRouteClear() {
      this.formData.routeOrAirspaceName = { text: '', value: '' }
    },

    openPicker(title, list, formKey) {
      this.pickerData = {
        show: true,
        title: title,
        list: list,
        formKey: formKey,
      }
    },
    closePicker() {
      this.pickerData = {
        show: false,
        title: '',
        list: [],
        formKey: '',
      }
    },
    onPickerConfirm(ev) {
      this.editData[this.pickerData.formKey] = ev.detail.value
      if (this.pickerData.formKey === 'emergencyLanding') {
        this.editData.emergencyLanding = ev.detail.value.text
      }
      this.closePicker()
    },
    //时间选择
    onTimeConfirm(ev) {
      this.editData.takeOffAndLanding[this.timePicker.index][
        this.timePicker.formKey
      ] = ev.detail
      this.closeTimePicker()
    },
    closeTimePicker() {
      this.timePicker = { show: false, index: 0, formKey: '', value: '12:00' }
    },
    openTimePicker(index, formKey) {
      this.timePicker = {
        show: true,
        index: index,
        formKey: formKey,
        value: '12:00',
      }
    },
    //航线类型tab切换
    onRouteTypeChange(item) {
      this.editData.flightType = item.value
      this.editData.routeOrAirspaceName = ''
      this.editData.departure = ''
      this.editData.alternate = ''
      this.editData.arrive = ''
    },

    // 提交表单
    async submitForm() {
      // 构建提交数据
      const submitData = {
        ...this.editData,
        flightTaskConfigId: this.editData.id,
        routeOrAirspaceName: this.editData.routeOrAirspaceName.text,
        routeOrAirspaceId: this.editData.routeOrAirspaceName.value,
        departure: this.editData.departure.text,
        departureCode: this.editData.departure.value,
        alternate: this.editData.alternate.text,
        alternateCode: this.editData.alternate.value,
        arrive: this.editData.arrive.text,
        arriveCode: this.editData.arrive.value,
      }
      const { response } = await planConfirmFlightTask(submitData)
      if (response.code === SUCCESS_CODE) {
        uni.showToast({
          title: response.msg || '操作成功',
          icon: 'success',
        })
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/flightTask/sure?status=1',
          })
          // uni.navigateBack()
        }, 1500)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../assets/css/common.less';

.receive-detail-page {
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
}

.content {
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
}

/* 表单样式 */
.form-box {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  /deep/ .text-row {
    word-break: break-all; /* 强制在单词内换行 */
    white-space: pre-line;

    .value-box {
      border-bottom: none;
    }

    .input-box {
      border-bottom: 1px solid #ccc;
    }
  }

  .flex-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 6px;
  }

  .btn-groups {
    width: 100%;
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
  }

  .custom-tag {
    padding: 0 4px;
    height: 24px;
    line-height: 24px;
    border: 1px solid #2c5de5;
    color: #2c5de5;
    background: transparent;
    font-size: 12px;
    border-radius: 4px;

    &.active {
      background: #2c5de5;
      color: #fff;
    }
  }
}

.submit-btn {
  width: 100%;
  margin-top: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.textarea-box {
  width: 100%;
  margin-top: 8px;
  border: 1px solid #eee;
  box-sizing: border-box;
  padding: 8px;
  border-radius: 4px;
}
</style>
