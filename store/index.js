// import Vue from 'vue';
// import Vuex from 'vuex';
// Vue.use(Vuex)
// const store = new Vuex.Store({
// 	state: {
// 		forceLogin: false,
// 		hasLogin: false,
// 		userName: '',
// 		userId: '',
// 		token: '',
// 		pointId: ''
// 	},
// 	mutations: {
// 		login(state, user) {
// 			state.userName = user.userName || '';
// 			state.hasLogin = true;
// 			state.userId = user.id || '';
// 			state.token = user.token || '';
// 			state.pointId = user.pointId || '';
// 			uni.setStorage({
// 				key: 'userInfo',
// 				data: user
// 			})
// 		},
// 		logout(state) {
// 			state.userName = '';
// 			state.hasLogin = false;
// 			state.userId = '';
// 			state.token = '';
// 			state.pointId = '';
// 			uni.removeStorage({
// 				key: 'userinfo'
// 			})
// 		}
// 	},
// })
