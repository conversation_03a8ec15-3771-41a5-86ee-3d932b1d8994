<template>

	<view class="warp">
		<view class="">
			<image class='plane' src="../../static/images/plane_bg.png" mode="widthFix"></image>
			<!-- <view class="main">
				<view class="header flex-row fz-12">
					<span class="index">{{taskIndex + 1}}</span>
					<span class="marRight33 white-nowrap">
						<text class="iconfont icon-seedling-fill col-gray-400 margin-right-4"></text>
						<span class="col-gray-500 marRight4 fz-14">旅客</span>
						<span class="col-gray-600 fz-14">{{taskInfo.name}}</span>
					</span>
					<span class="white-nowrap">
						<text class="iconfont icon-refresh-fill col-gray-400 margin-right-4"></text>
						<span class="col-gray-600 marRight4 fz-14" >{{taskInfo.idNumber}}</span>
					</span>
					
				</view>
			</view> -->

			<view class="detail-row bg-white-500 no-fold" style="padding-top: 0;">
				<span class="index">{{taskIndex + 1}}</span>
				<view class="detail-block">
					<p class="fz-12 col-gray-500">旅客</p>
					<p class="fz-14 col-gray-600">{{isNull(taskInfo.name)}}</p>
				</view>
				<view class="detail-block">
					<p class="fz-12 col-gray-500">状态</p>
					<p class="fz-14 col-gray-600">{{verificationStatus(taskInfo)}}</p>
				</view>
				<view class="arrow bg-white-500" @click.stop="changeShow">
					<text v-show="showMore" class="iconfont icon-arrow-up-line"><text
							class="fz-12 col-gray-500">关闭</text></text>
					<text v-show="!showMore" class="iconfont icon-arrow-down-line"><text
							class="fz-12 col-gray-500">展开</text></text>
				</view>
			</view>
			<view class="detail-row bg-white-500 no-fold" style="padding-top: 0;padding-left: 45px;">
				<view class="detail-block">
					<p class="fz-12 col-gray-500">证件号</p>
					<p class="fz-14 col-gray-600">{{isNull(taskInfo.idNumber)}}</p>
				</view>
			</view>
			
			<!-- 班次 -->
			<view class="" >
				<view :class="showMore ? 'show-view':'close-view'" class="bg-white-500 fold detail-row" style="padding-left: 45px;">
					<view class="detail-block">
						<p class="fz-12 col-gray-500">始发站</p>
						<p class="fz-14 col-gray-600">{{taskInfo.departureName}}({{taskInfo.departure}})</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">到达站</p>
						<p class="fz-14 col-gray-600">{{taskInfo.destinationName}}({{taskInfo.destination}})</p>
					</view>

				</view>


				<view :class="showMore ? 'show-view':'close-view'" class="bg-white-500 fold detail-row" style="padding-left: 45px;">
					<view class="detail-block">
						<p class="fz-12 col-gray-500">票号</p>
						<p class="fz-14 col-gray-600">{{taskInfo.ticketCode}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">值机时间</p>
						<p class="fz-14 col-gray-600">{{isNull(taskInfo.checkInDate)}} {{isNull(taskInfo.checkInTime)}}
						</p>
					</view>
				</view>


				<view :class="showMore ? 'show-view':'close-view'" class="bg-white-500 fold detail-row" style="padding-left: 45px;">
					<view class="detail-block">
						<p class="fz-12 col-gray-500">舱位</p>
						<p class="fz-14 col-gray-600">{{isNull(taskInfo.cabin)}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">座位</p>
						<p class="fz-14 col-gray-600">{{isNull(taskInfo.seat)}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">手机号</p>
						<p class="fz-14 col-gray-600 colorBlue" @click.stop='callPhone(taskInfo.telephone)'>
							{{isNull(taskInfo.telephone)}}
						</p>
					</view>
				</view>

				<view :class="showMore ? 'show-view':'close-view'" class="bg-white-500 fold detail-row" style="padding-left: 45px;">

					<view class="detail-block">
						<p class="fz-12 col-gray-500">安检时间</p>
						<p class="fz-14 col-gray-600">{{isNull(taskInfo.checkTime)}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">登机时间</p>
						<p class="fz-14 col-gray-600">{{isNull(taskInfo.boardingTime)}}</p>
					</view>

					<view class="detail-block">
						<p class="fz-12 col-gray-500">登机/取消登机</p>
						<p class="fz-14 col-gray-600 colorBlue" v-if="taskInfo.boardingStatus!=2"
							@click.stop='boarding(taskInfo)'>登机</p>
						<p class="fz-14 col-gray-600 colorBlue" v-if="taskInfo.boardingStatus==2"
							@click.stop='boardingCancel(taskInfo)'>取消登机</p>
					</view>

				</view>

				<view :class="showMore ? 'show-view':'close-view'" class="bg-white-500 fold detail-row no-fold" style="padding-left: 45px;">
					<view class="detail-block">
						<p class="fz-12 col-gray-500">旅客体重(KG)</p>
						<p class="fz-14 col-gray-600"><input class="fz-12 colorBlue" v-model="taskInfo.weightPersonal"
								placeholder="请输入" /></p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">手提重量(KG)</p>
						<p class="fz-14 col-gray-600"><input class="fz-12 colorBlue"
								v-model="taskInfo.weightHandLuggage" placeholder="请输入" /></p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">托运重量(KG)</p>
						<p class="fz-14 col-gray-600"><input class="fz-12 colorBlue"
								v-model="taskInfo.weightCheckedBaggage" placeholder="请输入" /></p>
					</view>
					<view class="detail-block" @click.stop='updateWeight(taskInfo)'>
						<p class="fz-12 col-gray-500">更新体重</p>
						<p class="fz-14 col-gray-600 colorBlue">提交</p>
					</view>
				</view>
			</view>
		</view>
	</view>
	</view>
</template>

<script>
	import {
		studyPassengerWeightUpdate,
		studyPassengerBoarding,
		studyPassengerCancelBoarding
	} from '../../api/weChat.js';
	export default {
		name: "TaskCard",
		props: {
			taskInfo: {
				type: Object,
			},
			taskIndex: {
				type: Number
			},
			types: {
				type: String
			},
			flight: {
				type: String
			},
			flightDate: {
				type: String,
			}
		},
		created() {
			this.userRole = uni.getStorageSync('userRole');
			this.userRole.includes('pilot')

		},
		mounted() {
			console.log(111111111, this.taskInfo)
		},
		data() {
			return {
				userRole: "",
				showMore: false,
				value: '',
				index: 0,
				showFlag: false,
			}
		},
		methods: {
			verificationStatus(item) {
				let result = ''
				if (item.checkStatus == 0 && item.boardingStatus != 2) {
					if (item.seat == null || item.seat == '') {
						result = "未值机"
					} else {
						result = "未安检"
					}
				} else if (item.boardingStatus != 2 && item.checkStatus == 1) {
					result = "已安检"
				} else if (item.boardingStatus == 2) {
					result = "已登机"
				}
				return result;
			},
			callPhone(e) {
				uni.showModal({
					title: '提示',
					content: '是否拨打旅客电话？',
					confirmText: "确定",
					cancelText: "取消",
					success: function(res) {
						if (res.confirm) {
							uni.makePhoneCall({
								phoneNumber: e,
							})
						}
					}
				})
			},
			isNull(e) {
				return e == null ? '' : e
			},

			changeShow() {
				this.showMore = !this.showMore;
			},

			changeShow() {
				this.showMore = !this.showMore;
			},

			async manualBoarding(e) {
				let that = this
				let param = {
					boardCard: e.boardingCard,
					flightNumber: e.flightNumber,
					execDate: e.date,
					group: e.group,
					boardingType: 2,
					ticketCode: e.ticketCode
				}
				console.log(param)
				const res = await studyPassengerBoarding(param);
				console.log(res)
				if (res.response.code == 200 && res.response.data == true) {
					uni.showToast({
						title: "登机成功"
					})
					setTimeout(function() {
						that.$emit('custom-event')
					}, 500)

				} else {
					uni.showModal({
						content: "登机失败"
					})
				}
			},
			boarding(e) {
				let that = this
				uni.showModal({
					title: '提示',
					content: '确认登机吗？',
					confirmText: "确定",
					cancelText: "取消",
					success: function(res) {
						if (res.confirm) {
							that.manualBoarding(e)
						}
					}
				})
			},

			async manualBoardingCancel(e) {
				let that = this
				let param = {
					boardCard: e.boardingCard,
					flightNumber: e.flightNumber,
					execDate: e.date,
					group: e.group,
					boardingType: 2,
					ticketCode: e.ticketCode
				}
				console.log(param)
				const res = await studyPassengerCancelBoarding(param);
				console.log(res)
				if (res.response.code == 200 && res.response.data == true) {
					uni.showToast({
						title: "取消成功"
					})
					setTimeout(function() {
						that.$emit('custom-event')
					}, 500)

				} else {
					uni.showModal({
						content: "取消登机失败"
					})
				}
			},
			boardingCancel(e) {
				let that = this
				uni.showModal({
					title: '提示',
					content: '确认取消登机吗？',
					confirmText: "确定",
					cancelText: "取消",
					success: function(res) {
						if (res.confirm) {
							that.manualBoardingCancel(e)
						}
					}
				})
			},
			async updateWeight(e) { //登机验证

				console.log(e)
				let param = {
					ticketCode: e.ticketCode,
					weightPersonal: e.weightPersonal,
					weightHandLuggage: e.weightHandLuggage,
					weightCheckedBaggage: e.weightCheckedBaggage
				}
				console.log(param)
				const res = await studyPassengerWeightUpdate(param);
				console.log(res)
				if (res.response.code == 200 && res.response.data == true) {
					uni.showToast({
						title: "更新成功"
					})


				} else {
					uni.showModal({
						content: "更新体重失败"
					})
				}


			},

		}
	}
</script>

<style lang="less" scoped>
	.warp {
		position: relative;
		overflow: hidden;
		background-color: rgba(255, 255, 255, 0.64);
		border: 1px solid rgba(255, 255, 255, 0.72);
		border-radius: 8px;
		// margin-bottom: 12px;
	}

	.colorBlue {
		color: #4858af;
	}

	.plane {
		width: 190px;
		position: absolute;
		top: 0;
		right: 0;
	}

	.index {
		width: 16px;
		height: 16px;
		line-height: 16px;
		text-align: center;
		margin-right: 13px;
		border-radius: 50%;
		color: #838791;
		background-color: #EAEAEB;
		display: inline-block;
		font-size: 12px;
	}

	.main {
		padding: 24rpx 32rpx 0 32rpx;
		height: 45px;
		background-color: rgba(0, 0, 0, 0);

		.header {
			padding-right: 84rpx;
			height: 16px;



			.marRight4 {
				margin-right: 8rpx;
			}

			.marRight33 {
				margin-right: 66rpx;
			}
		}

		.trip-address {
			margin-top: 16px;
			margin-bottom: 12px;

			.fz-16 {
				font-weight: 800;
			}
		}

		.trip-bar {
			width: 16px;
			height: 104px;
			margin-right: 12px;
		}

		.show-view {
			height: 330px;
			transition: .5s;
			backdrop-filter: blur(16px);
		}

		.close-view {
			height: 0;
			transition: .5s;
			overflow: hidden;
		}
	}

	.detail-row {
		padding: 12px 16px;
		display: flex;
		width: 100%;
		backdrop-filter: blur(16px);
		justify-content: space-between;
	}

	.show-view {
		min-height: 50px;
		transition: .5s;
		backdrop-filter: blur(16px);
	}

	.close-view {
		height: 0;
		transition: .5s;
		overflow: hidden;
	}

	.detail-block {
		flex: 1;
		// display: flex;
		flex-direction: column;
		justify-content: top;
		align-items: center;
		position: relative;
		margin-right: 13px;

		.phone {
			position: absolute;
			bottom: -17px;
			font-size: 12px;
			color: #2C5DE5;
		}

		.icon-phone-fill {
			font-size: 12px !important;
		}

		.fz-14 {
			font-weight: 600;
		}
	}

	//机型机号等样式调整
	.no-fold {
		padding-bottom: 15px;
	}

	.fold {
		padding: 0 16px;

		.detail-row {
			padding: 0;

			&:not(:last-child) {
				margin-bottom: 12px;
			}

			&:last-child {
				padding-top: 12px;
			}

			.fz-14 {
				margin-top: 4px;
			}

			&:nth-of-type(2) {
				.fz-14 {
					margin-bottom: 4px;
				}
			}
		}
	}

	.handel-section {
		width: 100%;
		padding: 12px 0;
		display: flex;
		flex-wrap: wrap;

		.handle-block {
			padding: 8px 10.5px;
			width: calc(50% - 8px);
			height: 60px;
			border: 1px solid #D9DADD;
			border-radius: 4px;
			display: flex;
			margin-bottom: 12px;
		}

		.handle-block:nth-child(odd) {
			margin-right: 16px;
		}

		.status {
			height: 20px;
			line-height: 20px;
			text-align: center;
			border-radius: 50px;
			width: fit-content;
			padding: 0 8px;
		}

		.complete {
			//已完成的状态
			background: #E9FBF1;
			color: #5BBC72;
			border: 1px solid #D6F3E2;
		}

		.incomplete {
			//未完成的状态
			background: #F6F6F6;
			color: #50545E;
			border: 1px solid #D9DADD;
		}

		.refuse {
			//已拒绝状态
			background: #FFEFF0;
			color: #E83F4E;
			border: 1px solid #FFD6D7;
		}
	}

	.arrow {
		height: 20px;
		text-align: center;
		backdrop-filter: blur(16px);
	}

	.show-view.stories {
		height: 30px;
		font-weight: 600;
		border-top: 1px solid #efefef;
	}

	.show-view.titles {
		height: 50px !important;
		line-height: 40px !important;
	}
</style>