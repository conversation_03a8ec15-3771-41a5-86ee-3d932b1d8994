<template>
	<view class="warp">
		<view class="" @click="openGroup()">
			<image class='plane' src="../../static/images/plane_bg.png" mode="widthFix"></image>
			<view class="main">
				<view class="header flex-row fz-12">
					<span class="index">{{taskIndex + 1}}</span>
					<span class="marRight33 white-nowrap">
						<text class="iconfont icon-seedling-fill col-gray-400 margin-right-4"></text>
						<span class="col-gray-500 marRight4" style='font-size: 13px;'>LOAD：</span>
						<span class="col-gray-600">{{taskIndex + 1}}</span>
					</span>
					<span class="white-nowrap">
						<text class="iconfont icon-refresh-fill col-gray-400 margin-right-4"></text>
						<span class="col-gray-500 marRight4" style='font-size: 13px;'>状态：</span>
						<span class="col-gray-600">正常</span>
					</span>

				</view>

			</view>

			<view class="detail-row bg-white-500 no-fold">

				<view style="width: 50%;" v-for="(item, index) in taskInfo.passengerGroup">
					<view class="detail-block">
						<p class="fz-12 col-gray-500">旅客</p>
						<p class="fz-14 col-gray-600">
							{{item.weight==null?item.passengerName:item.passengerName+' - '+item.weight}}</p>
					</view>
				</view>
			</view>
		</view>
	</view>







</template>

<script>
	import {

	} from '../../api/weChat.js';
	export default {
		name: "TaskCard",
		props: {
			taskInfo: {
				type: Object,
			},
			taskIndex: {
				type: Number
			},
			types: {
				type: String
			},
			flight: {
				type: String
			},
			flightDate: {
				type: String,
			}
		},
		created() {
			this.userRole = uni.getStorageSync('userRole');
			this.userRole.includes('pilot')

		},
		mounted() {
			console.log(111111111, this.taskInfo)
		},
		data() {
			return {
				pickerData: [{
						'name': '张三',
						'age': 18
					},
					{
						'name': '李四',
						'age': 20
					},
					{
						'name': '王五',
						'age': 21
					},
					{
						'name': '彰武',
						'age': 18
					}
				],
				list: [{
						'group': '001',
						'members': ['张三', '李四'],
						'selectedIndex': 0
					},
					{
						'group': '002',
						'members': ['彰武', '王五'],
						'selectedIndex': 0
					}
				],
				userRole: "",
				showMore: false,
				value: '',
				index: 0,
				showFlag: false,
			}
		},
		methods: {
			bindChange(e, groupIndex) {
				// 更新对应分组的selectedIndex
				this.list[groupIndex].selectedIndex = e.detail.value;
			},
			isNull(e) {
				return e == null ? '' : e
			},
			bindPickerChange(e) {
				console.log(e)
				this.index = e.detail.value
			},
			changeShow() {
				this.showMore = !this.showMore;
			},

			changeShow() {
				this.showMore = !this.showMore;
			},

			openGroup() { //旅客列表

				uni.navigateTo({
					url: '/pages/study/sortie/details?param=' + JSON.stringify(this.taskInfo)
				});
			}
		}
	}
</script>

<style lang="less" scoped>
	.warp {
		position: relative;
		overflow: hidden;
		background-color: rgba(255, 255, 255, 0.64);
		border: 1px solid rgba(255, 255, 255, 0.72);
		border-radius: 8px;
		margin-bottom: 12px;
	}

	.plane {
		width: 190px;
		position: absolute;
		top: 0;
		right: 0;
	}

	.main {
		padding: 24rpx 0rpx 0 32rpx;
		height: 45px;
		background-color: rgba(0, 0, 0, 0);

		.header {
			// padding-right: 84rpx;
			height: 16px;

			.index {
				width: 16px;
				height: 16px;
				line-height: 16px;
				text-align: center;
				margin-right: 13px;
				border-radius: 50%;
				color: #838791;
				background-color: #EAEAEB;
				display: inline-block;
			}

			.marRight4 {
				margin-right: 8rpx;
			}

			.marRight33 {
				margin-right: 10rpx;
			}
		}

		.trip-address {
			margin-top: 16px;
			margin-bottom: 12px;

			.fz-16 {
				font-weight: 800;
			}
		}

		.trip-bar {
			width: 16px;
			height: 104px;
			margin-right: 12px;
		}

		.show-view {
			height: 330px;
			transition: .5s;
			backdrop-filter: blur(16px);
		}

		.close-view {
			height: 0;
			transition: .5s;
			overflow: hidden;
		}
	}

	.detail-row {
		padding: 12px 16px;
		display: flex;
		width: 100%;
		backdrop-filter: blur(16px);
		flex-wrap: wrap;
		justify-content: space-between;
	}

	.show-view {
		height: 50px;
		transition: .5s;
		backdrop-filter: blur(16px);
	}

	.close-view {
		height: 0;
		transition: .5s;
		overflow: hidden;
	}

	.detail-block {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: top;
		align-items: center;
		position: relative;

		.phone {
			position: absolute;
			bottom: -17px;
			font-size: 12px;
			color: #2C5DE5;
		}

		.icon-phone-fill {
			font-size: 12px !important;
		}

		.fz-14 {
			font-weight: 600;
		}
	}

	//机型机号等样式调整
	.no-fold {
		padding-bottom: 15px;
	}

	.fold {
		padding: 0 16px;

		.detail-row {
			padding: 0;

			&:not(:last-child) {
				margin-bottom: 12px;
			}

			&:last-child {
				padding-top: 12px;
			}

			.fz-14 {
				margin-top: 4px;
			}

			&:nth-of-type(2) {
				.fz-14 {
					margin-bottom: 4px;
				}
			}
		}
	}

	.handel-section {
		width: 100%;
		padding: 12px 0;
		display: flex;
		flex-wrap: wrap;

		.handle-block {
			padding: 8px 10.5px;
			width: calc(50% - 8px);
			height: 60px;
			border: 1px solid #D9DADD;
			border-radius: 4px;
			display: flex;
			margin-bottom: 12px;
		}

		.handle-block:nth-child(odd) {
			margin-right: 16px;
		}

		.status {
			height: 20px;
			line-height: 20px;
			text-align: center;
			border-radius: 50px;
			width: fit-content;
			padding: 0 8px;
		}

		.complete {
			//已完成的状态
			background: #E9FBF1;
			color: #5BBC72;
			border: 1px solid #D6F3E2;
		}

		.incomplete {
			//未完成的状态
			background: #F6F6F6;
			color: #50545E;
			border: 1px solid #D9DADD;
		}

		.refuse {
			//已拒绝状态
			background: #FFEFF0;
			color: #E83F4E;
			border: 1px solid #FFD6D7;
		}
	}

	.arrow {
		height: 20px;
		text-align: center;
		backdrop-filter: blur(16px);
	}

	.show-view.stories {
		height: 30px;
		font-weight: 600;
		border-top: 1px solid #efefef;
	}

	.show-view.titles {
		height: 50px !important;
		line-height: 40px !important;
	}
</style>