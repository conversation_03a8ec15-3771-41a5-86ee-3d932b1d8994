<template>
  <view>
    <view
      class="nav"
      :class="{ 'nav-scrolled': isScrolled }"
      :style="'height:' + navH + 'px;' + style"
    >
      <view class="nav-title">
        <text
          class="nav-title-text iconfont icon-arrow-left-s-line"
          @click="handleGoBack()"
        ></text>
        <text class="nav-title-text">{{ title }}</text>
      </view>
    </view>
    <!--    占位-->
    <view :style="'height:' + navH + 'px;'"></view>
  </view>
</template>
<script>
export default {
  name: 'CustomerNav',
  props: {
    title: String, // 导航栏标题
    style: String, // 导航栏样式
    customGoBack: Boolean, // 是否使用自定义返回逻辑
  },
  emits: ['onBack'],
  data() {
    return {
      navH: 0,
      isScrolled: false, // 是否已滚动
    }
  },
  created() {
    // 获取导航栏高度
    this.navH = getApp().globalData.navHeight || 88
  },
  methods: {
    // 返回上一页
    handleGoBack() {
      // 如果父组件需要自定义返回逻辑，则触发事件
      if (this.customGoBack) {
        this.$emit('onBack')
      } else {
        // 否则默认返回上一页
        uni.navigateBack()
      }
    },
  },
}
</script>

<style scoped lang="scss">
// 导航栏样式
.nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  display: flex;
  align-items: flex-end;
  padding-bottom: 10px;
  transition: all 0.3s ease;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-title {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  position: relative;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.nav-title-text:first-child {
  position: absolute;
  left: 15px;
  font-size: 20px;
  cursor: pointer;
}
</style>
