<template>
    <view>
        <view class="show-box-bg wx-login-box">
            <view class="conten">
				<image class="submit-img" v-if="modalInfo.type === '1'" src="../../static/images/submit_success.png"></image>
				<image class="submit-img" v-if="modalInfo.type === '2'" src="../../static/images/submit_err.png"></image>
                <view class="text">
                    {{ modalInfo.content }}
                </view>
                <view class="btn-box" v-if="modalInfo.type === '1'">
                    <view class="confirm-success" @tap="$emit('cancel')">
                        确定
                    </view>
                </view>
				<view class="btn-box" v-if="modalInfo.type === '2'">
				    <view class="cancel" @tap="$emit('cancel')">
				        返回
				    </view>
				    <view class="confirm" @tap="$emit('confirm')">
				        重新答题
				    </view>
				</view>
            </view>
        </view>
    </view>
</template>

<script>
    export default {
        props:['modalInfo'],
        data() {
            return {}
        },
        methods:{
            operation(e){
                let type = e
                this.$emit('operation',type)
            },
        }
    }
</script>

<style scoped lang="scss">
    .wx-login-box {
        width: 100%;
        position: fixed;
        top: 0;
        left: 0;
        height: 100%;
		background-color: rgba(1, 6, 32, 0.7);
        z-index: 901;
        .conten {
            width: calc(100% - 32px);
            height: 260px;
            background-color: #FFF;
            z-index: 1000;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);
            border-radius: 8px;
			padding: 16px 20px;
			text-align: center;
            overflow: hidden;
			.submit-img{
				width: 120px;
				height: 120px;
				margin-bottom: 8px;
			}
            .text {
                width: 90%;
                font-size: 14px;
                color: #838791;
                text-align: center;
            }
            .btn-box {
                width: 100%;
				position: absolute;
				bottom: 20px;
				padding: 0 20px;
				left: 0;
                display: flex;
                .cancel {
                    width: 50%;
                    font-size: 14px;
					height: 40px;
					line-height: 40px;
					text-align: center;
                    color: #2C5DE5;
					background-color: #fff;
					border: 1px solid #2C5DE5;
					border-radius: 4px;
					margin-right: 8px;
                }
                .confirm {
                    width: 50%;
                    font-size: 14px;
					height: 40px;
					line-height: 40px;
					text-align: center;
                    color: #fff;
					background-color: #2C5DE5;
					border-radius: 4px
                }
				.confirm-success{
					width: 100%;
					font-size: 14px;
					height: 40px;
					line-height: 40px;
					text-align: center;
					color: #fff;
					background-color: #2C5DE5;
					border-radius: 4px
				}
            }
        }
        
    }
    
</style>