<template>
	<view>
		<view>
			<view v-for="(item,index) in chooseList" :key="item.bankId" class="radioItems">
				<view class="img">
					<image class="judgeImg" :src=" item.isTrue ? rightImg:wrongImg" mode="aspectFit"></image>
				</view>
				<view class="explain-container">
					<view>
						<view class="uni-title uni-common-mt uni-common-pl">{{item.item}}</view>
						<view class="uni-list">
							<radio-group :data-id="index">
								<label class="uni-list-cell uni-list-cell-pd" v-for="(obj, index) in item.option"
									:key="obj.value">
									<view class="cell-view">
										<radio disabled :value="obj.value" :checked="obj.id === item.uploadAnswer" />
									</view>
									<view class="cell-view">{{obj.value}}</view>
								</label>
							</radio-group>
						</view>
					</view>
					<view class="explains">
						<text class="explains-text">正确答案：{{item.trueAnswer==1?'A':item.trueAnswer==2?'B':item.trueAnswer==3?'C':item.trueAnswer==4?'D':''}}</text>
						<view class="log">
							<text class="explains-text">解析：</text>
							<text class="explains-text">{{item.analysis}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "radio-result",
		props: {
			chooseList: {
				type: Array
			}
		},
		data() {
			return {
				rightImg: "../../static/images/correct.png",
				wrongImg: "../../static/images/error.png"
			}
		},
		// watch: {
		// 	chooseList: function(newVal, oldVal) {
		// 		this.cData = newVal; //newVal即是chartData
		// 		console.log(this.cData )
		// 		this.drawChart();
		// 	}
		// },
		created() {},
		methods: {}
	}
</script>

<style lang="less" scoped>
	.radioItems {
		display: flex;
		margin-bottom: 24px;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: normal;
		font-size: 12px;
		line-height: 16px;

		&:last-child {
			margin-bottom: 0;
		}

		.img {
			.judgeImg {
				width: 18px;
				height: 18px;
				margin-right: 8px;
			}
		}

		&:not(:last-child) {
			margin-bottom: 24px;
		}

		.explain {
			outline: 1px solid red;
			width: 100%;
		}
	}

	.uni-title {
		color: #202532;
	}

	.uni-list-cell {
		display: flex;
		margin-top: 8px;

		.cell-view {
			color: #50545E;

			&:last-child {
				margin-top: 2px;
			}
		}
	}

	.explains {
		padding: 8px;
		width: 100%;
		// height: 80px;
		background: rgba(233, 251, 241, 0.64);
		border: 1px solid rgba(255, 255, 255, 0.72);
		box-sizing: border-box;
		border-radius: 8px;
		order: 5;
		margin: 8px 0px;

		.explains-text {
			width: 100%;
			font-family: SF Pro Text;
			font-style: normal;
			font-weight: 600;
			font-size: 12px;
			line-height: 12px;
			color: #50545E;
			flex-grow: 1;
		}

		.log {
			.explains-text {
				&:last-child {
					font-weight: normal;
				}
			}
		}
	}
</style>
