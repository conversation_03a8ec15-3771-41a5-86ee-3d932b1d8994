<template>
	<view class="timeline">
		<view v-for="(item,index) in recordList" :key="index">
			<view class="recordLine" v-if="index<10">
				<view class="recordTime">
					<text class="recordTime-text">{{item.opTime.split(' ')[0]}}<br/></text>
					<text class="recordTime-text">{{item.opTime.split(' ')[1]}}</text>
				</view>
				<!-- <view class="recordImg" :style="'height:'+recordTextHeight+'px'"> -->
				<view class="recordImg" :style="'height:'+65+'px'">
					<image class="img" src="../../static/images/recordLine.png" mode="widthFix"></image>
				</view>
				<view class="recordText">
					<text class="text">{{item.opTypeStr}}</text>
					<text class="text">{{item.opMsg == null  ? "":item.opMsg}}</text>
					<text class="text">{{item.opPersonType}}：{{item.opPersonName}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "timeLine",
		props: {
			recordList: {
				type: Array
			}
		},
		data() {
			return {
				timeYear: "",
				timeHour: "",
				recordTextHeight: ""
			};
		},
		mounted: function() {},
		watch: {
			recordList(newV) {
				this.lineOutHeight();
			},
		},
		methods: {
			lineOutHeight() {
				let view = uni.createSelectorQuery().in(this).select(".recordText");
				view.fields({
					size: true,
					scrollOffset: true
				}, data => {
					console.log("得到节点信息" + JSON.stringify(data));
					// this.recordTextHeight = data.height;
				}).exec();
			},
		}
	}
</script>

<style lang="less" scoped>
	.timeline {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 12px 16px;
		width: 100%;
		background: rgba(255, 255, 255, 0.64);
		border: 1px solid rgba(255, 255, 255, 0.72);
		box-sizing: border-box;
		border-radius: 8px;
	}

	.recordLine {
		height: auto;
		display: flex;

		&:last-child {
			overflow: hidden;
		}

		.recordTime {
			text-align: end;
			font-size: 12px;
			line-height: 20px;
			text-align: right;
			color: #50545E;

			.recordTime-text {
				display: block;
				line-height: 16px;

				&:last-child {
					margin-top: 4px;
				}
			}
		}

		.recordImg {
			width: 12px;
			height: auto;
			position: relative;
			margin: 0 12px;


			.img {
				width: inherit;
				position: relative;
				height: 100%;
				top: 3px;
			}
		}

		.recordText {
			flex: 1;
			display: flex;
			flex-direction: column;
			height: auto;
			margin-bottom: 24px;

			.text {
				font-size: 12px;
				line-height: 16px;
				color: #50545E;

				&:first-child {
					font-weight: 600;
				}

				&:not(:first-child) {
					font-weight: normal;
					margin-top: 4px;
				}
			}
		}
	}
</style>
