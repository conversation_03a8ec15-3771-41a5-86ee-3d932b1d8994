<template>
  <view class="empty-container" :style="containerStyle">
    <!-- 空状态图片 -->
    <view class="empty-image-wrapper">
      <image
        class="empty-image"
        :src="imageSrc"
        :style="imageStyle"
        mode="aspectFit"
      />
    </view>

    <!-- 空状态文本 -->
    <view class="empty-text" v-if="description">
      <text class="empty-description">{{ description }}</text>
    </view>

    <!-- 自定义插槽内容 -->
    <view class="empty-slot" v-if="$slots.default">
      <slot></slot>
    </view>

    <!-- 操作按钮 -->
    <view class="empty-action" v-if="buttonText">
      <view
        class="empty-button"
        :class="buttonClass"
        @click="handleButtonClick"
      >
        <text class="empty-button-text">{{ buttonText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Empty',
  props: {
    // 空状态图片路径
    image: {
      type: String,
      default: '/static/images/nodata.png',
    },
    // 图片大小
    imageSize: {
      type: [String, Number],
      default: 160,
    },
    // 描述文本
    description: {
      type: String,
      default: '当前无数据',
    },
    // 按钮文本
    buttonText: {
      type: String,
      default: '',
    },
    // 按钮类型
    buttonType: {
      type: String,
      default: 'primary', // primary, default
      validator: (value) => ['primary', 'default'].includes(value),
    },
    // 容器最小高度
    minHeight: {
      type: [String, Number],
      default: 300,
    },
    // 自定义容器样式
    customStyle: {
      type: String,
      default: '',
    },
  },
  computed: {
    // 图片路径处理
    imageSrc() {
      return this.image
    },
    // 图片样式
    imageStyle() {
      const size =
        typeof this.imageSize === 'number'
          ? `${this.imageSize}px`
          : this.imageSize
      return `width: ${size}; height: ${size};`
    },
    // 容器样式
    containerStyle() {
      const minHeight =
        typeof this.minHeight === 'number'
          ? `${this.minHeight}px`
          : this.minHeight
      return `min-height: ${minHeight}; ${this.customStyle}`
    },
    // 按钮样式类
    buttonClass() {
      return {
        'empty-button--primary': this.buttonType === 'primary',
        'empty-button--default': this.buttonType === 'default',
      }
    },
  },
  methods: {
    // 按钮点击事件
    handleButtonClick() {
      this.$emit('button-click')
    },
  },
}
</script>

<style lang="less" scoped>
@import '../../assets/css/variable.less';

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  box-sizing: border-box;
}

.empty-image-wrapper {
  margin-bottom: 24px;
}

.empty-image {
  display: block;
  margin-left: 30px;
}

.empty-text {
  margin-bottom: 24px;
  text-align: center;
}

.empty-description {
  font-size: 14px;
  line-height: 20px;
  color: #838791;
}

.empty-slot {
  margin-bottom: 24px;
}

.empty-action {
  width: 100%;
  max-width: 200px;
}

.empty-button {
  width: 100%;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;

  &--primary {
    background-color: #2c5de5;

    .empty-button-text {
      color: #ffffff;
    }

    &:active {
      background-color: #1b41bf;
    }
  }

  &--default {
    background-color: #ffffff;
    border: 1px solid #d9dadd;

    .empty-button-text {
      color: #2c5de5;
    }

    &:active {
      background-color: #f6f6f6;
    }
  }
}

.empty-button-text {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}
</style>
