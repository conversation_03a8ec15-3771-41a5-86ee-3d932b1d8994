<template>
	<view>
		<view @click="getChecked">
			<view v-for="(item,index) in chooseList" :key="item.bankId" class="radioItems">
				<view class="uni-title uni-common-mt uni-common-pl">{{item.item}}</view>
				<view class="uni-list">
					<radio-group @change="radioChange($event,item)" :data-id="index">
						<label class="uni-list-cell uni-list-cell-pd" v-for="(item, index) in item.option"
							:key="item.value">
							<view class="view">
								<radio :value="item.id" :checked="item.id === current" />
							</view>
							<view class="view">{{item.value}}</view>
						</label>
					</radio-group>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "radio-choose",
		props: {
			chooseList: {
				type: Array
			}
		},
		data() {
			return {
				current: 0,
				childList: []
			};
		},
		mounted() {
			console.log(this.chooseList,12)
		},
		// watch: {
		// 	chooseList: function(newVal, oldVal) {
		// 		// this.cData = newVal; //newVal即是chartData
		// 		// console.log(this.cData )
		// 		// this.drawChart();
		// 		console.log('line41',newVal)
		// 	}
		// },
		methods: {
			radioChange: function(evt, tag) {
				let indexNum = evt.currentTarget.dataset.id
				this.childList.length = this.chooseList.length;
				// this.childList[indexNum] = tag.bankId + evt.detail.value; //值的覆盖
				this.childList[indexNum] = {
					bankId: tag.bankId,
					answer: Number(evt.detail.value)
				}
				for (let i = 0; i < this.chooseList.length; i++) {
					let val = this.chooseList[i].option
					let ids = this.chooseList[i].bankId
					if (ids === tag.bankId) {
						// val.forEach((el,i) => {
						// 	this.current = val[i].id == evt.detail.value ? el.id : 0
						// 	console.log('line64',this.current)
						// })
					}
				}
			},
			getChecked() {
				this.$emit('getChildList', this.childList)
			}
		}
	}
</script>

<style lang="less" scoped>
	.uni-list-cell {
		display: flex;
		margin-top: 8px;
	}

	.radioItems {
		margin-bottom: 24px;
		font-family: SF Pro Text;
		font-style: normal;
		font-weight: normal;
		font-size: 14px;

		&:last-child {
			margin-bottom: 0;
		}

		.uni-title {
			line-height: 20px;
			color: #202532;
			margin-bottom: 8px;
		}

		.uni-list {
			.view {
				line-height: 20px;
				color: #50545E;

				&:first-child {
					margin-top: -2px;
				}
			}
		}
	}
</style>
