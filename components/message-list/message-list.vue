<template>
  <!-- 机长:pilot  副机长:copilot 放行员:maintenance 保障员:safetyOfficer -->
  <view class="" style="border-top:5px solid #eee"
        v-if="taskInfo.roleKey == 'pilot' || taskInfo.roleKey == 'copilot' || taskInfo.roleKey == 'maintenance' || taskInfo.roleKey == 'safetyOfficer'">
    <view class="uni-form-item uni-column">
      <view class="title">姓名:{{ taskInfo.userName }}</view>
    </view>
    <view class="" v-if="taskInfo.roleKey == 'pilot' || taskInfo.roleKey == 'copilot'">
      <view class="uni-form-item uni-column">
        <view class="title">执照有效期:</view>
        <picker :disabled='true' class="time-picker" mode="date" :value="taskInfo.validityOfLicense"
                @change="bindTimeChange($event, 1)">
          <text class="iconfont icon-calendar-todo-fill"></text>
          <view class="time" style="display: inline;">{{ taskInfo.validityOfLicense }}</view>
        </picker>
      </view>
      <view class="uni-form-item uni-column">
        <view class="title">体检合格证有效期:</view>
        <picker :disabled='true' class="time-picker" mode="date" :value="taskInfo.validityPeriodOfPhysicalExamination "
                @change="bindTimeChange($event, 2)">
          <text class="iconfont icon-calendar-todo-fill"></text>
          <view class="time" style="display: inline;">{{ taskInfo.validityPeriodOfPhysicalExamination }}</view>
        </picker>
      </view>
      <view class="uni-form-item uni-column">
        <view class="title">熟练检查基准月:</view>
        <picker :disabled='true' class="time-picker" mode="date" :value="taskInfo.proficiencyCheckStandardMonth "
                @change="bindTimeChange($event, 3)">
          <text class="iconfont icon-calendar-todo-fill"></text>
          <view class="time" style="display: inline;">{{ taskInfo.proficiencyCheckStandardMonth }}</view>
        </picker>
      </view>
      <view class="uni-form-item uni-column">
        <view class="title">熟练检查到期日:</view>
        <picker :disabled='true' class="time-picker" mode="date" :value="taskInfo.proficiencyCheckValidity "
                @change="bindTimeChange($event, 4)">
          <text class="iconfont icon-calendar-todo-fill"></text>
          <view class="time" style="display: inline;">{{ taskInfo.proficiencyCheckValidity }}</view>
        </picker>
      </view>
      <view class="uni-form-item uni-column">
        <view class="title">机长航线练检查到期日:</view>
        <picker :disabled='true' class="time-picker" mode="date"
                :value="taskInfo.validityPeriodOfRouteTrainingInspection " @change="bindTimeChange($event, 5)">
          <text class="iconfont icon-calendar-todo-fill"></text>
          <view class="time" style="display: inline;">{{ taskInfo.validityPeriodOfRouteTrainingInspection }}</view>
        </picker>
      </view>
      <view class="uni-form-item uni-column">
        <view class="title">危险品训练有效期:</view>
        <picker :disabled='true' class="time-picker" mode="date" :value="taskInfo.dangerTrainingInspection "
                @change="bindTimeChange($event, 6)">
          <text class="iconfont icon-calendar-todo-fill"></text>
          <view class="time" style="display: inline;">{{ taskInfo.dangerTrainingInspection }}</view>
        </picker>
      </view>
      <view class="uni-form-item uni-column">
        <view class="title">汉语言有效期:</view>
        <picker :disabled='true' class="time-picker" mode="date" :value="taskInfo.chineseLanguageValidity "
                @change="bindTimeChange($event, 7)">
          <text class="iconfont icon-calendar-todo-fill"></text>
          <view class="time" style="display: inline;">{{ taskInfo.chineseLanguageValidity }}</view>
        </picker>
      </view>
      <view class="uni-form-item uni-column">
        <view class="title">空勤登记证有效期:</view>
        <picker :disabled='true' class="time-picker" mode="date" :value="taskInfo.validityOfRegistrationCertificate "
                @change="bindTimeChange($event, 8)">
          <text class="iconfont icon-calendar-todo-fill"></text>
          <view class="time" style="display: inline;">{{ taskInfo.validityOfRegistrationCertificate }}</view>
        </picker>
      </view>
    </view>
    <view class="" v-if="taskInfo.roleKey == 'maintenance' || taskInfo.roleKey == 'safetyOfficer'">
      <view class="uni-form-item uni-column">
        <view class="title">空勤登记证有效期:</view>
        <picker :disabled='true' class="time-picker" mode="date" :value="taskInfo.validityOfRegistrationCertificate "
                @change="bindTimeChange($event, 8)">
          <text class="iconfont icon-calendar-todo-fill"></text>
          <view class="time" style="display: inline;">{{ taskInfo.validityOfRegistrationCertificate }}</view>
        </picker>
      </view>
      <view class="uni-form-item uni-column">
        <view class="title">Y12放行授权有效期:</view>
        <picker :disabled='true' class="time-picker" mode="date" :value="taskInfo.y12Inspection"
                @change="bindTimeChange($event, 9)">
          <text class="iconfont icon-calendar-todo-fill"></text>
          <view class="time" style="display: inline;">{{ taskInfo.y12Inspection }}</view>
        </picker>
      </view>
      <view class="uni-form-item uni-column">
        <view class="title">C208放行授权有效期:</view>
        <picker :disabled='true' class="time-picker" mode="date" :value="taskInfo.c208Inspection"
                @change="bindTimeChange($event, 10)">
          <text class="iconfont icon-calendar-todo-fill"></text>
          <view class="time" style="display: inline;">{{ taskInfo.c208Inspection }}</view>
        </picker>
      </view>

      <view class="uni-form-item uni-column">
        <view class="title">B300放行授权有效期:</view>
        <picker :disabled='true' class="time-picker" mode="date" :value="taskInfo.b300Inspection"
                @change="bindTimeChange($event, 11)">
          <text class="iconfont icon-calendar-todo-fill"></text>
          <view class="time" style="display: inline;">{{ taskInfo.b300Inspection }}</view>
        </picker>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "MessageList",
  props: {
    taskInfo: {
      type: Object,
    },
    taskIndex: {
      type: Number
    },
    types: {
      type: String
    },
    flight: {
      type: String
    },
    dateList: {
      type: Array
    }
  },
  data() {
    return {
      showMore: false,
    }
  },
  methods: {

    changeShow() {
      this.showMore = !this.showMore;
    },
    goflightDetail() {
      if (this.types == 1) {
        return false
      }
      console.log('/pages/flightPlan/flight?flightplanId=' + this.taskInfo.flightplan.flightplanId)
      uni.navigateTo({
        url: '/pages/flightPlan/flight?flightplanId=' + this.taskInfo.flightplan.flightplanId
      });

    },
    changeShow() {
      this.showMore = !this.showMore;
    },
    goFlyTest() { //飞前问答
      if (this.taskInfo.flyQaStatus === 0) {
        uni.navigateTo({
          url: '/pages/home/<USER>' + this.taskInfo.flightSortiesId
        });
      }
    },
    phoneCall(phoneNumber) {
      uni.makePhoneCall({
        phoneNumber: phoneNumber,
      })
    },
    goAirLineData() { //航线资料
      if (this.taskInfo.routeInfoStatus === 1) {
        uni.navigateTo({
          url: '/pages/home/<USER>' + this.taskInfo.flightSortiesId
        });
      }
    },
    goMeteReview() { //气象资料
      if (this.taskInfo.meteReviewStatus !== 0) {
        uni.navigateTo({
          url: '/pages/home/<USER>' + this.taskInfo.flightSortiesId
        });
      }
    },
    goEleManifest() { //电子舱单
      if (this.taskInfo.eleManifestStatus !== 0) {
        uni.navigateTo({
          url: '/pages/home/<USER>' + this.taskInfo.flightSortiesId
        });
      }
    },
    goReleaseSlip() { //放行单
      if (this.taskInfo.releasenoteStatus !== 0) {
        uni.navigateTo({
          url: '/pages/home/<USER>' + this.taskInfo.flightSortiesId
        });
      }
    },
    goFlyTime() { //飞行时刻
      uni.navigateTo({
        url: '/pages/home/<USER>' + this.taskInfo.flightSortiesId
      });
    }
  }
}
</script>

<style lang="less" scoped>

page {
  height: 100%;
}

.background {
  width: 100%;
  height: 100%;
  // position: fixed;
  background-size: 100% 100%;
  z-index: -1;
  position: absolute;
  top: 0px;
  bottom: 0px;
}

.nav {
  width: 100%;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
  z-index: 10;
}

.nav-title {
  width: 100%;
  height: inherit;
  position: relative;
  z-index: 10;
  font-family: SF Pro Text;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;

  .nav-title-text {
    // padding-top: 10px;
    font-family: SF Pro Text;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    color: #000000;

    &:first-child {
      font-weight: normal;
      position: absolute;
      left: 25px;
      font-size: 22px;
    }
  }
}

.uni-column {
  background: #ffffff86;
  line-height: 30px;
  padding: 10px 20px;
  font-size: 13px;

}

.content {
  padding: 10px;
}

textarea {
  border: 1px solid #efefef;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
}
</style>
