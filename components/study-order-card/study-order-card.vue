<template>
	<view class="warp">
		<view class="">
			<image class='plane' src="../../static/images/plane_bg.png" mode="widthFix"></image>
			<view class="main">
				<view class="header flex-row fz-12">
					<span class="index">{{taskIndex + 1}}</span>
					<span class="marRight33 white-nowrap">
						<text class="iconfont icon-seedling-fill col-gray-400 margin-right-4"></text>
						<span class="col-gray-500 marRight4" style='font-size: 13px;'>订单状态</span>
						<span class="col-gray-600">出票</span>
					</span>
					<span class="white-nowrap">
						<text class="iconfont icon-refresh-fill col-gray-400 margin-right-4"></text>
						<span class="col-gray-500 marRight4" style='font-size: 13px;'>{{taskInfo.orderDate}}</span>
						<!-- <span class="col-gray-600">{{taskInfo.taskNumber}}</span> -->
					</span>
					<!-- <view class="arrow bg-white-500" @click.stop="changeShow" style="float: right;">
						<text v-show="showMore" class="iconfont icon-arrow-up-line"><text class="fz-12 col-gray-500">关闭</text></text>
						<text v-show="!showMore" class="iconfont icon-arrow-down-line"><text class="fz-12 col-gray-500">展开</text></text>
					</view> -->
				</view>
				
			</view>

			<view class="detail-row bg-white-500 no-fold">
				<view class="detail-block">
					<p class="fz-12 col-gray-500">渠道</p>
					<p class="fz-14 col-gray-600">{{taskInfo.agentName}}</p>
				</view>
				<view class="detail-block">
					<p class="fz-12 col-gray-500">机场</p>
					<p class="fz-14 col-gray-600">{{taskInfo.airportName}}</p>
				</view>
				
			</view>
			<view class="detail-row bg-white-500 no-fold" style="padding-top: 0;">
				<view class="detail-block">
					<p class="fz-12 col-gray-500">创建时间</p>
					<p class="fz-14 col-gray-600">{{taskInfo.createTime}}</p>
				</view>
				
				<view class="detail-block">
					<p class="fz-12 col-gray-500">备注</p>
					<p class="fz-14 col-gray-600">{{taskInfo.remarks}}</p>
				</view>
			</view>

			<view class="detail-row bg-white-500 no-fold" style="padding-top: 0;">
				<view class="detail-block" @click.stop='passengerList(taskInfo)'>
					<p class="fz-12 col-gray-500">旅客列表</p>
					<p class="fz-14 col-gray-600" style="color: #4858af;">查看</p>
				</view>

			</view>



			<!-- 班次 -->
			<view class="">
				<view :class="showMore ? 'show-view':'close-view'" class="bg-white-500 fold detail-row no-fold">
					<view class="detail-block">
						<p class="fz-12 col-gray-500">通航公司</p>
						<p class="fz-14 col-gray-600">{{isNull(taskInfo.companyName)}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">机尾号</p>
						<p class="fz-14 col-gray-600">{{isNull(taskInfo.tailNumber)}}</p>
					</view>
				</view>
				<view :class="showMore ? 'show-view':'close-view'" class="bg-white-500 fold detail-row no-fold">
					<view class="detail-block">
						<p class="fz-12 col-gray-500">出发三字码</p>
						<p class="fz-14 col-gray-600">{{taskInfo.departureThreeCode}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">到达三字码</p>
						<p class="fz-14 col-gray-600">{{taskInfo.arrivedThreeCode}}</p>
					</view>
				</view>
				<view :class="showMore ? 'show-view':'close-view'" class="bg-white-500 fold detail-row">

					<view class="detail-block">
						<p class="fz-12 col-gray-500">本场计划起飞</p>
						<p class="fz-14 col-gray-600">{{taskInfo.expectedDepartureTime}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">本场计划到达</p>
						<p class="fz-14 col-gray-600">{{taskInfo.expectedArrivalTime}}</p>
					</view>

				</view>
				<view :class="showMore ? 'show-view':'close-view'" class="bg-white-500 fold detail-row">

					<view class="detail-block">
						<p class="fz-12 col-gray-500">本场变更起飞</p>
						<p class="fz-14 col-gray-600">{{isNull(taskInfo.changedExpectedDepartureTime)}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">本场变更到达</p>
						<p class="fz-14 col-gray-600">{{isNull(taskInfo.changedExpectedArrivalTime)}}</p>
					</view>

				</view>
				<view :class="showMore ? 'show-view':'close-view'" class="bg-white-500 fold detail-row">

					<view class="detail-block">
						<p class="fz-12 col-gray-500">航班状态</p>
						<p class="fz-14 col-gray-600">
							{{taskInfo.flightStatus==3?'本场起飞':taskInfo.flightStatus==1?'前方起飞':taskInfo.flightStatus==2?'本场到达':''}}
						</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">航班异常</p>
						<p class="fz-14 col-gray-600">
							{{taskInfo.abnormalStatus==3?'航班延误':taskInfo.abnormalStatus==4?'航班取消':''}}
						</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">值机状态</p>
						<p class="fz-14 col-gray-600">
							{{taskInfo.checkInStatus==1?'值机开始':taskInfo.checkInStatus==2?'值机截止':''}}
						</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">登机状态</p>
						<p class="fz-14 col-gray-600">
							{{taskInfo.boardingStatus==3?'登机开始':taskInfo.boardingStatus==4?'催促登机':taskInfo.boardingStatus==5?'登机截止':''}}
						</p>
					</view>

				</view>
			</view>


			

		</view>
	</view>
	</view>
</template>

<script>
	import {

	} from '../../api/weChat.js';
	export default {
		name: "TaskCard",
		props: {
			taskInfo: {
				type: Object,
			},
			taskIndex: {
				type: Number
			},
			types: {
				type: String
			},
			flight: {
				type: String
			},
			flightDate: {
				type: String,
			}
		},
		created() {
			this.userRole = uni.getStorageSync('userRole');
			this.userRole.includes('pilot')

		},
		mounted() {
			console.log(111111111, this.taskInfo)
		},
		data() {
			return {
				userRole: "",
				showMore: false,
				value: '',
				index: 0,
				showFlag: false,
			}
		},
		methods: {
			isNull(e) {
				return e == null ? '' : e
			},
			bindPickerChange(e) {
				console.log(e)
				this.index = e.detail.value
			},
			changeShow() {
				this.showMore = !this.showMore;
			},
			
			passengerList() { //旅客列表
				uni.navigateTo({
					url: '/pages/study/order/detail?orderNo=' + this.taskInfo.orderNo
				});
			}
		}
	}
</script>

<style lang="less" scoped>
	.warp {
		position: relative;
		overflow: hidden;
		background-color: rgba(255, 255, 255, 0.64);
		border: 1px solid rgba(255, 255, 255, 0.72);
		border-radius: 8px;
		margin-bottom: 12px;
	}

	.plane {
		width: 190px;
		position: absolute;
		top: 0;
		right: 0;
	}

	.main {
		padding: 24rpx 0rpx 0 32rpx;
		height: 45px;
		background-color: rgba(0, 0, 0, 0);

		.header {
			// padding-right: 84rpx;
			height: 16px;

			.index {
				width: 16px;
				height: 16px;
				line-height: 16px;
				text-align: center;
				margin-right: 13px;
				border-radius: 50%;
				color: #838791;
				background-color: #EAEAEB;
				display: inline-block;
				font-size: 12px;
			}

			.marRight4 {
				margin-right: 8rpx;
			}

			.marRight33 {
				margin-right: 10rpx;
			}
		}

		.trip-address {
			margin-top: 16px;
			margin-bottom: 12px;

			.fz-16 {
				font-weight: 800;
			}
		}

		.trip-bar {
			width: 16px;
			height: 104px;
			margin-right: 12px;
		}

		.show-view {
			height: 330px;
			transition: .5s;
			backdrop-filter: blur(16px);
		}

		.close-view {
			height: 0;
			transition: .5s;
			overflow: hidden;
		}
	}

	.detail-row {
		padding: 12px 16px;
		display: flex;
		width: 100%;
		backdrop-filter: blur(16px);
		justify-content: space-between;
	}

	.show-view {
		height: 50px;
		transition: .5s;
		backdrop-filter: blur(16px);
	}

	.close-view {
		height: 0;
		transition: .5s;
		overflow: hidden;
	}

	.detail-block {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: top;
		align-items: center;
		position: relative;

		.phone {
			position: absolute;
			bottom: -17px;
			font-size: 12px;
			color: #2C5DE5;
		}

		.icon-phone-fill {
			font-size: 12px !important;
		}

		.fz-14 {
			font-weight: 600;
		}
	}

	//机型机号等样式调整
	.no-fold {
		padding-bottom: 15px;
	}

	.fold {
		padding: 0 16px;

		.detail-row {
			padding: 0;

			&:not(:last-child) {
				margin-bottom: 12px;
			}

			&:last-child {
				padding-top: 12px;
			}

			.fz-14 {
				margin-top: 4px;
			}

			&:nth-of-type(2) {
				.fz-14 {
					margin-bottom: 4px;
				}
			}
		}
	}

	.handel-section {
		width: 100%;
		padding: 12px 0;
		display: flex;
		flex-wrap: wrap;

		.handle-block {
			padding: 8px 10.5px;
			width: calc(50% - 8px);
			height: 60px;
			border: 1px solid #D9DADD;
			border-radius: 4px;
			display: flex;
			margin-bottom: 12px;
		}

		.handle-block:nth-child(odd) {
			margin-right: 16px;
		}

		.status {
			height: 20px;
			line-height: 20px;
			text-align: center;
			border-radius: 50px;
			width: fit-content;
			padding: 0 8px;
		}

		.complete {
			//已完成的状态
			background: #E9FBF1;
			color: #5BBC72;
			border: 1px solid #D6F3E2;
		}

		.incomplete {
			//未完成的状态
			background: #F6F6F6;
			color: #50545E;
			border: 1px solid #D9DADD;
		}

		.refuse {
			//已拒绝状态
			background: #FFEFF0;
			color: #E83F4E;
			border: 1px solid #FFD6D7;
		}
	}

	.arrow {
		height: 20px;
		text-align: center;
		backdrop-filter: blur(16px);
	}

	.show-view.stories {
		height: 30px;
		font-weight: 600;
		border-top: 1px solid #efefef;
	}

	.show-view.titles {
		height: 50px !important;
		line-height: 40px !important;
	}
</style>