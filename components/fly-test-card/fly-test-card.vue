<template>
	<view class="warp">
		<image class='plane' src="../../static/images/plane_bg.png" mode="widthFix"></image>
		<view class="main">
			<view class="header flex-row fz-12">
				<span class="index">{{taskIndex + 1}}</span>
				<span class="marRight33 white-nowrap">
					<text class="iconfont icon-seedling-fill col-gray-400 margin-right-4"></text>
					<span class="col-gray-500 marRight4">任务性质</span>
					<span class="col-gray-600">{{taskInfo.flightPurpose}}</span>
				</span>
				<span class="white-nowrap">
					<text class="iconfont icon-refresh-fill col-gray-400 margin-right-4"></text>
					<span class="col-gray-500 marRight4">运行</span>
					<span class="col-gray-600">{{taskInfo.runNo}}</span>
				</span>
			</view>
			<view class="trip-address flex-row">
				<image class='trip-bar' src="../../static/images/tripBar.png"></image>
				<view class="trip flex-col justify-around">
					<view>
						<p class="fz-16 col-gray-700">{{taskInfo.departCity}}</p>
						<p class="fz-12 col-gray-500">{{taskInfo.flightDate +'  '+ taskInfo.planDepartTime}}</p>
					</view>
					<view>
						<p class="fz-16 col-gray-700">{{taskInfo.arriveCity}}</p>
						<p class="fz-12 col-gray-500">{{taskInfo.planArriveTime}}</p>
					</view>
				</view>
				<span
					v-if="taskInfo.flightSortiesStatus">{{taskInfo.flightSortiesStatus==1?'计划中':taskInfo.flightSortiesStatus==2?'已执行':taskInfo.flightSortiesStatus==3?'已取消':'默认'}}</span>
			</view>
		</view>
		<!-- <view class="detail-row bg-white-500 no-fold">
			<view class="detail-block">
				<p class="fz-12 col-gray-500">机型</p>
				<p class="fz-14 col-gray-600">C208</p>
			</view>
			<view class="detail-block">
				<p class="fz-12 col-gray-500">机号</p>
				<p class="fz-14 col-gray-600">B982B</p>
			</view>
			<view class="detail-block">
				<p class="fz-12 col-gray-500">呼号</p>
				<p class="fz-14 col-gray-600">B9829</p>
			</view>
		</view> -->
		<view class="bg-white-500 fold">
			<view class="detail-row" style="padding-top: 10px;">
				<view class="detail-block">
					<p class="fz-12 col-gray-500">机型</p>
					<p class="fz-14 col-gray-600">{{taskInfo.aircraft.aircraftStyle}}</p>
				</view>
				<view class="detail-block">
					<p class="fz-12 col-gray-500">机号</p>
					<p class="fz-14 col-gray-600">{{taskInfo.aircraft.aircraftTailNo}}</p>
				</view>
				<view class="detail-block">
					<p class="fz-12 col-gray-500">呼号</p>
					<p class="fz-14 col-gray-600">{{taskInfo.callSign}}</p>
				</view>
			</view>
			<view class="bg-white-500">
				<view class="detail-row">
					<view class="detail-block">
						<p class="fz-12 col-gray-500">机长</p>
						<p class="fz-14 col-gray-600">{{taskInfo.captain}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">副驾驶</p>
						<p class="fz-14 col-gray-600">{{taskInfo.copilot}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">放行员</p>
						<p class="fz-14 col-gray-600">{{taskInfo.maintenance}}</p>
					</view>
					<!-- <view class="detail-block">
						<p class="fz-12 col-gray-500">载客数</p>
						<p class="fz-14 col-gray-600">{{taskInfo.seatNum}}</p>
					</view> -->
				</view>
				<view class="detail-row">
					<view class="detail-block">
						<p class="fz-12 col-gray-500">责任运控值班</p>
						<p class="fz-14 col-gray-600">{{taskInfo.ocUser}}</p>
						<p class="col-line phone" @click="phoneCall(taskInfo.ocUserPhoneNumber)">
							<text class="iconfont icon-phone-fill margin-right-4"></text>
							{{taskInfo.ocUserPhoneNumber}}
						</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">总值班领导</p>
						<p class="fz-14 col-gray-600">{{taskInfo.generalleader}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">值班领导</p>
						<p class="fz-14 col-gray-600">{{taskInfo.leader}}</p>
					</view>
				</view>
				<view class="detail-row">
					<view class="detail-block">
						<p class="fz-12 col-gray-500">载客数</p>
						<p class="fz-14 col-gray-600">{{taskInfo.seatNum}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">油耗</p>
						<p class="fz-14 col-gray-600">{{taskInfo.fuel}}L</p>
					</view>
					<view class="detail-block"></view>
				</view>
			</view>
		</view>
	</view>
	</view>
</template>

<script>
	export default {
		name: "TaskCard",
		props: {
			taskInfo: {
				type: Object,
			},
			taskIndex: {
				type: Number
			}
		},
		data() {
			return {
				// iList: [] //组件的内容显示判断所需
			}
		},
		mounted() {},
		methods: {
			goFlyTest() {
				uni.navigateTo({
					url: '/pages/home/<USER>'
				});
			},
			//详情显示状态与否
			handleStatus() {
				console.log(this.taskInfo);
			},
			// 打电话
			phoneCall(phoneNumber){
				uni.makePhoneCall({
				      phoneNumber: phoneNumber,
				    })
			},
		}
	}
</script>

<style lang="less" scoped>
	.fz-14 {
		font-size: 14px;
		font-weight: 600;
		margin-top: 2px;
	}

	.fz-16 {
		font-size: 16px;
		font-weight: 800;
		line-height: 24px;
		margin-bottom: 2px;
	}

	.warp {
		position: relative;
		overflow: hidden;
		background-color: rgba(255, 255, 255, 0.64);
		border: 1px solid rgba(255, 255, 255, 0.72);
		border-radius: 8px;
		margin-bottom: 12px;
	}

	.plane {
		width: 190px;
		position: absolute;
		top: 0;
		right: 0;
		// z-index: -1;
	}

	.main {
		padding: 24rpx 32rpx 0 32rpx;
		height: 160px;
		background-color: rgba(0, 0, 0, 0);

		.header {
			padding-right: 84rpx;
			height: 16px;

			.index {
				text-align: center;
				padding: 5px 5px;
				display: flex;
				align-items: center;
				justify-content: center;
				background: #EAEAEB;
				border-radius: 40px;
				margin-right: 13px;
				font-weight: 600;
				font-size: 10px;
				color: #838791;
			}

			.marRight4 {
				margin-right: 8rpx;
			}

			.marRight33 {
				margin-right: 66rpx;
			}
		}

		.trip-address {
			margin-top: 16px;
			margin-bottom: 12px;

			span {
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				width: 46px;
				height: 20px;
				background: #EE843E;
				border-radius: 59px;
				// margin: 0px 8px;
				font-size: 10px;
				color: #FFFFFF;
				margin-top: 2px;
			}
		}

		.trip-bar {
			width: 16px;
			height: 104px;
			margin-right: 12px;
		}

		.trip {

			// height: 104px;
			>view {
				&:first-child {
					margin-bottom: 20px;
				}
			}

		}
	}

	.detail-row {
		padding: 12px 16px;
		display: flex;
		width: 100%;
		justify-content: space-between;
	}

	.bg-white-500 {
		z-index: 9;
		background: rgba(255, 255, 255, 0.64);
		backdrop-filter: blur(16px);
		margin-top: -2px;

		&:nth-of-type(2) {
			.bg-white-500 {
				background: none;
			}
		}
	}


	.detail-block {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: top;
		align-items: center;
		position: relative;

		.phone {
			position: absolute;
			bottom: -17px;
			font-size: 12px;
			color: #2C5DE5;
		}

		.icon-phone-fill {
			font-size: 12px !important;
		}
	}

	//机型机号等样式调整
	.no-fold {
		padding-bottom: 12px;
	}

	.fold {
		padding: 0 16px 12px;

		.detail-row {
			padding: 0;

			&:not(:last-child) {
				margin-bottom: 14px;
			}

			&:last-child {
				padding-top: 14px;
			}

			.fz-14 {
				margin-top: 4px;
			}

			&:nth-of-type(2) {
				.fz-14 {
					margin-bottom: 4px;
				}
			}
		}
	}


	.handel-section {
		width: 100%;
		padding: 12px 16px;
		display: flex;
		flex-wrap: wrap;

		.handle-block {
			padding: 8px 10.5px;
			width: calc(50% - 8px);
			height: 60px;
			border: 1px solid #D9DADD;
			border-radius: 4px;
			display: flex;
			margin-bottom: 12px;
		}

		.handle-block:nth-child(odd) {
			margin-right: 16px;
		}

		.status {
			height: 20px;
			line-height: 20px;
			text-align: center;
			border-radius: 50px;
			width: fit-content;
			padding: 0 8px;
		}

		.complete {
			//已完成的状态
			background: #E9FBF1;
			color: #5BBC72;
			border: 1px solid #D6F3E2;
		}

		.incomplete {
			//未完成的状态
			background: #F6F6F6;
			color: #50545E;
			border: 1px solid #D9DADD;
		}

		.refuse {
			//已拒绝状态
			background: #FFEFF0;
			color: #E83F4E;
			border: 1px solid #FFD6D7;
		}
	}

	.arrow {
		height: 20px;
		text-align: center;
	}
</style>
