<template>
	<view class="warp">
		<view class="">
			<!-- <image class='plane' src="../../static/images/plane_bg.png" mode="widthFix"></image> -->
			<view class="main">
				<view class="header flex-row fz-12">
					<span class="index">{{taskIndex + 1}}</span>
					<span class="marRight33 white-nowrap">
						<text class="iconfont icon-seedling-fill col-gray-400 margin-right-4"></text>
						<span class="col-gray-500 marRight4" style='font-size: 13px;'>客票状态</span>
						<span class="col-gray-600">出票</span>
					</span>
					<span class="white-nowrap">
						<text class="iconfont icon-refresh-fill col-gray-400 margin-right-4"></text>
						<span class="col-gray-500 marRight4" style='font-size: 13px;'>添加时间</span>
						<span class="col-gray-600">{{taskInfo.createTime}}</span>
					</span>
					<span class="white-nowrap">
						<text class="  col-gray-400 margin-right-4"></text>
						<span class="col-gray-500 marRight4" style='font-size: 13px;color: #4858af;z-index: 1000;'
							@click="delPassenger">删除</span>
					</span>
				</view>
				
			</view>

			<view class="detail-row bg-white-500 no-fold" style="padding-bottom: 5px;">
				<view class="detail-block">
					<view class="detail-block">
						<p class="fz-12 col-gray-500">姓名</p>
						<p class="fz-14 col-gray-600">{{taskInfo.name}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">证件号</p>
						<p class="fz-14 col-gray-600">{{taskInfo.idNo}}</p>
					</view>
					<view class="detail-block">
						<p class="fz-12 col-gray-500">手机号</p>
						<p class="fz-14 col-gray-600">{{taskInfo.phone}}</p>
					</view>
				</view>
				<view class="detail-block" style="align-items: flex-end;">
					<image :src="taskInfo.photo" style="width:200rpx;height:200rpx;"></image>
					
				</view>
			</view>
			<view class="detail-row bg-white-500 no-fold" style="padding-top: 0;">
				<view class="detail-block">
					<p class="fz-12 col-gray-500">备注</p>
					<p class="fz-14 col-gray-600">{{taskInfo.remarks==null?'':taskInfo.remarks}}</p>
				</view>
			</view>



		</view>
	</view>
	</view>
</template>

<script>
	import {
		httpify
	} from 'caseless';
	import {
		studyOrderPassengerDel
	} from '../../api/weChat.js';
	export default {
		name: "TaskCard",
		props: {
			taskInfo: {
				type: Object,
			},
			taskIndex: {
				type: Number
			},
			types: {
				type: String
			},
			flight: {
				type: String
			},
			flightDate: {
				type: String,
			}
		},
		created() {
			this.userRole = uni.getStorageSync('userRole');
			this.userRole.includes('pilot')

		},
		mounted() {
			// console.log(111111111, this.taskInfo)
			// console.log(this.httpImg + this.taskInfo.photo)
		},
		data() {
			return {
				userRole: "",
				showMore: false,
				value: '',
				index: 0,
				showFlag: false,
			}
		},
		methods: {

			async del() {
				let that = this
				let param = {
					orderNo: this.taskInfo.orderNo,
					ticketNo: this.taskInfo.ticketNo
				}
				console.log(param)
				const res = await studyOrderPassengerDel(param);
				console.log(res)
				if (res.response.code == 200 && res.response.data == true) {
					uni.showToast({
						title: "取消成功"
					})
					setTimeout(function() {
						that.$emit('custom-event')
					}, 500)

				} else {
					uni.showModal({
						content: "取消失败"
					})
				}
			},

			delPassenger() {
				let that = this
				uni.showModal({
					title: '提示',
					content: '确认删除旅客吗？',
					confirmText: "确定",
					cancelText: "取消",
					success: function(res) {
						if (res.confirm) {
							that.del()
						}
					}
				})

			},
			isNull(e) {
				return e == null ? '' : e
			},
			bindPickerChange(e) {
				console.log(e)
				this.index = e.detail.value
			},
			changeShow() {
				this.showMore = !this.showMore;
			},


		}
	}
</script>

<style lang="less" scoped>
	.warp {
		position: relative;
		overflow: hidden;
		background-color: rgba(255, 255, 255, 0.64);
		border: 1px solid rgba(255, 255, 255, 0.72);
		border-radius: 8px;
		margin-bottom: 12px;
	}

	.plane {
		width: 190px;
		position: absolute;
		top: 0;
		right: 0;
	}

	.main {
		padding: 24rpx 0rpx 0 10rpx;
		height: 45px;
		background-color: rgba(0, 0, 0, 0);

		.header {
			// padding-right: 84rpx;
			height: 16px;

			.index {
				width: 16px;
				height: 16px;
				line-height: 16px;
				text-align: center;
				margin-right: 13px;
				border-radius: 50%;
				color: #838791;
				background-color: #EAEAEB;
				display: inline-block;
				font-size: 12px;
			}

			.marRight4 {
				margin-right: 8rpx;
			}

			.marRight33 {
				margin-right: 10rpx;
			}
		}

		.trip-address {
			margin-top: 16px;
			margin-bottom: 12px;

			.fz-16 {
				font-weight: 800;
			}
		}

		.trip-bar {
			width: 16px;
			height: 104px;
			margin-right: 12px;
		}

		.show-view {
			height: 330px;
			transition: .5s;
			backdrop-filter: blur(16px);
		}

		.close-view {
			height: 0;
			transition: .5s;
			overflow: hidden;
		}
	}

	.detail-row {
		padding: 12px 16px;
		display: flex;
		width: 100%;
		backdrop-filter: blur(16px);
		justify-content: space-between;
	}

	.show-view {
		height: 50px;
		transition: .5s;
		backdrop-filter: blur(16px);
	}

	.close-view {
		height: 0;
		transition: .5s;
		overflow: hidden;
	}

	.detail-block {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: top;
		align-items: left;
		position: relative;
		padding-bottom: 10px;

		.phone {
			position: absolute;
			bottom: -17px;
			font-size: 12px;
			color: #2C5DE5;
		}

		.icon-phone-fill {
			font-size: 12px !important;
		}

		.fz-14 {
			font-weight: 600;
		}
	}

	//机型机号等样式调整
	.no-fold {
		padding-bottom: 15px;
	}

	.fold {
		padding: 0 16px;

		.detail-row {
			padding: 0;

			&:not(:last-child) {
				margin-bottom: 12px;
			}

			&:last-child {
				padding-top: 12px;
			}

			.fz-14 {
				margin-top: 4px;
			}

			&:nth-of-type(2) {
				.fz-14 {
					margin-bottom: 4px;
				}
			}
		}
	}

	.handel-section {
		width: 100%;
		padding: 12px 0;
		display: flex;
		flex-wrap: wrap;

		.handle-block {
			padding: 8px 10.5px;
			width: calc(50% - 8px);
			height: 60px;
			border: 1px solid #D9DADD;
			border-radius: 4px;
			display: flex;
			margin-bottom: 12px;
		}

		.handle-block:nth-child(odd) {
			margin-right: 16px;
		}

		.status {
			height: 20px;
			line-height: 20px;
			text-align: center;
			border-radius: 50px;
			width: fit-content;
			padding: 0 8px;
		}

		.complete {
			//已完成的状态
			background: #E9FBF1;
			color: #5BBC72;
			border: 1px solid #D6F3E2;
		}

		.incomplete {
			//未完成的状态
			background: #F6F6F6;
			color: #50545E;
			border: 1px solid #D9DADD;
		}

		.refuse {
			//已拒绝状态
			background: #FFEFF0;
			color: #E83F4E;
			border: 1px solid #FFD6D7;
		}
	}

	.arrow {
		height: 20px;
		text-align: center;
		backdrop-filter: blur(16px);
	}

	.show-view.stories {
		height: 30px;
		font-weight: 600;
		border-top: 1px solid #efefef;
	}

	.show-view.titles {
		height: 50px !important;
		line-height: 40px !important;
	}
</style>