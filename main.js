// #ifndef VUE3
import Vue from 'vue'
import App from './App'

Vue.config.productionTip = false
const netbaseurl = "https://ga.swcares.com.cn/trade/oc/wechat";
// const baseUrl = "http://*************:9907";  //服务器地址
// const netbaseurl = "http://**************:9907" //本地测试地址陈林
// const baseUrl = "https://ncov.natappvip.cc" //穿透地址
App.mpType = 'app'

function getDate() {
	//获取当前日期
	var a = new Date()
	return (a.getFullYear() + "-" + formatNumber(a.getMonth() + 1) + "-" + formatNumber(a.getDate()));
}
const formatNumber = n => {
	n = n.toString()
	return n[1] ? n : '0' + n
}

function getDateTimeByString(date) {
	if (date == null || date == "") {
		return ""
	}
	//获取当前日期
	var a = new Date(date)
	return (a.getFullYear() + "-" + formatNumber(a.getMonth() + 1) + "-" + formatNumber(a.getDate()) + " " +
		formatNumber(a.getHours()) + ":" + formatNumber(a.getMinutes()) + ":" + formatNumber(a.getSeconds()));
}

//实名认证模块 人脸图片地址前缀
let images = 'https://ga.swcares.com.cn/sale/'
Vue.prototype.$util = {
	formatNumber,
	getDate,
	getDateTimeByString,
	images
}




const app = new Vue({
	...App
})
app.$mount()
// #endif

// #ifdef VUE3
import {
	createSSRApp
} from 'vue'
import App from './App.vue'
export function createApp() {
	const app = createSSRApp(App)
	return {
		app,
	}
}
// #endif